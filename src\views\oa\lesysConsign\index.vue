<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysConsignSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysConsignTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysConsignTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="LesysConsign"
      row-key="lcId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <!--el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column-->

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="lesysConsignEdit">
      <lesysConsignForm
        ref="lesysConsignForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysConsignQuerySearch">
      <lesysConsignQuery
        ref="lesysConsignQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >


  </div>
</template>

<script>
  import { lesysConsignDoDeleteELog,
           lesysConsignGetList,
           lesysConsignDoSave,
           lesysConsignDoExport } from '@/api/oa/lesysConsign'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysConsignSearch from './components/Search.vue'
  import lesysConsignForm from './components/Form.vue'
  import lesysConsignQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapActions,mapGetters } from 'vuex'
  import { toLoginRoute } from '@/utils/routes'

  export default {
    name: 'lesysConsign',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysConsignSearch,
      lesysConsignForm,
      lesysConsignQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {lcFromOename:'',lcToOename:'',lcOename:''},
        queryForm: {lcFromOename:'',lcToOename:'',lcOename:''},
        form: {lcPid:'',lcFromLuid:'',lcFromOename:'',lcToLuid:'',lcToOename:'',lcLuid:'',lcOename:'',lcState:'',lcTotime:'',lcRvtime:''},
        rules: {

        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['委托人','受托人','操作人','状态','委托时间','取回时间'],
        columns: [
                { prop:'lcFromOename', label:'委托人', width:'auto' , sortable:true  },
                { prop:'lcToOename', label:'受托人', width:'auto' , sortable:true  },
                { prop:'lcOename', label:'操作人', width:'auto' , sortable:true  },
                { prop:'lcState', label:'状态', width:'auto' , sortable:true  },
                { prop:'lcTotime', label:'委托时间', width:'auto' , sortable:true  },
                { prop:'lcRvtime', label:'取回时间', width:'auto' , sortable:true  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          lcFromOename:'',
          lcFromLuid:'',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.searchForm.lcFromLuid = this.loginUser.luId
      this.fetchData()
    },
    methods: {
      ...mapActions({
        _logout: 'user/logout',
      }),
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        
      },
      
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        
      },
      async sure(){
        
      },
      async dbsure(row){
        
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['lesysConsignEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysConsignQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysConsignQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysConsignQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysConsignGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"lcTotime":{"celltype":"date"},"lcRvtime":{"celltype":"date"}},
                      "fileName":"委托表.xls",
                      "isnumber":true,
                      "excelTitle":"委托表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#LesysConsign",params)
        const { msg }  =  await lesysConsignDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"委托表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#LesysConsign",params)
        const { msg }  =  await lesysConsignDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>