import request from '@/utils/request'

//列表分页加载数据
export function busiDisplayFieldGetList(params) {
  return request({
    url: '/busi-display-field/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function busiDisplayFieldDoSave(data) {
  return request({
    url: '/busi-display-field/save',
    method: 'post',
    data,
  })
}
export function busiDisplayFieldDoSaves(data) {
  return request({
    url: '/busi-display-field/saves',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiDisplayFieldDoUpdate(data) {
  return request({
    url: '/busi-display-field/update',
    method: 'post',
    data,
  })
}
export function busiDisplayFieldDoUpdateOrder(data) {
  return request({
    url: '/busi-display-field/updateOrder',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function busiDisplayFieldDoSaveOrUpd(data) {
  return request({
    url: '/busi-display-field/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function busiDisplayFieldDoSaveOrUpdLog(data) {
  return request({
    url: '/busi-display-field/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiDisplayFieldDoDelete(data) {
  return request({
    url: '/busi-display-field/delete',
    method: 'post',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function busiDisplayFieldDoDeleteLog(data) {
  return request({
    url: '/busi-display-field/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function busiDisplayFieldDoDeleteELog(data) {
  return request({
    url: '/busi-display-field/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function busiDisplayFieldGetTreeList(params){
  return request({
    url: '/busi-display-field/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function busiDisplayFieldDoExport(data) {
  return request({
    url: '/busi-display-field/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function busiDisplayFieldGetStat(params) {
  return request({
    url: '/busi-display-field/vStat',
    method: 'get',
    params,
  })
}
