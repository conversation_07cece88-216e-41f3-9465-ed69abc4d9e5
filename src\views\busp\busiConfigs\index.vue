<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiConfigsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="busiConfigsTs"
      @handleAdd="handleAdd"/>

    <el-table
      ref="busiConfigsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="busiConfigs"
      row-key="bcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="200"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
          &nbsp;
          <el-dropdown trigger="click" @command="handleCommand">
            <el-button type="text">
              配置<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="list"  icon="el-icon-top">列表配置</el-dropdown-item>
              <el-dropdown-item command="form"  icon="el-icon-bottom">表单配置</el-dropdown-item>
              <el-dropdown-item command="sech"  icon="el-icon-zoom-in">查询配置</el-dropdown-item>
              <el-dropdown-item command="reds"  icon="el-icon-remove-outline">更新Redis</el-dropdown-item>
              <el-dropdown-item command="sign"  icon="el-icon-search">查看标识</el-dropdown-item>
              
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <table-edit ref="busiConfigsEdit">
      <busiConfigsForm
        ref="busiConfigsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigListSel" :fullscreen="true">
      <busiFieldConfigList
        ref="busiFieldConfigList"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigListclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigFormSel" :fullscreen="true">
      <busiFieldConfigForm
        ref="busiFieldConfigForm"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigFormclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigSearchSel" :fullscreen="true">
      <busiFieldConfigSearch
        ref="busiFieldConfigSearch"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigSearchclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >


  </div>
</template>

<script>
  import { busiConfigsDoDelete,
           busiConfigsGetData,
           busiConfigsDoSaveOrUpd,busiConfigsDoCopy } from '@/api/busp/busiConfigs'

  import { loadModelToRedis } from '@/api/busp/busiModel'         
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiConfigsSearch from './components/Search.vue'
  import busiConfigsForm from './components/Form.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import busiField from '@/views/busp/busiField/index.vue'
  import busiFieldSure from '@/views/busp/busiField/indexSure.vue'
  import busiFieldConfigList from '@/views/busp/busiFieldConfig/index_list.vue'
  import busiFieldConfigForm from '@/views/busp/busiFieldConfig/index_form.vue'
  import busiFieldConfigSearch from '@/views/busp/busiFieldConfig/index_search.vue'
  import { Loading } from 'element-ui'
  export default {
    name: 'busiConfigs',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiConfigsSearch,
      busiConfigsForm,
      busiField,
      busiFieldSure,
      busiFieldConfigList,
      busiFieldConfigForm,
      busiFieldConfigSearch
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          bcName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ],
          bcCode: [
            { required: true, message: '请输入编码', trigger: 'blur' }
          ],
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['名称','编码'],
        columns: [
                { prop:'bcName'   , label:'名称'  , width:'auto' , sortable:false  },
                { prop:'bcCode'   , label:'编码'  , width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        row: '',
        searchForm: {
          bcBmId:'',
          sortField:'',
          sortOrder:''
        },
        bmModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      init(row){
        this.bmModelRow = row
        this.searchForm.bcBmId = row.bmId
        this.fetchData()
      },
      count(index) {
        return index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiConfigsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.bmId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const regex1 = /^[a-zA-Z0-9]+$/
            if(!this.form.bcCode){
              if(!regex1.test(this.form.bcCode)){
                this.$message({message:'输入的编码错误，请输入英文、数字!',type:'warning'})
                return
              }
            }

            let msg  = {}
            if(this.editType=='copy'){
              msg = await busiConfigsDoCopy( this.form )
            }else{
              msg = await busiConfigsDoSaveOrUpd( this.form )
            }
            
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:msg.msg||'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiConfigsEdit.close()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      async handleAdd(command) {
        if(command == 'add'){
          this.form={'bcBmId':this.bmModelRow.bmId}
          this.editType = 'add'
          this.$refs['busiConfigsEdit'].showEdit('添加模型')
        }
        else if(command == 'cop'){
          if(this.row && this.row.bcId){
            this.form = Object.assign({},this.row)
            this.editType = 'copy'
            this.form.bcCode = ''
            this.$refs['busiConfigsEdit'].showEdit('复制')
          }
        }
        else if(command == 'listset'){
          this.$refs['busiFieldConfigListSel'].showEdit('列表配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigList.init(this.row)
          })
        }
        else if(command == 'formset'){
          this.$refs['busiFieldConfigFormSel'].showEdit('表单配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigForm.init(this.row)
          })
        }
        else if(command == 'searchs'){
          this.$refs['busiFieldConfigSearchSel'].showEdit('查询配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigSearch.init(this.row)
          })
        }
      },

      handleCommand(command){
        this.bmModelRow.bfcBcId = this.row.bcId
        if(command=='list'){
          this.$refs['busiFieldConfigListSel'].showEdit('列表配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigList.init(this.bmModelRow)
          })
        }else if(command=='form'){
          this.$refs['busiFieldConfigFormSel'].showEdit('表单配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigForm.init(this.bmModelRow)
          })
        }else if(command == 'sech'){
          this.$refs['busiFieldConfigSearchSel'].showEdit('查询配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigSearch.init(this.bmModelRow)
          })
        }else if(command == 'reds'){
          this.handleRedis(this.bmModelRow)
        }else if(command == 'sign'){
          this.$prompt('',{
            showCancelButton: false,
            inputValue:this.row.bcSign
          }).then(({ value }) => {
            
          }).catch(() => {      
          })
        }
      },
      busiFieldConfigListclose(){
        this.$refs.busiFieldConfigListSel.close()
      },
      busiFieldConfigFormclose(){
        this.$refs.busiFieldConfigFormSel.close()
      },
      busiFieldConfigSearchclose(){
        this.$refs.busiFieldConfigSearchSel.close()
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['busiConfigsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.bcId) {
          if(this.bmModelRow.bmCode==row.bcCode){
            this.$message({message:'默认配置不允许删除!',type:'warning'})
            return
          }
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await busiConfigsDoDelete(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      handleRedis(row) {
        this.$baseConfirm('确定更新Redis模型配置', null, async () => {
          let rr = Object.assign({},this.bmModelRow)
          rr.bcSign = row.bcSign
          rr.bcId   = row.bcId
          const msg = await loadModelToRedis(rr)
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiConfigsGetData(this.searchForm)
        this.list = data.data
        this.listLoading = false
      }
    },
  }
</script>