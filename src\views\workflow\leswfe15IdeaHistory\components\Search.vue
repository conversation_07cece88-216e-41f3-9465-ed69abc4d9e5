<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input placeholder="审批词" v-model="tableQueryForm.ideaText"  @keyup.enter.native="handleSearch" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
              添加
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-dropdown @command="handleExportRear">
          <el-button type="primary">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-download"   command="page">导出当前数据</el-dropdown-item>
            <el-dropdown-item icon="el-icon-download"   command="allp">导出全部数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'
  export default {
    name: 'leswfe15IdeaHistorySearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm);
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd');
      },
      // 导出
      handleExportRear(command) {
        this.$emit("handleExportRear",command)
      }
    },
  }
</script>