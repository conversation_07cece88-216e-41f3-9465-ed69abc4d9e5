<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiSortsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="busiSortsTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

    <el-table
      ref="busiSortsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="busiSorts"
      row-key="busiId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

    <table-edit ref="busiSortsEdit">
      <busiSortsForm
        ref="busiSortsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

  </div>
</template>

<script>
  import { busiSortsDoDelete,
           busiSortsGetList,
           busiSortsDoSaveOrUpd } from '@/api/busp/busiSorts'
  import TableEdit from '@/views/common/TableEdit.vue'
  import busiSortsSearch from './components/Search.vue'
  import busiSortsForm from './components/Form.vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'busiSorts',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      busiSortsSearch,
      busiSortsForm
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {busiIndx:'',busiName:''},
        queryForm: {busiIndx:'',busiName:''},
        form: {busiIndx:'',busiName:''},
        rules: {
          busiIndx: [
            { required: true, message: '请输入顺序', trigger: 'blur' }
          ],
          busiName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['名称','顺序'],
        columns: [
                { prop:'busiName'  , label:'名称'  , width:'auto' , sortable:false  },
                { prop:'busiIndx'  , label:'顺序'  , width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        row: '',
        searchForm: {
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiSortsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await busiSortsDoSaveOrUpd( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiSortsEdit.close()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.$refs['busiSortsEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['busiSortsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await busiSortsDoDelete({id:row.busiId})
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
        })
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiSortsGetList(this.searchForm)
        this.list = data.data
        this.listLoading = false
      }
    },
  }
</script>