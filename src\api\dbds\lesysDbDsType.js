import request from '@/utils/request'

//列表分页加载数据
export function lesysDbDsTypeGetList(params) {
  return request({
    url: '/lesys-db-ds-type/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbDsTypeDoSave(data) {
  return request({
    url: '/lesys-db-ds-type/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbDsTypeDoUpdate(data) {
  return request({
    url: '/lesys-db-ds-type/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbDsTypeDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-ds-type/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbDsTypeDoDelete(data) {
  return request({
    url: '/lesys-db-ds-type/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbDsTypeDoExport(data) {
  return request({
    url: '/lesys-db-ds-type/vExport',
    method: 'post',
    data,
  })
}
