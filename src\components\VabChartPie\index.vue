<template>
  <div class="vab-chart-pie-container">
    <vab-chart
      :init-options="initOptions"
      :option="option"
      theme="vab-echarts-theme"
    />
  </div>
</template>

<script>
  import Vab<PERSON>hart from '../VabChart'

  export default {
    name: 'Vab<PERSON>hart<PERSON><PERSON>',
    components: {
      VabChart,
    },
    props: {
      title: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        initOptions: {
          renderer: 'svg',
        },
        option: {
          grid: {
            top: 20,
            right: 20,
            bottom: 40,
            left: 40,
          },
          tooltip: {
            trigger: 'item',
          },
          series: [
            {
              name: '访问来源',
              type: 'pie',
              radius: ['40%', '80%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: 1048,
                  name: '搜索引擎',
                },
                {
                  value: 735,
                  name: '直接访问',
                },
                {
                  value: 580,
                  name: '邮件营销',
                },
                {
                  value: 484,
                  name: '联盟广告',
                },
                {
                  value: 300,
                  name: '视频广告',
                },
              ],
            },
          ],
        },
      }
    },
  }
</script>
