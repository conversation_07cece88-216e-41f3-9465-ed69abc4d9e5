<template>
  <div class="bg" :style="{height:dyHeight}">
    <div
      v-for="(item, index) in statistics"
      :key="index"
      class="statisticsItem"
      :style="{height:dyHeight}"
    >
      <el-row style="height: 100%">
        <el-col style="height: 100%" class="flexCenter" :span="6">
          <img :src="item.pic"  alt="" style="width: 60px;height: 50px">
        </el-col>
        <el-col style="height: 100%" :span="18">
          <div class="statisticsInfo">
            <div class="statisticsName">{{ item.name }}</div>
            <div class="statisticsNums">{{ item.nums }}</div>
          </div>
        </el-col>
      </el-row>
<!--      <div class="statisticsInfo">
        <div class="statisticsName">{{ item.name }}</div>
        <div class="statisticsNums">{{ item.nums }}</div>
      </div>-->
    </div>
  </div>
</template>
<script>
  export default {
    name: 'Topstatistics',
    props: {
      statistics: {
        type: Array,
        default: () => {
          ;[]
        },
      },
      dyHeight:{
        type:String,
        default : '114px'
      }
    },
    data() {
      return {}
    },
    mounted() {},
    methods: {},
  }
</script>

<style scoped lang="scss">
  .bg {
    background-color: transparent;
    width: 100%;
    display: flex;
    margin-bottom: 10px;
  }
  .flexCenter {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .statisticsItem {
    background: linear-gradient(225deg, #FFFFFF 0%, #F6F6F6 100%);
    border-radius: 7px;
    border: 1px solid #EAE3E3;
    margin-right: 10px;
    flex: 1;
    position: relative;
    .statisticsInfo {
      height: 80px;
      width: 70%;
      position: absolute;
      top: 50%;
      left: 65%;
      transform: translate(-50%, -50%);
      text-align: center;

      .statisticsName {
        height: 40px;
        width: 100%;
        line-height: 40px;
        text-align: center;
        font-weight: bold;
        color: #333333;
        font-size: 20px;
        font-family: 'PingFang SC', Arial, 'Microsoft YaHei', sans-serif;
      }
      .statisticsNums {
        height: 40px;
        width: 100%;
        line-height: 40px;
        text-align: center;
        font-weight: bold;
        font-size: 20px;
        color: #333333;
        font-family: 'PingFang SC', Arial, 'Microsoft YaHei', sans-serif;
      }
    }
  }
</style>
