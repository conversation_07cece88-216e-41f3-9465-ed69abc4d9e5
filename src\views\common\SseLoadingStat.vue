<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-statistic
            group-separator=","
            :precision="2"
            :value="value2"
            :title="title"
          ></el-statistic>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import { lesysCloseSse } from '@/utils/lesysSse' 
  import { baseURL } from '@/config'
  export default {
    name: 'sseLoadingStat',
    props: {
      uid:{
        type:String,
        default:'sse1234567890'
      }
    },
    data() {
      return {
        percentage:0,
        tipContent:'执行中',
        showTooltip:false,
        value2: 0,
        title: "操作进行中"
      }
    },
    created() {
      this.sseTest()
    },
    methods: {
      sseTest(){
        let that = this
        let eventSource = new EventSource(baseURL+'/Sse/createSse/'+this.uid,{ withCredentials: true,openWhenHidden:true });
        eventSource.onopen = function (event) {
          //console.log('SSE链接成功');
        }
        eventSource.onmessage = function (event) {
            if(event.data){
                //console.log('后端返回的数据:', event.data);
                if(event.data!='连接成功'){
                  let data = JSON.parse(event.data);
                  that.setPec(data.pec)
                  that.tipContent = data.msg
                  that.showTooltip = true
                  that.value2 = data.rows
                  if(data.status=='finished'){
                    that.closeSse()
                    eventSource.close()
                    setTimeout(() => {
                      that.$emit("handleFinish",data.data||0)
                    }, 1000)
                    
                  }
                }
            }
        }
        eventSource.onerror = (error) => {
            console.log('SSE链接失败');
        };  
      },
      setPec(pec){
        this.percentage = pec
      },
      closeSse(){
        lesysCloseSse({id:this.uid})
      }
    },
    watch: {
      form(newVal) {
        
      }
    }
  }
</script>