/**
 *  方法名：listToTree
 *  label非必传项
 * */
export function listToTree(myId, pId, list, label) {
  function exists(list, parentId) {
    for (let i = 0; i < list.length; i++) {
      if (list[i][myId] == parentId) return true
    }
    return false
  }
  let nodes = []
  for (let i = 0; i < list.length; i++) {
    if (label) {
      let code = ''
      if (list[i].mlflbm !== undefined) {
        code = '(' + list[i].mlflbm + ')'
      }
      list[i].label = list[i][label] + code
    }
    if (myId != 'id') {
      list[i].id = list[i][myId]
    }
    let row = list[i]
    if (!exists(list, row[pId])) {
      nodes.push(row)
    }
  }
  let toDo = []
  for (let i = 0; i < nodes.length; i++) {
    toDo.push(nodes[i])
  }
  while (toDo.length) {
    let node = toDo.shift()
    for (let i = 0; i < list.length; i++) {
      let row = list[i]
      if (row[pId] == node[myId]) {
        if (node.children) {
          node.children.push(row)
        } else {
          node.children = [row]
        }
        toDo.push(row)
      }
    }
  }
  for (let j = 0; j < nodes.length; j++) {
    // 不加会报错  下面的js拼接 找不到children 会报错
    if (nodes[j].children == undefined) {
      nodes[j].children = []
    }
  }
  return nodes
}
