<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >

    <div class="search-container">
      <el-input placeholder="业务属性" v-model="searchForm.bfName"  @change="handleSearch" style="width:150px;" />&nbsp;
      
      <el-button icon="el-icon-save" type="success" @click="handleAdd5">
        保存配置
      </el-button> 
      <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
        刷新
      </el-button>
    </div>

    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'selectbtn'" #default="{ row }">
          <el-input v-model="row[item.prop]" style="width:70%;"></el-input>
          <el-button icon="el-icon-search" circle @click="selectParamDir(row)"></el-button>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="120"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">保存</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>
    <table-edit ref="lesysParamdirsSelRef">
      <lesysParamdirsSel
        ref="lesysParamdirsSel"
        :lpdSsign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="lesysParamdirsSelClose">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="lesysParamdirsSelSave"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,busiFieldConfigDoUpdateOrd,busiFieldConfigDoReset,busiFieldConfigDoDelete } from '@/api/busp/busiFieldConfig'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import TableEdit from '@/views/common/TableEdit.vue'
  import lesysParamdirsSel from '@/views/oa/lesysParamdirs/index_sel.vue'

  export default {
    name: 'busiFieldConfigManageSearch',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      lesysParamdirsSel,
      TableEdit
    },
    data() {
      return {
        bfcOrder:0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['属性名','业务属性名','使用状态','是否查询列','查询列顺序','查询列字典'],
        columns: [
                { prop:'bfField'      , label:'属性名', width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性名', width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfUseState'   , label:'使用状态'    , width:'auto' , sortable:false  },
                { prop:'bfcIsquery'   , label:'是否查询列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcQorder'    , label:'查询列顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcDictionary', label:'查询列字典', width:'auto' , sortable:false  ,editType:'selectbtn'}       
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          sortField:'BFC_QORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'},{value:'快速查询列',label:'快速查询列'}],
        busiModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfcBmId = row.bmId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      selectParamDir(row){
        this.$refs['lesysParamdirsSelRef'].showEdit('选择字典')
      },
      lesysParamdirsSelClose(){
        this.$refs.lesysParamdirsSelRef.close()
      },
      lesysParamdirsSelSave(){
        var row = this.$refs.lesysParamdirsSel.getSel()
        this.row.bfcDictionary = "{\"name\":\""+row.lpdName+"\",\"type\":\"url\",\"data\":\""+row.lpdId+"\"}";
        this.$refs.lesysParamdirsSelRef.close()
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        var slist = []
        slist.push({bfcIsquery:row.bfcIsquery,
                        bfcQorder:row.bfcQorder,
                        bfcDictionary:row.bfcDictionary,
                        bfName:row.bfName,
                        bfcId:row.bfcId})
        const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
        if(msg.code == 200) {
          this.$message({message:'保存操作成功!',type:'success'})
        }else{
          this.$message({message:'保存操作失败!',type:'warning'})
        }
      },
      async handleAdd(command) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIsquery:this.list[i].bfcIsquery,
                        bfcQorder:this.list[i].bfcQorder,
                        bfcDictionary:this.list[i].bfcDictionary,
                        bfName:this.list[i].bfName,
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>