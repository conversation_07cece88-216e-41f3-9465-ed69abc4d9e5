<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiModelSearchSimple
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="busiModelTs"
      @handleSearch="handleSearch"/>

    <el-table
      ref="busiModelTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiModel"
      row-key="bmId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import { busiModelDoDeleteELog,
           busiModelGetList,
           busiModelDoSaveOrUpdLog,
           busiModelDoExport } from '@/api/busp/busiModel'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiModelSearchSimple from './components/SearchSimple.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  export default {
    name: 'busiModelSimple',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiModelSearchSimple
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','描述','实体名'],
        columns: [
                { prop:'bmName'  , label:'名称'  , width:'auto' , sortable:false  },
                { prop:'bmDesc'  , label:'描述'  , width:'auto' , sortable:false  }, 
                { prop:'bmEntity', label:'实体名', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bmDesc:'',
          bmEntity:'',
          bmName:'',
          bmSign:this.bmSign,
          bmSort:'',
          bmTable:'',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiModelForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.bmId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await busiModelDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiModelEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
        try{
          this.$emit("busiModelRowSelect",val);
        }catch(Ex){}
      },
      // 添加按钮事件
      handleAdd(command) {
     
      },
      handleSet(row) {
        this.$refs['busiFieldSel'].showEdit('业务属性')
        this.$nextTick(()=> {
          this.$refs.busiField.init(row)
        })
      },
      busiFieldclose(){
        this.$refs.busiFieldSel.close()
      },
      busiFieldConfigListclose(){
        this.$refs.busiFieldConfigListSel.close()
      },
      busiFieldConfigFormclose(){
        this.$refs.busiFieldConfigFormSel.close()
      },
      busiFieldConfigSearchclose(){
        this.$refs.busiFieldConfigSearchSel.close()
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['busiModelEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['busiModelQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.busiModelQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.busiModelQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await busiModelGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"业务模型表.xls",
                      "isnumber":true,
                      "excelTitle":"业务模型表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#BusiModel",params)
        const { msg }  =  await busiModelDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"业务模型表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#BusiModel",params)
        const { msg }  =  await busiModelDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>