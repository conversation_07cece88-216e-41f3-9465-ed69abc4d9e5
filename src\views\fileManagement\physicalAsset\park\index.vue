<!--园区资产档案-->
<template>
  <div class="park-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-s-home" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总园区数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>个</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">园区总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.totalArea || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.disposableCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline" label-width="100px">
        <el-form-item label="资产名称:">
          <el-input v-model="searchForm.zpkAssetName" placeholder="请输入资产名称" class="inputW" />
        </el-form-item>
        <el-form-item label="资产编号:">
          <el-input v-model="searchForm.zpkAssetNumber" placeholder="请输入资产编号" class="inputW" />
        </el-form-item>
        <el-form-item label="资产类型:">
          <el-select v-model="searchForm.zpkAssetType" placeholder="请选择资产类型" clearable class="inputW">
            <el-option label="园区" value="园区" />
            <el-option label="产业园" value="产业园" />
            <el-option label="科技园" value="科技园" />
          </el-select>
        </el-form-item>
        <el-form-item label="所在地区:">
          <el-input v-model="searchForm.zpkLocationProvinceCityDistrict" placeholder="请输入所在地区" class="inputW" />
        </el-form-item>
        <el-form-item label="经营方向:">
          <el-input v-model="searchForm.zpkMainBusinessDirection" placeholder="请输入经营方向" class="inputW" />
        </el-form-item>
        <el-form-item label="现状用途:">
          <el-select v-model="searchForm.zpkCurrentUsageDescription" placeholder="请选择现状用途" clearable class="inputW">
            <el-option label="科研办公" value="科研办公" />
            <el-option label="商业经营" value="商业经营" />
            <el-option label="工业生产" value="工业生产" />
            <el-option label="综合用途" value="综合用途" />
          </el-select>
        </el-form-item>
        <el-form-item label="境内/境外:">
          <el-select v-model="searchForm.zpkDomesticOrForeign" placeholder="请选择" clearable class="inputW">
            <el-option label="境内" value="境内" />
            <el-option label="境外" value="境外" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否存在纠纷:">
          <el-select v-model="searchForm.zpkHasDispute" placeholder="请选择" clearable class="inputW">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" :height="280" border style="width: 100%;margin-bottom: 20px;" v-loading="loading" row-key="zpkId">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="zpkAssetNumber" label="资产编号" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkAssetName" label="资产名称" width="150" show-overflow-tooltip />
      <el-table-column prop="zpkAssetType" label="资产类型" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkCurrentUsageDescription" label="现状用途" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkLocationProvinceCityDistrict" label="所在地区" width="150" show-overflow-tooltip />
      <el-table-column prop="zpkSpecificLocation" label="具体位置" width="180" show-overflow-tooltip />
      <el-table-column prop="zpkTotalArea" label="占地面积(㎡)" width="120" />
      <el-table-column prop="zpkOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkNetValue" label="净值(万元)" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkMainBusinessDirection" label="主要经营方向" width="150" show-overflow-tooltip />
      <el-table-column prop="zpkDomesticOrForeign" label="境内/境外" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkIsNonCoreAsset" label="是否两非资产" width="120" />
      <el-table-column prop="zpkHasDispute" label="是否存在纠纷" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkHasMortgage" label="是否存在抵押" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkOperator" label="联系人" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkOperatorContact" label="联系电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkResearchOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkCommercialArea" label="商业面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkResidentialArea" label="住宅面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkIndustrialArea" label="工业面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkRentalArea" label="出租面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkSelfUseArea" label="自用面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkIdleArea" label="闲置面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkOtherArea" label="其他面积" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkMortgagedArea" label="已抵押面积" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkDepartmentLeaderContact" label="部门电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkCompanyLeaderContact" label="分公司电话" width="120" show-overflow-tooltip />
      <el-table-column prop="zpkRemarks" label="备注" width="150" show-overflow-tooltip />
      <el-table-column prop="zpkCreatedTime" label="创建时间" width="160" show-overflow-tooltip />
      <el-table-column prop="zpkCreatedBy" label="创建人" width="100" show-overflow-tooltip />
      <el-table-column prop="zpkUpdatedTime" label="更新时间" width="160" show-overflow-tooltip />
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" background />

    <!-- 详情弹窗组件 -->
    <park-detail-dialog ref="parkDetail" />
  </div>
</template>

<script>
import { getParksList, exportParks, getParksStats } from '@/api/park'
import ParkDetailDialog from './components/ParkDetailDialog.vue'

export default {
  name: "index",
  components: {
    ParkDetailDialog
  },
  data () {
    return {
      searchForm: {
        zpkAssetName: '',
        zpkAssetNumber: '',
        zpkAssetType: '',
        zpkLocationProvinceCityDistrict: '',
        zpkMainBusinessDirection: '',
        zpkCurrentUsageDescription: '',
        zpkDomesticOrForeign: '',
        zpkHasDispute: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        totalArea: 0,
        disposableCount: 0
      },
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }

      getParksList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getParksStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.TOTAL_PARK_REGISTRATION || 0,
            totalValue: response.data.ROUND || 0,
            totalArea: response.data.TOTAL_AREA || 0,
            disposableCount: response.data.DISPOSABLE_COUNT || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.currentPage = 1
      this.fetchData()
    },

    resetQuery () {
      this.currentPage = 1
      this.searchForm = {
        zpkAssetName: '',
        zpkAssetNumber: '',
        zpkAssetType: '',
        zpkLocationProvinceCityDistrict: '',
        zpkMainBusinessDirection: '',
        zpkCurrentUsageDescription: '',
        zpkDomesticOrForeign: '',
        zpkHasDispute: ''
      }
      this.fetchData()
    },

    onExport () {
      const query = {
        ...this.searchForm
      }
      exportParks(query).then(() => {
        // 处理导出逻辑
        this.$message.success('导出成功')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },

    handleDetail (row) {
      this.$refs.parkDetail.showDialog(row)
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
.park-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.statistics-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 250px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  i {
    font-size: 24px;
    color: white;
  }
}

.red-bg {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.orange-bg {
  background: linear-gradient(135deg, #ffa726, #ff9800);
}

.blue-bg {
  background: linear-gradient(135deg, #42a5f5, #2196f3);
}

.purple-bg {
  background: linear-gradient(135deg, #ab47bc, #9c27b0);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;

  span {
    font-size: 14px;
    font-weight: normal;
    margin-left: 4px;
  }
}

.red-text {
  color: #ff6b6b;
}

.orange-text {
  color: #ffa726;
}

.blue-text {
  color: #42a5f5;
}

.purple-text {
  color: #ab47bc;
}

.search-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-inline {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.inputW {
  width: 200px;
}
</style>