<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-row :gutter="20">
      <el-col :span="10" >
        <lesysDbDsSimple ref="lesysDbDsSimple" @lesysDbDsSimple="lesysDbDsSimple">
          
        </lesysDbDsSimple>
      </el-col>
      <el-col :span="14">
        <lesysDbTables ref="lesysDbTables">
          
        </lesysDbTables>
      </el-col>
    </el-row>  
  </div>
</template>

<script>
  import lesysDbDsSimple from '@/views/dbds/lesysDbDs/indexSimple.vue'
  import lesysDbTables from './index.vue'

  export default {
    name: 'lesysDbTables1',
    components: {
      lesysDbDsSimple,
      lesysDbTables
    },
    data() {
      return {
        isFullscreen:false
      }
    },
    computed: {

    },
    created() {
      
    },
    methods: {
      lesysDbDsSimple(lesysDbDs){
        this.$refs.lesysDbTables.loadTables(lesysDbDs)
      }
      
    }
  }
</script>