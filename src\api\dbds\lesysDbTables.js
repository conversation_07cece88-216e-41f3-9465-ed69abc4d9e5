import request from '@/utils/request'

//列表分页加载数据
export function lesysDbTablesGetPage(params) {
  return request({
    url: '/lesys-db-tables/vPageList',
    method: 'get',
    params,
  })
}
export function lesysDbTablesGetList(params) {
  return request({
    url: '/lesys-db-tables/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbTablesDoSave(data) {
  return request({
    url: '/lesys-db-tables/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbTablesDoUpdate(data) {
  return request({
    url: '/lesys-db-tables/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbTablesDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-tables/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbTablesDoDelete(data) {
  return request({
    url: '/lesys-db-tables/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbTablesDoExport(data) {
  return request({
    url: '/lesys-db-tables/vExport',
    method: 'post',
    data,
  })
}