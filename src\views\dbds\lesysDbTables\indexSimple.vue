<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysDbTablesSearchSimple
      :queryForm="searchForm"
      ref="lesysDbTablesTs"
      @handleSearch="handleSearch"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysDbTablesTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="lesysDbTables"
      row-key="ldtId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
      type="selection"
      width="55">
      </el-table-column>

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-search ref="lesysDbTablesQuerySearch">
      <lesysDbTablesQuerySimple
        ref="lesysDbTablesQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { lesysDbTablesGetPage} from '@/api/dbds/lesysDbTables'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysDbTablesSearchSimple from './components/searchSimple.vue'
  import lesysDbTablesQuerySimple from './components/querySimple.vue'

  import { testDataSource,syncTables,syncColumns,syncTableByLdId,lesysDbDsColumnExport } from '@/api/dbds/lesysDbDs'
  import { Loading } from 'element-ui'
  import { baseURL } from '@/config'
  import axios from 'axios'
  export default {
    name: 'lesysDbTablesSimple',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableSearch,
      lesysDbTablesSearchSimple,
       lesysDbTablesQuerySimple
    },
    data() {
      return {
        fullscreenLoading: false,
        queryFormDf: {ldtLdId:'',ldtTableName:'',ldtTableComment:'',ldtType:''},
        queryForm: {ldtLdId:'',ldtTableName:'',ldtTableComment:'',ldtType:''},
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['数据源ID','表名','表描述','类型'],
        columns: [
                { prop:'ldtTableName', label:'表名', width:'auto' , sortable:false  },
                { prop:'ldtTableComment', label:'表描述', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        lesysDbDsRow:{},
        multipleSelection:[]
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      //this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      loadTable(ldr){
        this.lesysDbDsRow = ldr
        this.searchForm.ldtLdId = ldr.ldId
        this.fetchData()
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
        try{
          this.$emit("lesysDbTablesSimpleSel",this.lesysDbDsRow,val);
        }catch(Ex){}
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      //同步表列
      async handleQuery(command) {
        var tables = []
        var tableids = []
        for(var i=0;i<this.multipleSelection.length;i++){
          tables[i] = this.multipleSelection[i].ldtTableName
          tableids[i] = this.multipleSelection[i].ldtId
        }
        if(command=='synctablecolumn'){
          
          if(tables.length==0){
            this.$message({message:'请选择需要同步列的表!',type:'warning'})
          }else{
            const msg0 = await testDataSource(this.lesysDbDsRow.ldId)
            if(msg0.code == 200) {
              this.$baseConfirm('确定同步数据源数据表列吗？', null, async () => {
                  let loadingInstance = Loading.service({ fullscreen: true,text: '数据源数据表列同步中。。。。。' });
                  const msg = await syncColumns({'ldId':this.lesysDbDsRow.ldId,'tableName':tables.join(","),'tableId':tableids.join(",")})
                  this.$nextTick(() => {
                    loadingInstance.close();
                  });
                  if(msg.code == 200) {
                    this.$message({message:'同步成功!',type:'success'})
                    try{
                      this.$emit("lesysDbTablesSimpleSel",this.lesysDbDsRow,this.row);
                    }catch(Ex){}
                  }else{
                    this.$message({message:'同步失败!',type:'warning'})
                  }
              })
            }else{
              this.$message({message:'数据库连接失败，无法同步!',type:'warning'})
            }
          } 
        }
        else if(command=='exporttablecolumns'){
          if(tables.length==0){
            this.$message({message:'请选择需要导出的表!',type:'warning'})
          }else{
            let param = {ldId:this.lesysDbDsRow.ldId,ldtcLdtTableName:tables.join(",")}

            const data  =  await lesysDbDsColumnExport(param)
            if(data.code==200){
              axios({
                url: baseURL+'/lesys-db-ds/downloadFile',
                method: 'POST',
                responseType: 'blob',
                data: {'fileId':data.msg}
              })
              .then(response => {
                const url = window.URL.createObjectURL(new Blob([response.data]))
                const link = document.createElement('a')
                link.href  = url
                link.setAttribute('download','table.xls')
                document.body.appendChild(link)
                link.click()
              })
              .catch(error => {

              })
            }
          }
        }   
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysDbTablesQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          if(this.queryForm[key]!=''){
            this.searchForm[key] = this.queryForm[key]
          }
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysDbTablesQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysDbTablesGetPage(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>