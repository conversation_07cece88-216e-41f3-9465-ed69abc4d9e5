<template>
  <div class="vab-chart-line-container">
    <vab-chart
      :init-options="initOptions"
      :option="option"
      theme="vab-echarts-theme"
    />
  </div>
</template>

<script>
  import Vab<PERSON>hart from '../VabChart'

  export default {
    name: 'Vab<PERSON>hartLine',
    components: {
      VabChart,
    },
    props: {
      title: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        initOptions: {
          renderer: 'svg',
        },
        option: {
          grid: {
            top: 20,
            right: 20,
            bottom: 40,
            left: 40,
          },
          xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          yAxis: {
            type: 'value',
          },
          series: {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line',
          },
        },
      }
    },
  }
</script>
