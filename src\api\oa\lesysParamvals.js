import request from '@/utils/request'

//列表分页加载数据
export function lesysParamvalsGetList(params) {
  return request({
    url: '/lesys-paramvals-vue/vPage',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysParamvalsDoSave(data) {
  return request({
    url: '/lesys-paramvals-vue/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysParamvalsDoUpdate(data) {
  return request({
    url: '/lesys-paramvals-vue/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysParamvalsDoSaveOrUpd(data) {
  return request({
    url: '/lesys-paramvals-vue/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function lesysParamvalsDoSaveOrUpdLog(data) {
  return request({
    url: '/lesys-paramvals-vue/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysParamvalsDoDelete(data) {
  return request({
    url: '/lesys-paramvals-vue/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function lesysParamvalsDoDeleteLog(data) {
  return request({
    url: '/lesys-paramvals-vue/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function lesysParamvalsDoDeleteELog(data) {
  return request({
    url: '/lesys-paramvals-vue/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function lesysParamvalsGetTreeList(params){
  return request({
    url: '/lesys-paramvals-vue/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function lesysParamvalsDoExport(data) {
  return request({
    url: '/lesys-paramvals-vue/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function lesysParamvalsGetStat(params) {
  return request({
    url: '/lesys-paramvals-vue/vStat',
    method: 'get',
    params,
  })
}

//上移
export function lesysParamvalsDoUp(data) {
  return request({
    url: '/lesys-paramvals-vue/up',
    method: 'post',
    data,
  })
}
//下移
export function lesysParamvalsDoDown(data) {
  return request({
    url: '/lesys-paramvals-vue/down',
    method: 'post',
    data,
  })
}
