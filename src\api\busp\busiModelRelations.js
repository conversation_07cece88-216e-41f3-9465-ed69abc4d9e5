import request from '@/utils/request'


export function busiModelRelationsGetData(params) {
  return request({
    url: '/busi-model-relations/vList',
    method: 'get',
    params,
  })
}

//保存新增,不记录日志
export function busiModelRelationsDoSave(data) {
  return request({
    url: '/busi-model-relations/save',
    method: 'post',
    data,
  })
}

//根据条件删除,记录日志,参数为实体，logDesc
export function busiModelRelationsDoDelete(data) {
  return request({
    url: '/busi-model-relations/delete',
    method: 'post',
    data,
  })
}
