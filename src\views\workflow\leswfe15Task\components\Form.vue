<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="12">
          <el-form-item label="流程实例ID" prop="instId">
            <el-input v-model.trim="form.instId" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点ID" prop="nodeId">
            <el-input v-model.trim="form.nodeId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点名称" prop="nodeName">
            <el-input v-model.trim="form.nodeName" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上一环节task_id，第一个为"0"，缺省" prop="taskPid">
            <el-input-number v-model.trim="form.taskPid" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务组id，抢先式任务为同一组,task_gid = task_pid，非抢先式 taskgid=0" prop="taskGid">
            <el-input-number v-model.trim="form.taskGid" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指定的任务办理人,登录帐号" prop="taskUid">
            <el-input v-model.trim="form.taskUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指定的任务办理人,姓名" prop="taskUxm">
            <el-input v-model.trim="form.taskUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务产生时间" prop="taskTime">
            <el-date-picker v-model.trim="form.taskTime" style="width:100%" type="date" clearable value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否是新任务：1-新任务, 否则其他值" prop="taskIsNew">
            <el-input-number v-model.trim="form.taskIsNew" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务实际办理人,登录帐号" prop="taskDoUid">
            <el-input v-model.trim="form.taskDoUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务实际办理人,姓名" prop="taskDoUxm">
            <el-input v-model.trim="form.taskDoUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务实际办理时间" prop="taskDoTime">
            <el-input-number v-model.trim="form.taskDoTime" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务办理的意见输入" prop="taskDoIdea">
            <el-input v-model.trim="form.taskDoIdea" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务办理用户(TASK_UID)的 部门,岗位,角色,用户组 等属性信息" prop="xUserData">
            <el-input v-model.trim="form.xUserData" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务所属流程节点的 部门,岗位,角色,用户组 等属性信息, 来自流程定义中存储的数据" prop="xNodeData">
            <el-input v-model.trim="form.xNodeData" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务办理过程中的表单数据暂存: json object 格式" prop="xTempSave">
            <el-input type="textarea" :rows="2" maxlength="2000" show-word-limit clearable v-model.trim="form.xTempSave"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务办理过程中文件操作的动作，临时存放" prop="xTempFile">
            <el-input type="textarea" :rows="2" maxlength="2000" show-word-limit clearable v-model.trim="form.xTempFile"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务办理过程中客户端获取节点的留底，用于比对，避免再次解析流程" prop="xTempSend">
            <el-input type="textarea" :rows="2" maxlength="2000" show-word-limit clearable v-model.trim="form.xTempSend"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务ID,流水号" prop="taskId"  style="display:none;">
            <el-input v-model.trim="form.taskId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'leswfe15TaskForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>