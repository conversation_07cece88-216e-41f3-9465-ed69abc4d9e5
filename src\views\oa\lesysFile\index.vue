<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model="key"
              placeholder="快速搜索"
              @keyup.enter.native="handleFind"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-upload"
              type="primary"
              @click="handleAdd('add')"
            >
              上传
            </el-button>
          </el-form-item>
          <!--el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
          </el-form-item-->
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-dropdown @command="handleExport">
          <el-button type="primary">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-download"   command="page">导出当前数据</el-dropdown-item>
            <el-dropdown-item icon="el-icon-download"   command="allp">导出全部数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </vab-query-form-right-panel>
    </vab-query-form>

    <vxe-table
      class="mylist-table"
      v-loading="listLoading"
      ref="table"
      id="mainData"
      border
      stripe
      show-overflow="tooltip"
      :height="height"
      :row-config="{ useKey: true,keyField:'dataId',isCurrent:true,isHover:true }"
      :column-config="{ resizable: true, useKey: true }"
      :data="list"
      size="small"
      :scroll-y="{enabled: true, gt: 300}"
      @current-change="currentSelectRow"
      :checkbox-config="{highlight: true}"
      @cell-dblclick="cellDblClick"
      :seq-config="seqConfig"
      :export-config="exportConfig"
    >
      <vxe-column field="seq" type="seq" width="70" align="center" fixed="left"></vxe-column>
      <vxe-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :title="item.label"
        :field="item.prop"
        align="center"
        v-bind="{ ...item }"
        header-align="center"
        minWidth="150"
      >
      </vxe-column>

      <vxe-column
        align="center"
        title="操作"
        fixed="right"
        show-overflow-tooltip
        width="200"
        field="oper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleView(row)" icon="el-icon-circle-plus">查看</el-button>
          <el-button type="text" @click="handleDown(row)" icon="el-icon-download">下载</el-button>
          <el-button type="text" @click="handleDelete(row)" icon="el-icon-delete">删除</el-button>
        </template>
      </vxe-column>

      <template #empty>
        <el-image
          style="height: 110px"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </vxe-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!--table-edit ref="sseLoadingEdit">
      <sseLoading
        ref="sseLoading"
        @handleFinish="handleFinish"
        :uid="uid"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="sseLoadingclose">
          关 闭
        </el-button>
      </template>
    </table-edit -->

    <FileUpload 
      ref="fileUpload1"
      :config="config1"
      @handleUploadScuess="handleUploadScuess1"/>
  </div>
</template>

<script>
  import { lesysFileGetPageList,lesysFileDown,lesysFileExport } from '@/api/oa/lesysFile'         
  import { exportRearTmplVxe } from '@/api/exportExcel'
  import { mapGetters } from 'vuex'
  import config from '@/config'
  import { uuid } from '@/utils/index'
  import { LesFile } from '@/utils/lesFile'
  import sseLoading from '@/views/common/SseLoading.vue'
  import FileUpload from '@/views/common/FileUpload.vue'
  export default {
    name: 'lesysFile',
    props: {
      gheight: {
        type: Number,
      }
    },
    components: {
      FileUpload
      //sseLoading
    },
    data() {
      let searchForm = {
          pageNo: 1,
          pageSize: 20,
          sortField: '',
          sortOrder: '',
          tn:this.mainName
      };
      const seqConfig = {
        seqMethod ({ rowIndex }) {
          return `${(searchForm.pageNo - 1) * searchForm.pageSize + rowIndex + 1}`
        }
      };
      const exportConfig = {
        sheetMethod(params) {
          const { worksheet } = params;
          worksheet.eachRow(excelRow => {
              excelRow.height = 40;
              excelRow.eachCell(excelCell => {
                  // 设置单元格边框
                  excelCell.border = {
                      top: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      left: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      bottom: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      right: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      }
                  };
              });
          });
        }
      };

      return {
        fullscreenLoading: false,
        editType: '',
        queryFormDf: {},
        queryForm: {},
        form: {},
        formConfig: {
          labelPosition: 'right',
          labelWidth: '80px',
          size: 'small',
        },
        title: '',
        isFullscreen: false,
        height: !this.gheight ? this.$baseTableHeight(1, 1) : this.gheight,
        list: [],
        tableData: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm,
        rules: {},
        checkList: ['文件名称','文件时间','文件大小'],
        columns: [
          { prop:'fileName'     , label:'文件名称', sortable:false  },
          { prop:'fileTimestamp', label:'文件时间', sortable:false  },
          { prop:'fileSize'     , label:'文件大小', sortable:false  }
        ],
        multipleSelection: [],
        editWidth: '1200px', 
        checkListForms:{},
        seqConfig,
        exportConfig,
        key:'',
        uid:'',
        config1: {
          limit:1,
          isDecode: 0
        }
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: { 
      handleAdd(){
        this.$refs['fileUpload1'].show()
      },
      async handleUploadScuess1(files){
        this.$message({message:'上传附件操作成功!',type:'success'})
        this.fetchData() 
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val.row
      },
      // 双击行编辑事件
      cellDblClick(val) {
        this.row = val.row
      },
      handleView(row){
        LesFile(row.fileId, 'v')
      },
      handleDown(row){
        LesFile(row.fileId, 'l')
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗？', null, async () => {
          LesFile(row.fileId, 'd')
          this.fetchData()
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleFind() {
        let searchProps = []
        this.columns.forEach(key => {
          searchProps.push(key['prop'])
        })
        const filterVal = String(this.key).trim()
        if (filterVal) {
          const filterRE = new RegExp(filterVal, 'gi')
          const rest = this.tableData.filter(item => searchProps.some(key => String(item[key]).indexOf(filterVal) > -1))
          this.list = rest.map(row => {
            // 搜索为克隆数据，不会污染源数据
            const item = Object.assign({}, row)
            searchProps.forEach(key => {
              item[key] = String(item[key]).replace("null","")
            })
            return item
          })
        } else {
          this.list = this.tableData
        }
      },
      
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysFileGetPageList(this.searchForm)
        this.tableData = list
        this.list = list
        this.total = total
        this.listLoading = false
      },
      async handleFinish(batchId){
        this.$refs.sseLoadingEdit.close()
        if(batchId!=0 && batchId!=-1){
          let fm = {fileId:batchId,fileName:"附件.xlsx"}
          const data = await lesysFileDown({export:fm})
          if(data.code==200){
            window.open(config.baseURL+"/"+data.msg)
          }else{
            this.$message({ message: data.msg||'导出数据失败!', type: 'warning' })
          }
        }
      },
      sseLoadingclose(){
        this.$refs.sseLoadingEdit.close()
      },
      async handleExport(command){
        if(command=='page'){
          this.handleExportRear()
        }
        else{
          this.$message({ message: '功能待实现。。。。。!', type: 'warning' })
          return
          this.uid = uuid()
          this.$refs['sseLoadingEdit'].showEdit('数据导出')
          const $table = this.$refs.table
          if ($table) {
            let columns = $table.getColumns()
            let excelExps = []
            for(var i=0;i<columns.length;i++){
              if(columns[i].field=='seq' || columns[i].field=='oper'){
                continue
              }
              let cs = parseInt(columns[i].colSpan)-1
              excelExps.push({field:columns[i].field,title:columns[i].title,rowspan:columns[i].rowSpan,celliswrap:"0",celltype:"text",cellwidth:"18",cellwraplength:"0",
                              colspan:columns[i].colSpan,dateFormat:"yyyy-MM-dd HH:mm:ss"})
              for(var j=0;j<cs;j++){
                excelExps.push({field:'',title:'',rowspan:1,celliswrap:"0",celltype:"text",cellwidth:"18",cellwraplength:"0",
                              colspan:1,dateFormat:"yyyy-MM-dd HH:mm:ss"})
              }                
            }
            let exportForm = Object.assign({}, this.searchForm)
            exportForm.export = {"sheetName":"附件","excelExps":[excelExps],isserial:0,fileName:"附件.xlsx",pageSize:500}
            exportForm.uid = this.uid
            const data = await lesysFileExport(exportForm)
            if(data.code==200){
              //window.open(config.baseURL+"/"+data.msg)
              this.$message({ message: '启动数据导出操作成功!', type: 'success' })
            }else{
              this.$message({ message: data.msg||'导出失败!', type: 'warning' })
            }
          }
        }
      },
      async handleExportRear() {
        const $table = this.$refs.table
        if ($table) {
          var expcolumns = this.columns
          expcolumns.unshift({field:'seq'})
          $table.exportData({
            //excludeFields:"checkbox,oper",
            type: 'xlsx',
            columns:expcolumns,
            filename:`附件${Date.now()}`,
            sheetName:'附件'
          })
        }
      },
      handleQuery(){
        //this.queryForm = Object.assign(this.queryForm, this.searchForm)
        this.$refs['mainDataSearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose() {
        this.$refs.mainDataSearch.close()
      },
      //高级查询清空
      queryClear() {
        this.queryForm = Object.assign(this.queryForm, this.queryFormDf)
        this.searchForm.query = {}
      },
      //高级查询
      querySure() {
        this.searchForm.pageNo = 1
        this.$refs.mainDataSearch.close()
        this.searchForm.query = this.queryForm
        this.fetchData()
      }
    }
  }
</script>
<style lang="scss" scoped>
  .mylist-table {
    ::v-deep(.keyword-highlight)  {
      background-color: #FFFF00;
    }
  }
</style>