<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysParamdirsSearchSel
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysParamdirsTs"
      @handleSearch="handleSearch"/>

    <el-table
      ref="lesysParamdirsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="LesysParamdirs"
      row-key="lpdId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <!--el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="170"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleVals(row)">确定选择</el-button>
        </template>
      </el-table-column-->

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

  </div>
</template>

<script>
  import { lesysParamdirsDoDeleteELog,
           lesysParamdirsGetSelList,
           lesysParamdirsDoSaveOrUpdLog,
           lesysParamdirsDoExport } from '@/api/oa/lesysParamdirs'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysParamdirsSearchSel from './components/Search_sel.vue'
  import lesysParamdirsForm from './components/Form.vue'
  import lesysParamdirsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'lesysParamdirsSel',
    props: {
      gheight: {
        type:Number
      },
      lpdSsign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysParamdirsSearchSel,
      lesysParamdirsForm,
      lesysParamdirsQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {lpdName:''},
        queryForm: {lpdName:''},
        form: {lpdName:'',lpdIstree:'',lpdSsign:'',lpdOrder:'',lpdDesc:''},
        rules: {
 
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','是否为树形','顺序','描述'],
        columns: [
                { prop:'lpdName', label:'名称', width:'auto' , sortable:false  },
                { prop:'lpdIstree', label:'是否为树形', width:'auto' , sortable:false  },
                { prop:'lpdOrder', label:'顺序', width:'auto' , sortable:false  },
                { prop:'lpdDesc', label:'描述', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          lpdName:'',
          lpdSsign:this.lpdSsign,
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        windowWidth: '0px'
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    mounted() {
      
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      handleVals(val){
        this.row = val
      },
      getSel(){
        return this.row
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysParamdirsGetSelList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      }
    },
  }
</script>