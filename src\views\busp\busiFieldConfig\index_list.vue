<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  > 
    <div class="search-container">
      <el-button icon="el-icon-save" type="primary" @click="handleAdd3">
        列表列(否)
      </el-button>
      <el-button icon="el-icon-save" type="primary" @click="handleAdd4">
        列表列(是)
      </el-button>
      &nbsp;<el-input-number v-model="bfcOrderStart" :precision="0" :step="1" :max="500" :min="0"></el-input-number>
      &nbsp;<el-input-number v-model="bfcOrder"      :precision="0" :step="1" :max="500" :min="0"></el-input-number>&nbsp;
      
      <el-dropdown @command="handleAdd6">
        <el-button type="primary">
          保存顺序<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command='-'>向前</el-dropdown-item>
          <el-dropdown-item command='+'>向后</el-dropdown-item>
          <el-dropdown-item command='0'>重置</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>&nbsp;
      <el-button icon="el-icon-save" type="primary" @click="handleAdd5">
        保存配置
      </el-button> 

      <el-button icon="el-icon-save" type="primary" @click="handleAdd10">
        重置配置
      </el-button> 

      <!--el-button icon="el-icon-plus" type="primary" @click="handleAdd11">
        选择
      </el-button-->

      <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
        刷新
      </el-button>

      &nbsp;<el-select v-model.trim="bfColumn"   clearable   style="width:200px">
        <el-option v-for="item in bfColumns" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
      </el-select>
      &nbsp;<el-button icon="el-icon-save" type="primary" @click="impcolumn">
        导入
      </el-button>
    </div>
    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'selectbtn'" #default="{ row }">
          <el-input v-model="row[item.prop]" style="width:70%;"></el-input>
          <el-button icon="el-icon-search" circle @click="selectParamDir(row)"></el-button>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="120"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">保存</el-button>
          <el-button type="text" @click="handleDele(row)" :disabled="row.bfcIsquote=='否'">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <table-edit ref="busiModelFieldConfigSelSel" :fullscreen="true">
      <busiModelFieldConfigSel
        ref="busiModelFieldConfigSel"
        :bdSsign="bmSign"
        :bmId="busiModelRow.bmId"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiModelFieldConfigSelClose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
    <table-edit ref="lesysParamdirsSelRef">
      <lesysParamdirsSel
        ref="lesysParamdirsSel"
        :lpdSsign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="lesysParamdirsSelClose">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="lesysParamdirsSelSave"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,
           busiFieldConfigDoUpdateOrd,
           busiFieldConfigDoReset,
           busiFieldConfigDoImp,
           busiFieldConfigDoDeleteLog } from '@/api/busp/busiFieldConfig'
  import { busiFieldGetList } from '@/api/busp/busiField'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import busiModelFieldConfigSel from './indexSel'
  import TableEdit from '@/views/common/TableEdit.vue'
  import lesysParamdirsSel from '@/views/oa/lesysParamdirs/index_sel.vue'
  export default {
    name: 'busiFieldConfigList',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      busiModelFieldConfigSel,
      TableEdit,
      lesysParamdirsSel
    },
    data() {
      return {
        bfcOrder:0,
        bfcOrderStart: 0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['业务实体','业务属性','是否列表','列表顺序','是否筛选','筛选字典','列表宽度'],
        columns: [
                //{ prop:'bfColumn'     , label:'业务字段', width:'auto' , sortable:false},
                { prop:'bfField'      , label:'业务实体', width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性', width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfcIslist'    , label:'是否列表', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcLorder'    , label:'列表顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcIsfilter'  , label:'是否筛选', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcDictionary', label:'筛选字典', width:'auto' , sortable:false  ,editType:'selectbtn'},
                { prop:'bfcListwidth' , label:'列表宽度', width:'auto' , sortable:false  ,editType:'number'}
                //{ prop:'bfcIsquote'   , label:'是否引用', width:'auto' , sortable:false}        
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          bfVersion:'0',
          sortField:'BFC_LORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'}],
        busiModelRow:{},
        bfColumn:'',
        bfColumns:[]
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfVersion = row.bmVersion
        this.searchForm.bfcBmId = row.bmId
        this.searchForm.bfcBcId = row.bfcBcId
        this.fetchData()
        this.fetchFieldData()
      },
      async fetchFieldData(){
        this.bfColumns = []
        const data = await busiFieldGetList({bfBmId:this.busiModelRow.bmId})
        for(let i=0;i<data.data.length;i++){
          this.bfColumns.push({value:data.data[i].bfId,label:data.data[i].bfName+'('+data.data[i].bfColumn+')'})
        }  
      },
      async impcolumn(){
        if(this.bfColumn && this.bfColumn!=''){
          const msg = await busiFieldConfigDoImp({bfBmId:this.busiModelRow.bmId,bfcBcId:this.busiModelRow.bfcBcId,bfcBfId:this.bfColumn})
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:msg.msg||'操作失败!',type:'warning'})
          }
        }
      },
      selectParamDir(row){
        this.$refs['lesysParamdirsSelRef'].showEdit('选择字典')
      },
      lesysParamdirsSelClose(){
        this.$refs.lesysParamdirsSelRef.close()
      },
      lesysParamdirsSelSave(){
        var row = this.$refs.lesysParamdirsSel.getSel()
        this.row.bfcDictionary = "{\"name\":\""+row.lpdName+"\",\"type\":\"url\",\"data\":\""+row.lpdId+"\"}";
        this.$refs.lesysParamdirsSelRef.close()
      },

      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      handleAdd3(){
        this.handleAdd('configIslist0')
      },
      handleAdd4(){
        this.handleAdd('configIslist1')
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      handleAdd10(){
        this.$baseConfirm('重置将清除所有配置信息，确定吗', null, async () => {
          const msg = await busiFieldConfigDoReset({bfBmId:this.busiModelRow.bmId,bfcBcId:this.busiModelRow.bfcBcId})
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      handleAdd11(){
        this.$refs['busiModelFieldConfigSelSel'].showEdit('选择引用模型属性列')
        this.$nextTick(()=> {
          this.$refs.busiModelFieldConfigSel.init(this.busiModelRow)
        })
      },
      busiModelFieldConfigSelClose(){
        this.$refs.busiModelFieldConfigSelSel.close()
        this.fetchData()
      },
      handleAdd6(command){
        if(command=='+'){
          this.$baseConfirm('确定将显示顺序向后'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_LORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='-'){
          this.$baseConfirm('确定将显示顺序向前'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_LORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='0'){
          this.$baseConfirm('确定重置显示顺序吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_LORDER',bfcPlus:command,bfcPlusVal:0,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        } 
      },
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        var slist = []
        slist.push({bfcIslist:row.bfcIslist,
                      bfcLorder:row.bfcLorder,
                      bfName:row.bfName,
                      bfcListwidth:row.bfcListwidth,
                      bfcId:row.bfcId,
                      bfcIsfilter:row.bfcIsfilter,
                      bfcDictionary:row.bfcDictionary,
                      bfcBfId:row.bfcBfId})
        const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
        if(msg.code == 200) {
          this.$message({message:'保存操作成功!',type:'success'})
        }else{
          this.$message({message:'保存操作失败!',type:'warning'})
        }
      },
      handleDele(row){
        if(row.bfcIsquote=='否'){
          this.$message({message:'非引用模型属性配置，无法删除!',type:'warning'})
        }else{
          this.$baseConfirm('确定删除引用的属性配置吗', null, async () => {
            const  msg  = await busiFieldConfigDoDeleteLog( row )
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
      },
      async handleAdd(command) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:this.list[i].bfcIslist,
                        bfcLorder:this.list[i].bfcLorder,
                        bfName:this.list[i].bfName,
                        bfcListwidth:this.list[i].bfcListwidth,
                        bfcId:this.list[i].bfcId,
                        bfcBfId:this.list[i].bfcBfId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist0'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:'否',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist1'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:'是',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>