<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">


        <el-col :span="24">
          <el-form-item label="参数名称" prop="lpvName">
            <el-input v-model.trim="form.lpvName" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="参数标识" prop="lpvSign">
            <el-input v-model.trim="form.lpvSign" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="参数值" prop="lpvVal">
            <el-input v-model.trim="form.lpvVal" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="lpvDesc">
            <el-input type="textarea" :rows="2" maxlength="100" show-word-limit clearable v-model.trim="form.lpvDesc"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="id" prop="lpvId"  style="display:none;">
            <el-input v-model.trim="form.lpvId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="目录ID" prop="lpvLpdId"  style="display:none;">
            <el-input v-model.trim="form.lpvLpdId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="pid" prop="lpvPid"  style="display:none;">
            <el-input v-model.trim="form.lpvPid" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'lesysParamvalsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>