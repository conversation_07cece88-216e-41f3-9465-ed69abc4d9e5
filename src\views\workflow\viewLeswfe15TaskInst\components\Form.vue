<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="8">
          <el-form-item label="实例ID" prop="instId">
            <el-input v-model.trim="form.instId" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="节点ID" prop="nodeId">
            <el-input v-model.trim="form.nodeId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务名称" prop="nodeName">
            <el-input v-model.trim="form.nodeName" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务ID" prop="taskId">
            <el-input-number v-model.trim="form.taskId" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务PID" prop="taskPid">
            <el-input-number v-model.trim="form.taskPid" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务GID" prop="taskGid">
            <el-input-number v-model.trim="form.taskGid" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人账号" prop="taskUid">
            <el-input v-model.trim="form.taskUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理人" prop="taskUxm">
            <el-input v-model.trim="form.taskUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务时间" prop="taskTime">
            <el-date-picker v-model.trim="form.taskTime" style="width:100%" type="date" clearable value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否新任务" prop="taskIsNew">
            <el-input-number v-model.trim="form.taskIsNew" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际办理人账号" prop="taskDoUid">
            <el-input v-model.trim="form.taskDoUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际办理人" prop="taskDoUxm">
            <el-input v-model.trim="form.taskDoUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理时间" prop="taskDoTime">
            <el-input-number v-model.trim="form.taskDoTime" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="办理意见" prop="taskDoIdea">
            <el-input v-model.trim="form.taskDoIdea" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上一节点名称" prop="lastNodeName">
            <el-input v-model.trim="form.lastNodeName" maxlength="93" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建部门" prop="instCreateDept">
            <el-input v-model.trim="form.instCreateDept" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模板ID" prop="tmplId">
            <el-input v-model.trim="form.tmplId" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实例名称" prop="instName">
            <el-input v-model.trim="form.instName" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实例状态" prop="instState">
            <el-input v-model.trim="form.instState" maxlength="5" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实例密级" prop="instSec">
            <el-input-number v-model.trim="form.instSec" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建人账号" prop="instCreateUid">
            <el-input v-model.trim="form.instCreateUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建人" prop="instCreateUxm">
            <el-input v-model.trim="form.instCreateUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建时间" prop="instCreateTime">
            <el-date-picker v-model.trim="form.instCreateTime" style="width:100%" type="date" clearable value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型1" prop="tmplType1">
            <el-input v-model.trim="form.tmplType1" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型2" prop="tmplType2">
            <el-input v-model.trim="form.tmplType2" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型3" prop="tmplType3">
            <el-input v-model.trim="form.tmplType3" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="tmplType">
            <el-input v-model.trim="form.tmplType" maxlength="61" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务ID" prop="taskId"  style="display:none;">
            <el-input v-model.trim="form.taskId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'viewLeswfe15TaskInstForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>