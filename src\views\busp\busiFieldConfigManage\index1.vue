<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  > 
  <div class="search-container">
    <el-input placeholder="业务列名" v-model="searchForm.bfColumn"  @change="handleSearch" style="width:150px;" />&nbsp;
    <el-input placeholder="业务属性" v-model="searchForm.bfName"  @change="handleSearch" style="width:150px;" />&nbsp;
  </div>

    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="120"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">选择</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,
           busiFieldConfigDoUpdateOrd,
           busiFieldConfigDoReset,
           busiFieldConfigDoDelete,
           busiFieldConfigDoSaveQuote } from '@/api/busp/busiFieldConfig'
  
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  export default {
    name: 'busiFieldConfig1',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
    },
    data() {
      return {
        bfcOrder:0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['业务表名','业务字段名','业务属性名','是否列表列','列表列顺序','列表列宽度'],
        columns: [
                { prop:'bfBmTable'    , label:'业务表名'  , width:'auto' , sortable:false},
                { prop:'bfColumn'     , label:'业务字段名', width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性名', width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfcIslist'    , label:'是否列表列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcLorder'    , label:'列表列顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcListwidth' , label:'列表列宽度', width:'auto' , sortable:false  ,editType:'number'}        
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          sortField:'BFC_LORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'}],
        busiModelRow:{},
        bmId:''
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.listLoading = false
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row,bmId){
        this.busiModelRow = row
        this.searchForm.bfcBmId = row.bmId
        this.bmId = bmId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        row.bmId = this.bmId
        
        const  msg  = await busiFieldConfigDoSaveQuote( row )
        if(msg.code == 200) {
          this.$message({message:'操作成功!',type:'success'})
          this.fetchData()
        }else{
          this.$message({message:msg.msg||'保存操作失败!',type:'warning'})
        } 
      }
    }
  }
</script>