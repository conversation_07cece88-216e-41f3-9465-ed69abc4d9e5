<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input placeholder="属性ID" v-model="tableQueryForm.bfcBfId"  @keyup.enter.native="handleSearch" />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              高级查询
            </el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
              添加
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button icon="el-icon-download" type="primary" @click="handleExportRear" title="导出表格">
          
        </el-button>
        <!--el-button icon="el-icon-download" type="primary" @click="handleExportTmpl">
          导出模板
        </el-button>
        <el-upload
           :show-file-list="false"
           action=""
           :accept="fileAccept"
           auto-upload
           :disabled="fileUploadBtnText == '正在导入'"
           :http-request="uploadFile"
           style="transform: translateY(-5px);margin-right: 10px;"
           >
          <el-button type="primary":icon="uploadBtnIcon">导入</el-button>
        </el-upload-->
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
          title="表格全屏"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          
        </el-button>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="taleCheckList" @change="handleCheckedChange">
            <vab-draggable v-bind="dragOptions" :list="tableColums">
              <div v-for="(item, index) in tableColums" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
              title="可拖拽列设置"
            >
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'
  import axios from 'axios'
  import config from '@/config'
  import store from '@/store'

  export default {
    name: 'busiFieldConfigSearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList,
        fileUploadBtnText: "导入",
        uploadBtnIcon:"el-icon-upload2",
        fileAccept:".xls,.xlsx"
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm)
      },
      handleQuery() {
        this.$emit('handleQuery')
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd')
      },
      clickFullScreen() {
        this.isFullscreen = !this.isFullscreen
        this.$emit("handleHeight",this.isFullscreen)
      },
      handleCheckedChange(val) {
        this.$emit("handleCheckedChange",val)
      },
      // 导出
      handleExportRear() {
        this.$emit("handleExportRear")
      },
      //导出excel模板
      handleExportTmpl() {
        this.$emit("handleExportTmpl")
      },
      //导入
      async uploadFile(param){
        let file = param.file
        let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
        let acceptArr = this.fileAccept+","
        if(acceptArr.indexOf(fileType+",")==-1){
          this.$message({message: `${file.name}文件类型不符，请重新选择${this.fileAccept}格式文件`,type: "warning"})
        }else{
          this.uploadBtnIcon = "el-icon-loading"
          this.fileUploadBtnText = "正在导入"
          var formdata = new FormData()
          formdata.append('file', file)
          formdata.append('fileparam','对应excel列的实体类名称,逗号分隔，具体此参数什么格式内容请与后端商量')
          var url = config.baseURL+"/busi-field-config/upload"
          axios({url,method: 'post',data:formdata,
            headers:{
              'Authorization': store.getters['user/token'],
              'Content-Type':'multipart/form-data'
            }
          }).then(res => {
            if(res.data.code==200){
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$baseMessage('导入成功！', 'success', 'vab-hey-message-success')
              this.$emit("handleImportRear")
              //生成代码是调用index函数，具体可根据实际编写
            }else{
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$baseMessage(res.data.msg, 'error', 'vab-hey-message-error')
              if(res.data.data){
                window.open(config.baseURL+"/"+res.data.data)
              } 
            }
          }).catch(err => {
            this.uploadBtnIcon = "el-icon-upload2"
            this.fileUploadBtnText = "导入"
            this.$baseMessage('导入失败！', 'error', 'vab-hey-message-error')
          })
        }
      }
    },
  }
</script>