<template>
  <el-dropdown @command="handleCommand" @visible-change="handleVisibleChange">
    <span class="avatar-dropdown">
      <el-avatar class="user-avatar" :src="avatar" />
      <div class="user-name">
        <span class="hidden-xs-only">{{ loginUser.oeName }}</span>
        <vab-icon
          class="vab-dropdown"
          :class="{ 'vab-dropdown-active': active }"
          icon="arrow-down-s-line"
        />
      </div>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="modifyInfo" v-if="loginUser.luFlag!='1'">
          <vab-icon icon="user-line" />
          {{ translateTitle('信息维护') }}
        </el-dropdown-item>
        <el-dropdown-item command="modifyPassWord">
          <vab-icon icon="user-line" />
          {{ translateTitle('修改密码') }}
        </el-dropdown-item>
        <el-dropdown-item command="logout">
          <vab-icon icon="logout-circle-r-line" />
          {{ translateTitle('退出登录') }}
        </el-dropdown-item>

      </el-dropdown-menu>
    </template>
    <table-edit ref="edit" :pop-width="popWidth">
      <el-form
        ref="form"
        label-width="80px"
        :model="form"
        slot="form"
        :rules="rules"
      >
        <el-form-item label="原密码" prop="oldPassWord">
          <el-input v-model="form.oldPassWord" type="password"  minlength="6" maxlength="16"/>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassWord">
          <el-input v-model="form.newPassWord" type="password"  minlength="6" maxlength="16" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassWord">
          <el-input v-model="form.confirmPassWord" type="password"  minlength="6" maxlength="16" />
        </el-form-item>
      </el-form>
      <template slot="footerCont">
        <!--el-button @click="close">取 消</el-button-->
        <el-button
          v-loading.fullscreen.lock="fullscreenLoading"
          type="primary"
          @click="save"
        >
          确 定
        </el-button>
      </template>
    </table-edit>

    <table-edit ref="editEmp" :pop-width="popWidth">
      <el-form
        ref="formEmp"
        label-width="80px"
        :model="formEmp"
        slot="form"
        :rules="rulesEmp"
      >
        <el-form-item label="手机号" prop="oePhone">
          <el-input v-model="formEmp.oePhone"  minlength="11" maxlength="30"/>
        </el-form-item>
        <el-form-item label="座机号" prop="oeTele">
          <el-input v-model="formEmp.oeTele" minlength="4" maxlength="20" />
        </el-form-item>
        <el-form-item label="房间号" prop="oeHouse">
          <el-input v-model="formEmp.oeHouse"  minlength="3" maxlength="20" />
        </el-form-item>
      </el-form>
      <template slot="footerCont">
        <el-button @click="closeEmp">取 消</el-button>
        <el-button
          v-loading.fullscreen.lock="fullscreenLoading"
          type="primary"
          @click="saveEmp"
        >
          确 定
        </el-button>
      </template>
    </table-edit>


  </el-dropdown>
</template>

<script>
  import { translateTitle } from '@/utils/i18n'
  import { mapActions, mapGetters } from 'vuex'
  import { toLoginRoute,toLoginRouteSsa } from '@/utils/routes'
  import request from '@/utils/request'
  import TableEdit from '@/views/common/TableEdit.vue'

  import { lesysConsignDoSave } from '@/api/oa/lesysConsign'

  export default {
    name: 'VabAvatar',
    components: {
      TableEdit,
    },
    data() {
      var validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入原密码'))
        }
      }
      var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入新密码'))
        }
      }
      var validatePass3 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'))
        } else if (value !== this.form.newPassWord) {
          callback(new Error('两次输入密码不一致!'))
        }
      }
      return {
        active: false,
        popWidth: '500px',
        fullscreenLoading: false,
        form: {
          oldPassWord: '',
          newPassWord: '',
          confirmPassWord: '',
        },
        rules: {
          oldPassWord: [{ validator: validatePass, trigger: 'blur' }],
          newPassWord: [{ validator: validatePass2, trigger: 'blur' }],
          confirmPassWord: [{ validator: validatePass3, trigger: 'blur' }],
        },
        formEmp: {
          oePhone: '',
          oeTele: '',
          oeHouse: '',
          oeFlag:'0'
        },
        rulesEmp: {
          oePhone: [{ message: "请输入手机号", trigger: 'blur' }],
          oeTele: [{ message: "请输入座机号", trigger: 'blur' }],
          oeHouse: [{ message: "请输入房间号", trigger: 'blur' }]
        },
      }
    },
    computed: {
      ...mapGetters({
        avatar: 'user/avatar',
        username: 'user/username',
        loginUser: 'user/loginUser',
      }),
    },
    created() {
      this.checkUserPass();
    },
    methods: {
      translateTitle,
      ...mapActions({
        _logout: 'user/logout',
      }),
      async checkUserPass(){
        let data = {'luId':this.username,'luFlag':this.loginUser.luFlag};
        let res = await request({
          url: '/lesys-ssa-user/checkPass',
          method: 'POST',
          data,
        })

        if (res.code == '200') {
          if(res.data == '-8' || res.data=='-9'){
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          this.$refs.edit.showEdit1('修改密码')
          }  
        }
      },
      handleCommand(command) {
        switch (command) {
          case 'logout':
            this.logout()
            break
          case 'modifyPassWord':
            this.modifyPassWord()
            break
          case 'modifyInfo':
            this.modifyInfo()
            break
          case 'logout1':
            this.logout1()
            break
        }
      },
      handleVisibleChange(val) {
        this.active = val
      },
      personalCenter() {
        this.$router.push('/setting/personalCenter')
      },
      //修改个人信息
      modifyInfo(){
        if(this.formEmp.oeFlag=='0'){
          this.formEmp.oeHouse = this.loginUser.oeHouse
          this.formEmp.oeTele  = this.loginUser.oeTele
          this.formEmp.oePhone = this.loginUser.oePhone
          this.formEmp.oeFlag = '1'
        }
        this.$refs.editEmp.showEdit('修改信息')  
      },
      closeEmp(){
        this.$refs.editEmp.close()
      },
      async saveEmp(){
        let data = this.formEmp
        data.oeId= this.loginUser.oeId
        let res = await request({
          url: '/lesys-emp-vue/update',
          method: 'POST',
          data,
        })
        if (res.code == '-1') {
          this.$message({
            message: res.data,
            type: 'warning',
          })
        } else {
          this.$message({
            message: '修改成功！',
            type: 'success',
          })
          this.closeEmp()
        }
      },
      // 修改密码
      modifyPassWord() {
        this.$refs.edit.showEdit('修改密码')
      },
      close() {
        this.$refs.form.resetFields()
        this.$refs.edit.close()
      },
      async save() {
        let data = this.form
        data.luFlag = this.loginUser.luFlag
        let res = await request({
          url: '/lesys-ssa-user/updatePass',
          method: 'POST',
          data,
        })
        if (res.code == '-1') {
          this.$message({
            message: res.data,
            type: 'warning',
          })
        } else {
          this.$message({
            message: '密码修改成功！',
            type: 'success',
          })
          this.close()
        }
      },
      async logout() {
        let luflag = this.loginUser.luFlag
        await this._logout()
        //await this.$router.push(toLoginRoute(this.$route.fullPath))
        //去掉回到原页面
        if(luflag=='1'){
          await this.$router.push(toLoginRouteSsa())
        }else{
          await this.$router.push(toLoginRoute())
        }
        
      },
      async logout1() {
        
      },
      close1() {
        
      },
      async sure(){
        
      },
      async dbsure(row){
        if(row!=-1){
        }
      }
    },
  }
</script>

<style lang="scss" scoped>
  .avatar-dropdown {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    justify-items: center;
  
    .user-avatar {
      width: 40px;
      height: 40px;
      margin-left: 15px;
      cursor: pointer;
      border-radius: 50%;
    }

    .user-name {
      position: relative;
      display: flex;
      align-content: center;
      align-items: center;
      height: 40px;
      margin-left: 6px;
      line-height: 40px;
      cursor: pointer;

      [class*='ri-'] {
        margin-left: 0 !important;
      }
    }
  }
</style>
