<template>
  <div class="tableEdit-container">
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="dialogFormVisible"
      v-if="dialogFormVisible"
      :width="popWidth"
      @close="close"
      style="overflow-y: auto;"
      :close-on-click-modal="false"
      class="showAll_dialog"
      :destroy-on-close="true"
    >
      <slot name="form" />
      <slot slot="footer" name="footerCont"></slot>
    </el-dialog>
  </div>
</template>

<script>

  export default {
    name: 'TableSearch',
    props: {
      popWidth: {
        type: String,
        default: '960px'
      }
    },
    data() {
      return {
        title: '',
        dialogFormVisible: false
      }
    },
    methods: {
      showQuery(type) {
        this.title = type;
        this.dialogFormVisible = true
      },
      close() {
        this.dialogFormVisible = false
      },
    }
  }
</script>
