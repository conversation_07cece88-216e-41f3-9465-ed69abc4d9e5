<template>
  <!--一栏布局 -->
  <div
    class="vab-layout-onecolumn"
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
  > 
    <vab-onecolumn-bar />
    <div
      class="vab-main"
      :class="{
        ['vab-main-' + theme.columnStyle]: true,
        'is-collapse-main': collapse,
      }"
    >
      <div
        class="vab-layout-header"
        :class="{
          'fixed-header': fixedHeader,
        }"
      >
        <vab-onenav />
        <vab-tabs v-show="showTabs" style="width: calc(100% - 64px);left: 64px;"/>
      </div>
      <vab-app-main/>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    name: 'VabLayoutOnecolumn',
    props: {
      collapse: {
        type: Boolean,
        default() {
          return false
        },
      },
      fixedHeader: {
        type: Boolean,
        default() {
          return true
        },
      },
      showTabs: {
        type: <PERSON><PERSON>an,
        default() {
          return true
        },
      },
    },
    computed: {
      ...mapGetters({
        theme: 'settings/theme',
      }),
    },
  }
</script>

<style lang="scss" scoped>
  .vab-layout-onecolumn {
    .vab-main {
      margin-left: 64px;
      .fixed-header {
        left: 0;
        width: 100%;
      }

      &.is-collapse-main {
        &.vab-main-horizontal {
          margin-left: $base-left-menu-width-min * 1.3;

          :deep() {
            .fixed-header {
              left: 0;
              width: 100%;
            }
          }
        }
      }
    }
  }
</style>
