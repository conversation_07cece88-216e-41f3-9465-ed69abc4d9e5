<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-row :gutter="20">
      <el-col :span="6" >
        <busiModelSimple @busiModelRowSelect="busiModelRowSelect" :bmSign="bdSsign">
          
        </busiModelSimple>
      </el-col>
      <el-col :span="18">
        <busiField1 ref="busiField1">
          
        </busiField1>
      </el-col>
    </el-row>  
  </div>
</template>

<script>
  import busiModelSimple from '@/views/busp/busiModel/indexSimple.vue'
  import busiField1 from './index1.vue'

  export default {
    name: 'busiModelFieldSel',
    props: {
      bdSsign: {
        type:String
      },
      busiDisplayRow:{
        type:Object,
        default:{}
      }
    },
    components: {
      busiModelSimple,
      busiField1
    },
    data() {
      return {
        isFullscreen:false
      }
    },
    computed: {

    },
    created() {
      
    },
    methods: {
      init(row){

      },
      busiModelRowSelect(row){
        this.$refs.busiField1.init(row,this.busiDisplayRow)
      }  
    }
  }
</script>