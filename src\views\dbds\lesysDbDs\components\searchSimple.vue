<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input placeholder="名称" v-model="tableQueryForm.ldName"  @keyup.enter.native="handleSearch" />
          </el-form-item>

        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <!--el-button
            icon="el-icon-search"
            type="primary"
            @click="handleQuery"
          >
            高级查询
          </el-button-->
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'

  export default {
    name: 'lesysDbDsSearchSimple',
    props: {
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm)
      },
      handleQuery() {
        this.$emit('handleQuery')
      }
    },
  }
</script>