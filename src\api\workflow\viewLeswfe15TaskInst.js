import request from '@/utils/request'

//列表分页加载数据
export function viewLeswfe15TaskInstGetList(params) {
  return request({
    url: '/view-leswfe15-task-inst/vPageList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function viewLeswfe15TaskInstDoSave(data) {
  return request({
    url: '/view-leswfe15-task-inst/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function viewLeswfe15TaskInstDoUpdate(data) {
  return request({
    url: '/view-leswfe15-task-inst/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function viewLeswfe15TaskInstDoSaveOrUpd(data) {
  return request({
    url: '/view-leswfe15-task-inst/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function viewLeswfe15TaskInstDoSaveOrUpdLog(data) {
  return request({
    url: '/view-leswfe15-task-inst/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function viewLeswfe15TaskInstDoDelete(data) {
  return request({
    url: '/view-leswfe15-task-inst/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function viewLeswfe15TaskInstDoDeleteLog(data) {
  return request({
    url: '/view-leswfe15-task-inst/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function viewLeswfe15TaskInstDoDeleteELog(data) {
  return request({
    url: '/view-leswfe15-task-inst/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function viewLeswfe15TaskInstGetTreeList(params){
  return request({
    url: '/view-leswfe15-task-inst/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function viewLeswfe15TaskInstDoExport(data) {
  return request({
    url: '/view-leswfe15-task-inst/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function viewLeswfe15TaskInstGetStat(params) {
  return request({
    url: '/view-leswfe15-task-inst/vStat',
    method: 'get',
    params,
  })
}

//列表分页加载任务跟踪
export function viewLeswfe15TaskInstGetTrackList(params) {
  return request({
    url: '/view-leswfe15-task-inst/trackList',
    method: 'get',
    params,
  })
}
