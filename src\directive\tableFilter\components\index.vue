<template>
  <div>
    <ul>
      <li class="tableFilterLi">
        <i class="el-icon-s-unfold"></i>
        <el-button type="text" @click="handleOrder('ascending')">
          升序排列
        </el-button>
      </li>
      <li class="tableFilterLi">
        <i class="el-icon-s-fold"></i>
        <el-button type="text" @click="handleOrder('descending')">
          降序排列
        </el-button>
      </li>
      <el-popover placement="right" trigger="click" width="250">
        <FilterData
          ref="FilterData"
          :select-item-list="checkList"
          @selectedList="selectedList"
        >
          <template #buttonSlot>
            <el-button @click="sure('search')" type="primary">确定</el-button>
          </template>
        </FilterData>
        <li class="filterData tableFilterLi" slot="reference">
          <i class="el-icon-s-grid"></i>
          筛选数据
          <i class="el-icon-arrow-right"></i>
        </li>
      </el-popover>

      <el-popover placement="right" trigger="click" :width="dynamicWidth()">
        <FilterCondition
          v-if="!isShowDate()"
          ref="FilterCondition"
          :name="columnVueComponent.label"
          @keywordsChanged="keywordsChanged"
        >
          <template #buttonSlot>
            <el-button @click="sure('search')"type="primary">确定</el-button>
          </template>
        </FilterCondition>
        <FilterDate v-else ref="FilterDate" @dateChanged="dateChanged">
          <template #buttonSlot>
            <el-button @click="sure('search')" type="primary">确定</el-button>
          </template>
        </FilterDate>
        <li class="filterData tableFilterLi" slot="reference">
          <i class="el-icon-s-grid"></i>
          筛选条件
          <i class="el-icon-arrow-right"></i>
        </li>
      </el-popover>
    </ul>
  </div>
</template>
<script>
  import FilterData from './FilterData.vue'
  import FilterCondition from './FilterCondition.vue'
  import FilterDate from './FilterDate.vue'
  export default {
    name: 'TableFilter',
    components: { FilterData, FilterCondition, FilterDate },
    props: {
      vm: {
        type: Object,
        default: () => {},
      },
      columnVueComponent: {
        type: Object,
        default: () => {},
      },
      handle: {
        type: Object,
        default: () => ({
          fn: 'search',
          ref: 'table',
          tableList: 'tableData',
          filterParmas: 'filterParmas',
          getDirectiveParmas: 'getDirectiveParmas',
        }),
      },
    },

    data() {
      return {
        checkList: [],
        selectList: [],
        ascending: [],
        descending: [],
        filterParmas: {},
        keyWords: '',
        date: '',
        visible: false,
      }
    },
    watch: {
      columnVueComponent: {
        handler: function () {
          this.checkList = Array.from(
            new Set(
              this.vm[this.handle.tableList].map(
                (item) => item[this.columnVueComponent.prop]
              )
            )
          )
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {
      this.$baseEventBus.$on('reset', () => {
        this.$nextTick(() => {
          this.$refs.FilterCondition
            ? this.$refs.FilterCondition.clear()
            : void 0
          this.$refs.FilterDate ? this.$refs.FilterDate.clear() : void 0

          this.$refs.FilterData ? this.$refs.FilterData.clear() : void 0

          this.selectList = []
          this.filterParmas = {}
          this.keyWords = ''
          this.date = ''
          this.sure('clear')
        })
      })
    },
    methods: {
      selectedList(data) {
        this.selectList = data
        this.date = ''
        this.keyWords = []
        const params = {}
        params[this.columnVueComponent.prop] = this.selectList.join(', ')
        this.formatterParams(params)
        this.$nextTick(() => {
          this.$refs.FilterCondition
            ? this.$refs.FilterCondition.clear()
            : void 0
          this.$refs.FilterDate ? this.$refs.FilterDate.clear() : void 0
        })
      },
      keywordsChanged(data) {
        this.keyWords = data
        this.$nextTick(() => {
          this.$refs.FilterData.clear()
        })
        this.selectList = []
        const params = {}
        params[this.columnVueComponent.prop] = this.keyWords
        this.formatterParams(params)
      },
      dateChanged(data) {
        this.date = data
        const params = {}
        params[this.columnVueComponent.prop] = this.date
        this.formatterParams(params)
      },
      handleOrder(type) {
        if (type === 'ascending') {
          if (!this.ascending.includes(this.columnVueComponent.prop)) {
            this.ascending.push(this.columnVueComponent.prop)
          }
          this.descending = this.descending.filter(
            (item) => item !== this.columnVueComponent.prop
          )
        } else {
          if (!this.descending.includes(this.columnVueComponent.prop)) {
            this.descending.push(this.columnVueComponent.prop)
          }
          this.ascending = this.ascending.filter(
            (item) => item !== this.columnVueComponent.prop
          )
        }
        this.filterParmas['ascending'] = this.ascending.join(',')
        this.filterParmas['descending'] = this.descending.join(',')
        // this.handleSearch()
      },
      isShowDate() {
        const arr = ['日期', '时间', '年份', '月份']
        return arr.filter((item) =>
          this.columnVueComponent.label.includes(item)
        ).length
      },

      formatterParams(data) {
        this.filterParmas = Object.assign({}, this.filterParmas, data)
      },
      sure(type) {
        if (type === 'search') {
          this.vm[this.handle.getDirectiveParmas](this.filterParmas)
        } else {
          this.vm[this.handle.getDirectiveParmas]('clear')
        }
      },

      handleSearch() {
        this.vm[this.handle.fn]()
      },
      dynamicWidth() {
        return this.isShowDate() ? 300 : 400
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-button--text {
    color: black !important;
  }
  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }
  li {
    list-style: none;
    text-align: left;
    padding-left: 10px;
    margin-top: 5px;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    &:nth-child(3) {
      position: relative;
      background-color: #13ce66;
    }
    &:hover {
      background-color: #a29d9d;
      color: black;
    }
  }
  ul {
    box-shadow: 0 0.0625rem 0.25rem rgba(0, 21, 41, 0.08);
    margin: 0;
    padding: 0;
    width: 150px;
    height: 150px;
    background-color: #ffffff;
    border-radius: 5px;
  }
  .filterData {
    position: relative;
    .el-icon-arrow-right {
      font-size: 16px;
      position: absolute;
      top: 7px;
      right: 5px;
    }
  }

  .el-icon-s-unfold {
    font-size: 16px;
    display: inline-block;
    transform: rotate(-90deg);
  }
  .el-icon-s-fold {
    font-size: 16px;
    display: inline-block;
    transform: rotate(-90deg);
  }

  .el-icon-s-grid {
    font-size: 16px;
  }
</style>
