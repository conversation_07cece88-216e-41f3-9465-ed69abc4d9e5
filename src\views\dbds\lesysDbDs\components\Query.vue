<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="12">
          <el-form-item label="名称" prop="ldName">
            <el-input v-model.trim="form.ldName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据库类型" prop="ldType">
            <el-select v-model.trim="form.ldType"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.ldType" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ip" prop="ldHost">
            <el-input v-model.trim="form.ldHost" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户" prop="ldUsername">
            <el-input v-model.trim="form.ldUsername" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="ldPassword">
            <el-input v-model.trim="form.ldPassword" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实例" prop="ldDb">
            <el-input v-model.trim="form.ldDb" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="端口" prop="ldPort">
            <el-input v-model.trim="form.ldPort" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="简称" prop="ldShort">
            <el-input v-model.trim="form.ldShort" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="url" prop="ldUrl">
            <el-input v-model.trim="form.ldUrl" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模式名" prop="ldSchema">
            <el-input v-model.trim="form.ldSchema" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="链接串" prop="ldPath">
            <el-input v-model.trim="form.ldPath" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据源类型" prop="ldSort">
            <el-select v-model.trim="form.ldSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.ldSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'lesysDbDsQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          ldSort:[{"label":"内置","value":"内置"},{"label":"外置","value":"外置"}]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>