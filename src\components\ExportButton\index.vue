<!--导出按钮组件-->
<template>
  <div class="export-button-group">
    <!-- 单个导出按钮 -->
    <el-button
      v-if="!showDropdown"
      :type="buttonType"
      :size="buttonSize"
      :icon="buttonIcon"
      :loading="exporting"
      @click="handleExport"
      :disabled="disabled"
    >
      {{ buttonText }}
    </el-button>

    <!-- 下拉菜单导出按钮 -->
    <el-dropdown v-else @command="handleDropdownCommand" :disabled="disabled">
      <el-button
        :type="buttonType"
        :size="buttonSize"
        :loading="exporting"
      >
        {{ buttonText }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="page" icon="el-icon-download">导出当前数据</el-dropdown-item>
        <el-dropdown-item command="all" icon="el-icon-download">导出全部数据</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { exportRearEnd } from '@/api/exportExcel'
import { baseURL } from '@/config'

export default {
  name: 'ExportButton',
  props: {
    // 导出API函数
    exportApi: {
      type: Function,
      required: true
    },
    // 表格选择器
    tableSelector: {
      type: String,
      required: true
    },
    // 查询表单数据
    queryForm: {
      type: Object,
      default: () => ({})
    },
    // 导出文件名
    fileName: {
      type: String,
      default: '导出数据.xls'
    },
    // Excel标题
    excelTitle: {
      type: String,
      default: '导出数据'
    },
    // 日期字段配置
    dateFields: {
      type: Object,
      default: () => ({})
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'success'
    },
    // 按钮大小
    buttonSize: {
      type: String,
      default: 'small'
    },
    // 按钮图标
    buttonIcon: {
      type: String,
      default: 'el-icon-download'
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: '导出'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示序号
    showNumber: {
      type: Boolean,
      default: true
    },
    // 额外的导出参数
    extraParams: {
      type: Object,
      default: () => ({})
    },
    // 是否显示下拉菜单
    showDropdown: {
      type: Boolean,
      default: false
    },
    // 全部数据导出时的页面大小
    allDataPageSize: {
      type: Number,
      default: 10000
    }
  },
  data() {
    return {
      exporting: false
    }
  },
  methods: {
    // 下拉菜单命令处理
    handleDropdownCommand(command) {
      if (command === 'page') {
        this.handleExportPage()
      } else if (command === 'all') {
        this.handleExportAll()
      }
    },

    // 导出数据（兼容单按钮模式）
    async handleExport() {
      if (this.showDropdown) {
        // 如果是下拉模式，默认导出当前数据
        this.handleExportPage()
      } else {
        // 单按钮模式，导出当前数据
        this.handleExportPage()
      }
    },

    // 导出当前页数据
    async handleExportPage() {
      if (this.exporting) return

      this.exporting = true
      try {
        // 构建导出参数
        let params = {
          dataFields: this.dateFields,
          fileName: this.fileName,
          isnumber: this.showNumber,
          excelTitle: this.excelTitle,
          queryForm: this.queryForm,
          ...this.extraParams
        }

        // 使用exportRearEnd处理表格数据
        let exportData = exportRearEnd(this.tableSelector, params)

        // 调用导出API
        const response = await this.exportApi(exportData)

        if (response && response.msg) {
          // 打开下载链接
          window.open(baseURL + "/" + response.msg)
          this.$message.success('当前数据导出成功')
          this.$emit('export-success', response)
        } else {
          throw new Error('导出响应格式错误')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error(error.message || '导出失败，请重试')
        this.$emit('export-error', error)
      } finally {
        this.exporting = false
      }
    },

    // 导出全部数据
    async handleExportAll() {
      if (this.exporting) return

      this.exporting = true
      try {
        // 构建全部数据导出参数
        let allDataQueryForm = {
          ...this.queryForm,
          pageNo: 1,
          pageSize: this.allDataPageSize
        }

        let params = {
          dataFields: this.dateFields,
          fileName: this.fileName.replace('.xls', '_全部数据.xls'),
          isnumber: this.showNumber,
          excelTitle: this.excelTitle + '(全部数据)',
          queryForm: allDataQueryForm,
          ...this.extraParams
        }

        // 使用exportRearEnd处理表格数据
        let exportData = exportRearEnd(this.tableSelector, params)

        // 调用导出API
        const response = await this.exportApi(exportData)

        if (response && response.msg) {
          // 打开下载链接
          window.open(baseURL + "/" + response.msg)
          this.$message.success('全部数据导出成功')
          this.$emit('export-all-success', response)
        } else {
          throw new Error('全部数据导出响应格式错误')
        }
      } catch (error) {
        console.error('全部数据导出失败:', error)
        this.$message.error(error.message || '全部数据导出失败，请重试')
        this.$emit('export-all-error', error)
      } finally {
        this.exporting = false
      }
    }
  }
}
</script>

<style scoped>
.export-button-group {
  display: inline-block;
}

.export-button-group .el-dropdown {
  vertical-align: top;
}

.export-button-group .el-button {
  margin-right: 0;
}

/* 下拉菜单项图标样式 */
.export-button-group .el-dropdown-menu__item {
  display: flex;
  align-items: center;
}

.export-button-group .el-dropdown-menu__item i {
  margin-right: 8px;
  width: 14px;
}
</style>
