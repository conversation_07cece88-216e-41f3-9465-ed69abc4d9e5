<template>
  <div class="tableEdit-container">
    <el-dialog
      :id="mathRandomId"
      :ref="mathRandomId"
      append-to-body
      :title="title"
      :show-close="sclose"
      :visible="dialogFormVisible"
      v-if="dialogFormVisible"
      :width="popWidth"
      @close="close"
      @opened=opened
      style="overflow-y: auto;"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :class="[fullscreen ? 'el-dialog-fullscreen-self' : '', 'showAll_dialog']"
      :fullscreen="fullscreen"
      :modal="modal"
    >
      <slot name="form" />
      <slot slot="footer" name="footerCont"></slot>
    </el-dialog>
  </div>
</template>

<script>

  export default {
    name: 'TableEdit',
    props: {
      popWidth: {
        type: String,
        default: '960px'
      },
      popHeight: {
        type:String,
        default: '0px'
      },
      fullscreen: {
        type:Boolean,
        default:false
      },
      modal:{
        type: Boolean,
        default:true
      }
    },
    data() {
      return {
        mathRandomId:Math.random(1,1000),
        title: '',
        sclose: true,
        dialogFormVisible: false,
        
      }
    },
    mounted(){
      
    },
    methods: {
      showEdit(type) {
        this.title = type;
        this.dialogFormVisible = true
      },

      showEdit1(type) {
        this.title = type;
        this.sclose = false;
        this.dialogFormVisible = true
      },

      close() {
        this.dialogFormVisible = false
      },
      opened(){
        this.$nextTick(()=> {
          //if(this.popHeight !== '0px') {
          //  const elDialogBodyDomList = this.$refs[this.mathRandomId].$el.querySelector('.el-dialog__body')
          //  elDialogBodyDomList.style.height = this.popHeight
          //}
        })
      }
    }
  }
</script>
<!--style lang="scss" scoped>
 ::v-deep  .el-dialog__body { 
  max-height:1000px!important;
 }
</style-->
