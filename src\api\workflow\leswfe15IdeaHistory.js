import request from '@/utils/request'

//列表分页加载数据
export function leswfe15IdeaHistoryGetList(params) {
  return request({
    url: '/leswfe15-idea-history/vPage',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function leswfe15IdeaHistoryDoSave(data) {
  return request({
    url: '/leswfe15-idea-history/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function leswfe15IdeaHistoryDoUpdate(data) {
  return request({
    url: '/leswfe15-idea-history/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function leswfe15IdeaHistoryDoSaveOrUpd(data) {
  return request({
    url: '/leswfe15-idea-history/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function leswfe15IdeaHistoryDoSaveOrUpdLog(data) {
  return request({
    url: '/leswfe15-idea-history/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function leswfe15IdeaHistoryDoDelete(data) {
  return request({
    url: '/leswfe15-idea-history/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function leswfe15IdeaHistoryDoDeleteLog(data) {
  return request({
    url: '/leswfe15-idea-history/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function leswfe15IdeaHistoryDoDeleteELog(data) {
  return request({
    url: '/leswfe15-idea-history/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function leswfe15IdeaHistoryGetTreeList(params){
  return request({
    url: '/leswfe15-idea-history/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function leswfe15IdeaHistoryDoExport(data) {
  return request({
    url: '/leswfe15-idea-history/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function leswfe15IdeaHistoryGetStat(params) {
  return request({
    url: '/leswfe15-idea-history/vStat',
    method: 'get',
    params,
  })
}
