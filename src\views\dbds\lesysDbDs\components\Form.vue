<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="12">
          <el-form-item label="名称" prop="ldName">
            <el-input v-model.trim="tableForm.ldName" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据库类型" prop="ldType">
            <el-select v-model.trim="tableForm.ldType"   clearable   style="width:100%" @change="changeType">
              <el-option v-for="item in optionsData.ldType" :key="item.ldtType" :label="item.ldtType" :value="item.ldtType"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="简称" prop="ldShort">
            <el-input v-model.trim="tableForm.ldShort" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据源类型" prop="ldSort">
            <el-select v-model.trim="tableForm.ldSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.ldSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ip" prop="ldHost">
            <el-input v-model.trim="tableForm.ldHost" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="端口" prop="ldPort">
            <el-input v-model.trim="tableForm.ldPort" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户" prop="ldUsername">
            <el-input v-model.trim="tableForm.ldUsername" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="ldPassword">
            <el-input v-model.trim="tableForm.ldPassword" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实例" prop="ldDb">
            <el-input v-model.trim="tableForm.ldDb" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模式名" prop="ldSchema">
            <el-input v-model.trim="tableForm.ldSchema" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="url" prop="ldUrl">
            <el-input v-model.trim="tableForm.ldUrl" maxlength="300" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="ldId"  style="display:none;">
            <el-input v-model.trim="tableForm.ldId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { lesysDbDsTypeGetList } from '@/api/dbds/lesysDbDsType'
  export default {
    name: 'lesysDbDsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          ldSort:[{"label":"内置","value":"内置"},{"label":"外置","value":"外置"}],
          ldType:[]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
      
    },
    methods: {
      async getSelectOptions(){
        const msg = await lesysDbDsTypeGetList({})
        if(msg.code==200){
          this.optionsData.ldType = msg.data
          this.changeType(this.form.ldType)
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      },
      changeType(val){
        for(var i=0;i<this.optionsData.ldType.length;i++){
          if(this.optionsData.ldType[i].ldtType==val){
            this.tableForm.ldUrl = this.optionsData.ldType[i].ldtUrltmpl
            break
          }
        }
      }

    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>