<!--园区资产详情弹窗组件-->
<template>
  <el-dialog title="园区资产详情" :visible.sync="visibleShow" width="80%" append-to-body>
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产编号：</span>
              <span class="detail-value">{{ detailData.zpkAssetNumber || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产名称：</span>
              <span class="detail-value">{{ detailData.zpkAssetName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产类型：</span>
              <span class="detail-value">{{ detailData.zpkAssetType || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">现状用途：</span>
              <span class="detail-value">{{ detailData.zpkCurrentUsageDescription || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">所在地区：</span>
              <span class="detail-value">{{ detailData.zpkLocationProvinceCityDistrict || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">具体位置：</span>
              <span class="detail-value">{{ detailData.zpkSpecificLocation || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">占地面积：</span>
              <span class="detail-value">{{ detailData.zpkTotalArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">原值：</span>
              <span class="detail-value">{{ detailData.zpkOriginalValue || '-' }}万元</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">净值：</span>
              <span class="detail-value">{{ detailData.zpkNetValue || '-' }}万元</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">主要经营方向：</span>
              <span class="detail-value">{{ detailData.zpkMainBusinessDirection || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">境内/境外：</span>
              <span class="detail-value">{{ detailData.zpkDomesticOrForeign || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否两非资产：</span>
              <span class="detail-value">{{ detailData.zpkIsNonCoreAsset || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否存在纠纷：</span>
              <span class="detail-value">{{ detailData.zpkHasDispute || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否存在抵押：</span>
              <span class="detail-value">{{ detailData.zpkHasMortgage || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系人：</span>
              <span class="detail-value">{{ detailData.zpkOperator || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系电话：</span>
              <span class="detail-value">{{ detailData.zpkOperatorContact || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门负责人：</span>
              <span class="detail-value">{{ detailData.zpkDepartmentLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门电话：</span>
              <span class="detail-value">{{ detailData.zpkDepartmentLeaderContact || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分公司负责人：</span>
              <span class="detail-value">{{ detailData.zpkCompanyLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分公司电话：</span>
              <span class="detail-value">{{ detailData.zpkCompanyLeaderContact || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">备注：</span>
              <span class="detail-value">{{ detailData.zpkRemarks || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="面积信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">科研办公面积：</span>
              <span class="detail-value">{{ detailData.zpkResearchOfficeArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">商业面积：</span>
              <span class="detail-value">{{ detailData.zpkCommercialArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">住宅面积：</span>
              <span class="detail-value">{{ detailData.zpkResidentialArea || '-' }}㎡</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">工业面积：</span>
              <span class="detail-value">{{ detailData.zpkIndustrialArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">出租面积：</span>
              <span class="detail-value">{{ detailData.zpkRentalArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">自用面积：</span>
              <span class="detail-value">{{ detailData.zpkSelfUseArea || '-' }}㎡</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">闲置面积：</span>
              <span class="detail-value">{{ detailData.zpkIdleArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">其他面积：</span>
              <span class="detail-value">{{ detailData.zpkOtherArea || '-' }}㎡</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">已抵押面积：</span>
              <span class="detail-value">{{ detailData.zpkMortgagedArea || '-' }}㎡</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="系统信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ detailData.zpkCreatedTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value">{{ detailData.zpkCreatedBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">更新时间：</span>
              <span class="detail-value">{{ detailData.zpkUpdatedTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">更新人：</span>
              <span class="detail-value">{{ detailData.zpkUpdatedBy || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import { getParkDetail } from '@/api/park'
export default {
  data () {
    return {
      visibleShow: false,
      detailData: {}
    }
  },
  methods: {
    showDialog (row) {
      getParkDetail(row.zpkId).then(response => {
        if (response && response.data) {
          this.detailData = response.data
          this.visibleShow = true
        }
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },

    closeDialog () {
      // 手动关闭对话框
      this.visibleShow = false
    },

  }
}
</script>

<style scoped>
.detail-item {
  margin-bottom: 15px;
  line-height: 24px;
}
.detail-label {
  display: inline-block;
  min-width: 120px;
  color: #606266;
  font-weight: bold;
}
.detail-value {
  color: #303133;
}
</style>
