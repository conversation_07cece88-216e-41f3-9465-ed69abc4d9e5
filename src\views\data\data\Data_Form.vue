<template>
  <div class="Form-container">
   <el-form ref="form" label-width="160px" :model="tableForm" :rules="tableRules">
      <el-row :gutter="20" v-for="(item0, index0) in formList" :key="index0">
        <el-col :span="24/formCols" v-for="(item, index) in item0" :key="item.bfcId">
          <el-form-item
            v-if="item.bfIspk == '是' || item.bfcEdittype == 'HIDDEN'"
            :label="item.bfName"
            :prop="item.bfField"
            style="display: none"
          >
            <el-input
              v-if="item.bfIspk == '是' || item.bfcEdittype == 'HIDDEN'"
              v-model.trim="tableForm[item.bfField]"
              type="hidden"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="item.bfIspk == '否' && item.bfcEdittype != 'HIDDEN'"
            :label="item.bfName"
            :prop="item.bfField"
          >
            <el-input
              v-if="item.bfcEdittype == 'TEXT'"
              v-model.trim="tableForm[item.bfField]"
              :maxlength="item.bfcMaxl"
              show-word-limit
              clearable
              :placeholder="'请输入' + item.bfName"
              :disabled="item.bfcDisabled== '是'"
            ></el-input>

            <el-input-number
              v-if="item.bfcEdittype == 'NUMBER'"
              v-model.trim="tableForm[item.bfField]"
              style="width: 100%"
              :placeholder="'请输入' + item.bfName"
              :disabled="item.bfcDisabled== '是'"
            ></el-input-number>

            <el-date-picker
              style="width: 100%"
              v-if="
                item.bfcEdittype == 'DATE' || item.bfcEdittype == 'TIMESTAMP'
              "
              v-model.trim="tableForm[item.bfField]"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              :placeholder="'请选择' + item.bfName"
              :disabled="item.bfcDisabled== '是'"
            ></el-date-picker>

            <el-select
              v-if="
                item.bfcEdittype == 'TEXT-SELECT' ||
                item.bfcEdittype == 'TEXT-SELECTS'
              "
              v-model.trim="tableForm[item.bfField]"
              style="width: 100%"
              :placeholder="'请选择' + item.bfName"
              :multiple="item.bfcEdittype == 'TEXT-SELECTS'"
              :disabled="item.bfcDisabled== '是'"
              @change="selectchange($event,item.bfField)"
            >
              <el-option
                v-for="item in optionsData[item.bfField]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>

            <el-input
              v-if="item.bfcEdittype == 'TEXTS'"
              v-model.trim="tableForm[item.bfField]"
              :maxlength="item.bfcMaxl"
              :disabled="item.bfcDisabled== '是'"
              show-word-limit
              clearable
              type="textarea"
              :placeholder="'请输入' + item.bfName"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { getOptionsLpvName,getOptionsLpvName2 } from '@/api/busp/busiUtil'
  import Vue from 'vue'
  export default {
    name: 'MasterForm',
    props: {
      rules: {
        type: Object,
        require: true,
      },
      form: {
        type: Object,
        require: true,
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true,
      },
      formList: {
        type: Array,
        require: true,
      },
      formOptionData: {
        type: Object,
      },
      formCols: {
        type: Number,
        default: 2
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData: this.formOptionData,
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectData()
    },
    methods: {
      async getSelectData() {
        this.optionsData = await getOptionsLpvName2(this.formList)
        //重置校验规则
        for (let i = 0; i < this.formList.length; i++) {
          for (let j=0; j < this.formList[i].length; j++) {
            if (
              this.formList[i][j].bfcEdittype == 'TEXT-SELECT' ||
              this.formList[i][j].bfcEdittype == 'TEXT-SELECTS' ||
              this.formList[i][j].bfcEdittype == 'TEXT-RADIO' ||
              this.formList[i][j].bfcEdittype == 'TEXT-CHECKBOX'
            ) {
              this.selectchange(this.tableForm[this.formList[i][j].bfField],this.formList[i][j].bfField)
            }
          }
        }
      },
      selectchange(val,field){
        
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      },
    },
  }
</script>
