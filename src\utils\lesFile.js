import axios from 'axios'
import { Message } from 'element-ui'
import { baseURL, lesFileSaveSec } from '@/config'

//图片格式
let _LESFILE_OLEIMG_EXTS =
  '*.bmp; *.jpg; *.png; *.jpeg; *.tif; *.gif; *.pcx; *.tga; *.exif; *.fpx; *.svg; *.psd; *.cdr; *.pcd; *.dxf; *.ufo; *.eps; *.ai; *.raw; *.wmf; *.webp'

function _LesFile_IsOleImgFile(ext) {
  return _LESFILE_OLEIMG_EXTS.indexOf(ext) >= 0
}

//pdf文件格式
let _LESFILE_OLEPDF_EXTS = '*.pdf; *.tif; *.tiff'

function _LesFile_IsOlePdfFile(ext) {
  return _LESFILE_OLEPDF_EXTS.indexOf(ext) >= 0
}

//office文件格式
let _LESFILE_OLEOFFICE_EXTS =
  '*.doc; *.docx; *.xls; *.xlsx; *.ppt; *.pptx; *.pdf; '

function _LesFile_IsOleOfficeFile(ext) {
  return _LESFILE_OLEOFFICE_EXTS.indexOf(ext) >= 0
}

let _LesFile_oth_imgs_jsp = '/FILE/LesSystem/LesFileApi/oth_imgs.jsp'
let _LesFile_oth_pdfs_jsp = '/FILE/LesSystem/LesFileApi/oth_pdfs.jsp'
let _LesFile_oth_office_jsp = '/FILE/LesSystem/LesAspose/FileView.jsp'

export function getFilePath(FileID) {
  return '/FILE/_LesFileIdStorageDir_/'+FileID.substring(0,1)+'/'+FileID.substring(1,2)+'/'+FileID.substring(2,3)+'/'+FileID;
}

function LesFile_View_File(FileID) {
  let url = '/FILE/LesFileRpcApiService?cmd=getinfo&file_id=' + FileID
  let durl = '/FILE/FileDownloadService?file_isdecode=1&file_id=' + FileID
  axios
    .post(url)
    .then((res) => {
      if (res.status == 200) {
        if (res.data['FILE_NAME']) {
          let ext = res.data['FILE_NAME'].toLowerCase()
          ext = '*.' + ext.split('.').pop()
          if (_LesFile_IsOleImgFile(ext)) {
            window.open(
              _LesFile_oth_imgs_jsp +
                '?file_id=' +
                FileID +
                '&file_sec=' +
                lesFileSaveSec
            )
          } else if(_LesFile_IsOlePdfFile(ext)){
            window.open(_LesFile_oth_pdfs_jsp+"?file_id="+FileID+"&file_sec="+lesFileSaveSec,1800,1000,0,0);
          }
          
          else if (_LesFile_IsOleOfficeFile(ext)) {
            //aspose在线查看
            //window.open(
            //  _LesFile_oth_office_jsp + '?fileId=' + FileID + '&ext=' + ext
            //)
            Message({
              message: '该格式文件不支持在线查看，请下载',
              type: 'warning',
            })
          } else {
            Message({
              message: '该格式文件不支持在线查看，请下载',
              type: 'warning',
            })
          }
          /*
        else if(_LesFile_IsOlePdfFile(ext)){
          window.open(_LesFile_oth_pdfs_jsp+"?file_id="+FileID+"&file_sec="+lesFileSaveSec,1800,1000,0,0);
        }else{
          Message({message: '该格式文件不支持在线查看，请下载',type: 'warning'});
        }*/
        } else {
          Message({
            message: '无效的文件id或者文件已经不存在',
            type: 'warning',
          })
        }
      } else {
        Message({ message: '无效的文件id或者文件已经不存在', type: 'warning' })
      }
    })
    .catch((err) => {
      Message({ message: '无效的文件id或者文件已经不存在', type: 'warning' })
    })
}

function LesFile_Download(FileID) {
  let url = '/FILE/LesFileRpcApiService?cmd=getinfo&file_id=' + FileID
  let durl = '/FILE/FileDownloadService?file_isdecode='+lesFileSaveSec+'&file_id=' + FileID
  axios
    .post(url)
    .then((res) => {
      if (res.status == 200) {
        if (res.data['FILE_URL']) {
          //window.open(durl)
          window.location.href = durl
        } else {
          Message({
            message: '无效的文件id或者文件已经不存在',
            type: 'warning',
          })
        }
      } else {
        Message({ message: '无效的文件id或者文件已经不存在', type: 'warning' })
      }
    })
    .catch((err) => {
      Message({ message: '无效的文件id或者文件已经不存在', type: 'warning' })
    })
}

function LesFile_Delete(FileID) {
  let url = '/FILE/LesFileRpcApiService?cmd=delete&file_id=' + FileID
  axios
    .post(url)
    .then((res) => {
      if (res.status == 200) {
        if (res.data.substring(0, 1) == '!') {
          //Message({ message: '文件删除成功', type: 'success' })
          return true
        } else {
          Message({
            message: '无效的文件id或者文件已经不存在',
            type: 'warning',
          })
          return false
        }
      } else {
        //Message({ message: '文件删除失败', type: 'warning' })
        return false
      }
    })
    .catch((err) => {
      //Message({ message: '文件删除失败', type: 'warning' })
      return false
    })
}
export function LesFile(FileID, oper) {
  if (oper == 'd') {
    return LesFile_Delete(FileID)
  } else if (oper == 'v') {
    return LesFile_View_File(FileID)
  } else if (oper == 'l') {
    return LesFile_Download(FileID)
  }
}
