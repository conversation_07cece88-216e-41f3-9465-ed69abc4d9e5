<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysParamvalsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysParamvalsTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleDevelop="handleDevelop"
      @handleContract="handleContract"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>
    <el-table
      ref="lesysParamvalsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="LesysParamvals"
      row-key="lpvId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      highlight-current-row 
      @current-change="currentSelectRow"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="70"
        label-class-name="number">
      </el-table-column>

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="165"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>

          <el-button type="text" @click="handleUp(row)">上移</el-button>
          <el-button type="text" @click="handleDown(row)">下移</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

    <table-edit ref="lesysParamvalsEdit">
      <lesysParamvalsForm
        ref="lesysParamvalsForm"
        slot="form"
        :show="false"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysParamvalsQuerySearch">
      <lesysParamvalsQuery
        ref="lesysParamvalsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >
  </div>
</template>

<script>
  import { lesysParamvalsDoDeleteELog,
           lesysParamvalsDoSaveOrUpdLog,
           lesysParamvalsDoExport,
           lesysParamvalsGetTreeList,
           lesysParamvalsDoUp,
           lesysParamvalsDoDown } from '@/api/oa/lesysParamvals'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysParamvalsSearch from './components/Search.vue'
  import lesysParamvalsForm from './components/Form.vue'
  import lesysParamvalsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'lesysParamvals',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysParamvalsSearch,
      lesysParamvalsForm,
      lesysParamvalsQuery
    },
    data() {
      return {

        fullscreenLoading: false,
        editType:'',
        queryFormDf: {lpvName:''},
        queryForm: {lpvName:''},
        //表单赋值
        form: {lpvLpdId:'',lpvPid:'',lpvName:'',lpvSign:'',lpvOrder:'',lpvVal:'',lpvDesc:'',lpvIdPath:'',lpvIdName:'',lpvChilds:'',lpvVid:'',lpvHaschild:''},
        //表单验证规则
        rules: {
          lpvLpdId: [
            { required: false, message: '请输入目录ID', trigger: 'blur' }
          ],
          lpvPid: [
            { required: false, message: '请输入pid', trigger: 'blur' }
          ],
          lpvName: [
            { required: true, message: '请输入参数名称', trigger: 'blur' }
          ],
          lpvSign: [
            { required: false, message: '请输入参数标识', trigger: 'blur' }
          ],
          lpvOrder: [
            { required: false, message: '请输入顺序', trigger: 'blur' }
          ],
          lpvVal: [
            { required: false, message: '请输入参数值', trigger: 'blur' }
          ],
          lpvDesc: [
            { required: false, message: '请输入描述', trigger: 'blur' }
          ],
          lpvIdPath: [
            { required: false, message: '请输入idPATH', trigger: 'blur' }
          ],
          lpvIdName: [
            { required: false, message: '请输入namePath', trigger: 'blur' }
          ],
          lpvChilds: [
            { required: false, message: '请输入参数子参数名', trigger: 'blur' }
          ],
          lpvVid: [
            { required: false, message: '请输入顶级根ID', trigger: 'blur' }
          ],
          lpvHaschild: [
            { required: false, message: '请输入参数子数量', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1):this.gheight,
        checkList: ['参数名称','参数标识','参数值','描述'],
        columns: [
                { prop:"lpvName" ,label:"参数名称", width:'auto',disableCheck: true , sortable:false  },
                { prop:'lpvSign', label:'参数标识', width:'auto' , sortable:false  },
                { prop:'lpvVal', label:'参数值', width:'auto' , sortable:false  },
                { prop:'lpvDesc', label:'描述', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        row: '',
        searchForm: {
          lpvName:'',
          lpvLpdId:'',
          lpdIstree:''
        },
        lesysParamdirs:''
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      init(row) {
        this.lesysParamdirs = row
        this.searchForm.lpvLpdId = row.lpdId
        this.searchForm.lpdIstree = row.lpdIstree
        this.fetchData()
      },
      //新增或者修改保存
      save() {
        this.$refs.lesysParamvalsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData = JSON.stringify(this.form)
            if(this.form.lpvId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await lesysParamvalsDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        });
      },
      // 弹窗取消按钮
      close() {
        this.$refs.lesysParamvalsEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      //上移
      async handleUp(row){
        const msg = await lesysParamvalsDoUp(row)
        if(msg.code == 200) {
          this.$message({message:'上移操作成功!',type:'success'});
          this.fetchData();
        }else{
          this.$message({message:'上移操作失败,'+msg.data,type:'warning'});
        }
      },
      //下移
      async handleDown(row){
        const msg = await lesysParamvalsDoDown(row)
        if(msg.code == 200) {
          this.$message({message:'下移操作成功!',type:'success'});
          this.fetchData();
        }else{
          this.$message({message:'下移操作失败,'+msg.data,type:'warning'});
        }
      },
      currentSelectRow(val) {
        this.row = val
      },
      //添加
      handleAdd() {
        this.editType = 'add'
        this.form=Object.assign({},{"lpvPid":"0",'lpvLpdId':this.lesysParamdirs.lpdId})
        this.$refs['lesysParamvalsEdit'].showEdit('添加')
      },
      //添加下级
      handleAddSub(row){
        this.editType = 'add'
        this.form=Object.assign({},{"lpvPid":row.lpvId})
        this.$refs['lesysParamvalsEdit'].showEdit('添加下级')
      },
      // 表格行双击事件
      cellDblClick(row) {
        this.handleEdit(row) 
      },
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['lesysParamvalsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      handleDelete(row) {
        if (row.lpvId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            row.logData = JSON.stringify(row) 
            row.logDesc = "删除目录参数表数据"
            const msg = await lesysParamvalsDoDeleteELog( row )
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        } else {
          if (this.selectRows.length > 0) {
            const ids = this.selectRows.map((item) => item.lpvId).join()
            this.$baseConfirm('确定删除吗', null, async () => {
              row.logData = JSON.stringify(row) 
              row.logDesc = '删除目录参数表数据' 
              const msg = await lesysParamvalsDoDeleteELog(row)
              if(msg.code == 200) {
                this.$message({message:'删除操作成功!',type:'success'})
                await this.fetchData()
              }else{
                this.$message({message:'删除操作失败!',type:'warning'})
              }
            })
          } else {
            this.$baseMessage('请选择要删除的数据', 'error', 'vab-hey-message-error')
          }
        }
      },
      handleSearch($event) {
        this.searchForm = $event
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysParamvalsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysParamvalsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.$refs.lesysParamvalsQuerySearch.close()
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const {
          data: { list },
        } = await lesysParamvalsGetTreeList(this.searchForm)
        this.list = list
        this.listLoading = false
        this.isShow = false
        this.$refs.lesysParamvalsTs.isShow = false
      },
      // 展开
      handleDevelop() {
        let dom = document.getElementById("LesysParamvals")
        let els = dom.getElementsByClassName('el-table__expand-icon')
        if(this.list.length != 0 && els.length != 0){
          this.$refs.lesysParamvalsTs.isShow = false;
          for(let k = 0;k < els.length;k ++){
            els[k].classList.add("dafult")
          }
          if(dom.getElementsByClassName('el-table__expand-icon--expanded')){
            const open = dom.getElementsByClassName('el-table__expand-icon--expanded')
            for(let j = 0;j < open.length;j ++){
              open[j].classList.remove("dafult")
            }
            const dafult = dom.getElementsByClassName('dafult')
            for(let a = 0;a < dafult.length;a ++){
              dafult[a].click()
            }
          }
        }
      },
      // 收缩
      handleContract() {
        if(this.list.length != 0){
          this.$refs.lesysParamvalsTs.isShow = true
          const elsopen = document.getElementById("LesysParamvals").getElementsByClassName('el-table__expand-icon--expanded')
          if(elsopen){
            for(let i = 0;i < elsopen.length;i ++){
              elsopen[i].click()
            }
          }
        }
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"createdTime":{"celltype":"date"},"updatedTime":{"celltype":"date"}},
                      "fileName":"目录参数表.xls",
                      "isnumber":true,
                      "excelTitle":"目录参数表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#LesysParamvals",params)
        const { msg }  =  await lesysParamvalsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"目录参数表模板.xls",
                      "excelIstmpl":true}
        let qf = exportRearEnd("#LesysParamvals",params)
        const { msg }  =  await lesysParamvalsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>