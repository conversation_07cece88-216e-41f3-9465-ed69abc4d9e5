<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <mainData1 mainName="Q0VUQ1RBU0tT" mainTitle="任务"></mainData1>
  </div>
</template>

<script>
  import mainData1 from '@/views/data/data/index1.vue'
  export default {
    name: 'task1',
    props: {
      
    },
    components: {
      mainData1
    },
    
    data() {
      return {
        isFullscreen: false
      }
    },
    created() {
      
    },
    methods: {

    },
  }
</script>
