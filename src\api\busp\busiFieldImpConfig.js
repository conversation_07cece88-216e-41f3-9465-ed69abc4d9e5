import request from '@/utils/request'

//列表分页加载数据
export function busiFieldImpConfigGetList(params) {
  return request({
    url: '/busi-field-imp-config/vList',
    method: 'get',
    params,
  })
}
export function busiFieldImpConfigGetViewList(params) {
  return request({
    url: '/busi-field-imp-config/vViewList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function busiFieldImpConfigDoSave(data) {
  return request({
    url: '/busi-field-imp-config/save',
    method: 'post',
    data,
  })
}
