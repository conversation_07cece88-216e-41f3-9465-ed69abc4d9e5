<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
      
        <el-col :span="24">
          <el-form-item label="模型名称" prop="bmName">
            <el-input v-model.trim="form.bmName" maxlength="30" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型编码" prop="bmCode">
            <el-input v-model.trim="form.bmCode" maxlength="30" show-word-limit clearable :disabled="type=='update'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型描述" prop="bmDesc">
            <el-input v-model.trim="form.bmDesc" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <!--el-col :span="24">
          <el-form-item label="模型实体" prop="bmEntity">
            <el-input v-model.trim="form.bmEntity" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col-->
        <el-col :span="24">
          <el-form-item label="模型表名" prop="bmTable">
            <el-input v-model.trim="form.bmTable" maxlength="30" show-word-limit clearable :disabled="type=='update'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="bmId"  style="display:none;">
            <el-input v-model.trim="form.bmId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型分类" prop="bmSort">
            <el-select v-model.trim="tableForm.bmSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bmSort" :key="item.busiId" :label="item.busiName" :value="item.busiId"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标识" prop="bmSign"  style="display:none;">
            <el-input v-model.trim="form.bmSign" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型状态" prop="bmStatus">
            <el-select v-model.trim="tableForm.bmStatus"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bmStatus" :key="item.value" :label="item.name" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型实体类型" prop="bmType">
            <el-select v-model.trim="tableForm.bmType"   clearable   style="width:100%"  disabled>
              <el-option v-for="item in optionsData.bmType" :key="item.value" :label="item.name" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否自动订阅" prop="bmIsauto">
            <el-select v-model.trim="form.bmIsauto"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bmIsauto" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="确认状态" prop="bmConfirm"  style="display:none;">
            <el-input v-model.trim="form.bmConfirm" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { busiSortsGetList } from '@/api/busp/busiSorts'
  export default {
    name: 'busiModelForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          bmStatus:[{name:'有效',value:'有效'},{name:'失效',value:'失效'}],
          bmSort:[],
          bmIsauto:[{text:'否',value:'否'},{text:'是',value:'是'}],
          bmType:[{name:'表',value:'表'},{name:'视图',value:'视图'}],
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const data = await busiSortsGetList(this.searchForm)
        this.optionsData.bmSort = data.data
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>