import request from '@/utils/request'

//列表分页加载数据
export function lesysDbEntityTypeGetList(params) {
  return request({
    url: '/lesys-db-entity-type/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbEntityTypeDoSave(data) {
  return request({
    url: '/lesys-db-entity-type/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbEntityTypeDoUpdate(data) {
  return request({
    url: '/lesys-db-entity-type/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbEntityTypeDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-entity-type/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbEntityTypeDoDelete(data) {
  return request({
    url: '/lesys-db-entity-type/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbEntityTypeDoExport(data) {
  return request({
    url: '/lesys-db-entity-type/vExport',
    method: 'post',
    data,
  })
}
