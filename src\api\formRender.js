// import request from '@/utils/request'

// export function getFormData(params) {
//   return request({
//     url: '',
//     method: 'get',
//     params,
//   })
// };

export function renderFrom() {
  return {
    inline: true,
    labelPosition: 'right',
    labelWidth: '100px',
    size: 'small',
    statusIcon: true,
    formItemList: [
      {
        type: 'input',
        label: '姓名',
        disable: false,
        readonly: false,
        value: '',
        placeholder: '请输入姓名',
        rules: [],
        key: 'name',
        subtype: 'text',
        maxLength: '20',
      },
      {
        type: 'radio',
        label: '性别',
        value: '',
        button: false,
        border: true,
        rules: [],
        key: 'gender',
        options: [
          {
            value: '1',
            label: '男',
            disabled: false,
          },
          {
            value: '0',
            label: '女',
            disabled: false,
          },
        ],
      },
    ],
  }
}
