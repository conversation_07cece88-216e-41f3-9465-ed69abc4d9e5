import request from '@/utils/request'

//分页查询企业产权档案
export function enterprisePropertyGetList(params) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/vPage',
    method: 'get',
    params,
  })
}

//列表查询企业产权档案
export function enterprisePropertyGetVList(params) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/vList',
    method: 'get',
    params,
  })
}

//保存新增,不记录日志
export function enterprisePropertyDoSave(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/save',
    method: 'post',
    data,
  })
}

//保存更新,不记录日志
export function enterprisePropertyDoUpdate(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/update',
    method: 'post',
    data,
  })
}

//保存新增或更新,不记录日志
export function enterprisePropertyDoSaveOrUpd(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/saveOrUpd',
    method: 'post',
    data,
  })
}

//保存新增或更新,记录日志,实体类增加logDesc属性
export function enterprisePropertyDoSaveOrUpdLog(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/saveOrUpdLog',
    method: 'post',
    data,
  })
}

//根据主键删除,不记录日志
export function enterprisePropertyDoDelete(id) {
  return request({
    url: `/zcgl-enterprise-property-rights-archives/delete-by-id/${id}`,
    method: 'delete',
  })
}

//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function enterprisePropertyDoDeleteLog(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/delete-by-id-log',
    method: 'post',
    data,
  })
}

//根据条件删除,记录日志,参数为实体，logDesc
export function enterprisePropertyDoDeleteELog(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/deleteLog',
    method: 'post',
    data,
  })
}

//后端导出
export function enterprisePropertyDoExport(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/vExport',
    method: 'post',
    data,
  })
}

//统计数据
export function enterprisePropertyGetStat(params) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/vStat',
    method: 'get',
    params,
  })
}

//通过id查询
export function enterprisePropertyGetDetail(id) {
  return request({
    url: `/zcgl-enterprise-property-rights-archives/get-by-id/${id}`,
    method: 'get',
  })
}

//上传文件
export function enterprisePropertyUploadFile(data) {
  return request({
    url: '/zcgl-enterprise-property-rights-archives/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
