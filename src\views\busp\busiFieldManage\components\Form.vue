<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="属性名称" prop="bfName">
            <el-input v-model.trim="form.bfName" maxlength="50" show-word-limit  ></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="字段分类" prop="bfSort">
            <el-select v-model.trim="tableForm.bfSort"     style="width:100%">
              <el-option v-for="item in optionsData.bfSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="属性实体名" prop="bfField">
            <el-input v-model.trim="form.bfField" maxlength="50" show-word-limit clearable disabled></el-input>
          </el-form-item>
        </el-col>


      
        <el-col :span="24">
          <el-form-item label="使用状态" prop="bfUseState">
            <el-select v-model.trim="tableForm.bfUseState"     style="width:100%">
              <el-option v-for="item in optionsData.bfUseState" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="24">
          <el-form-item label="主键" prop="bfId"  style="display:none;">
            <el-input v-model.trim="form.bfId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'busiFieldForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          bfSort:[{value:'统计',label:'统计'},{value:'维度',label:'维度'},{value:'其它',label:'其它'}],
          isToF:[{value:'是',label:'是'},{value:'否',label:'否'}],
          bfUseState:[{value:'未启用',label:'未启用'},{value:'启用',label:'启用'},{value:'停用',label:'停用'}],
          bfLevel:[{value:'系统定义',label:'系统定义'},{value:'业务定义',label:'业务定义'},{value:'自定义',label:'自定义'}],
          bfColumnType:[{value:'字符',label:'字符'},{value:'时间',label:'时间'},{value:'数值',label:'数值'}]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>