import FileSaver from 'file-saver'
import XLSX from 'xlsx'

export function exportXLSX(el, fileName = '表格文件') {
  // 解决生成重复数据-因为使用了fixed属性
  // let fixRight = document.querySelector(el+ " .el-table__fixed-right");
  // let fixLeft = document.querySelector(el+ " .el-table__fixed-left");
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let tableBody = tableDom.querySelector('.el-table__body')
  tableHeader.childNodes[0].append(tableBody.childNodes[1])
  let headerDom = tableHeader.childNodes[0].querySelectorAll('th')
  // 移除左侧checkbox的节点
  if (headerDom[0].querySelectorAll('.el-checkbox').length > 0) {
    headerDom[0].remove()
  }
  for (let key in headerDom) {
    if (headerDom[key].innerText === '操作') {
      headerDom[key].remove()
    }
  }
  // 清理掉checkbox 和操作的button
  let tableList = tableHeader.childNodes[0].childNodes[2].querySelectorAll('td')
  for (let key = 0; key < tableList.length; key++) {
    if (
      tableList[key].querySelectorAll('.el-checkbox').length > 0 ||
      tableList[key].querySelectorAll('.el-button').length > 0
    ) {
      tableList[key].remove()
    }
  }
  var wb
  // if (fixRight || fixLeft) {
  //   wb = XLSX.utils.table_to_book(
  //     document.querySelector(el).removeChild(fixRight || fixLeft),
  //     { raw: true }
  //   );
  //   document.querySelector(el).appendChild(fixRight || fixLeft);
  // } else {
  //   wb = XLSX.utils.table_to_book(document.querySelector(el), {
  //     raw: true,
  //   });
  // };

  wb = XLSX.utils.table_to_book(tableHeader)
  // 把当前的book节点写入XLSX中
  let webOut = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: true,
    type: 'array',
  })
  try {
    FileSaver.saveAs(
      new Blob([webOut], { type: 'application/octet-stream' }),
      fileName + '.xlsx'
    )
  } catch (e) {
    if (typeof console !== 'undefined') console.log(e, webOut)
  }
}
export function exportRearEnd(el, params) {
  if(params.uncols){
    params.uncols = params.uncols + ","
  }
  if(!params.recols){
    params.recols = {}
  }
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let headerDom = tableHeader.childNodes[0].querySelectorAll('th')
  let header = []
  let uexportcols = {}
  for (let i = 0; i < headerDom.length; i++) {
    if (headerDom[i].childNodes.length == 0) {
      continue
    }
    let classnames = headerDom[i].childNodes[0]['classList']
    let cname = classnames[classnames.length - 1]
    if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
      continue
    }
    if (
      cname.indexOf('checkbox') != -1 ||
      cname.indexOf('lesoper') != -1 ||
      cname == 'number'
    ) {
      uexportcols[i] = cname
      continue
    }

    let celltype = 'text'
    let cellwidth = '18'
    let dateFormat = 'yyyy-MM-dd HH:mm:ss'
    if (dateFields[cname] && dateFields[cname]['celltype']) {
      celltype = dateFields[cname]['celltype']
    }
    if (dateFields[cname] && dateFields[cname]['dateFormat']) {
      dateFormat = dateFields[cname]['dateFormat']
    }
    if (dateFields[cname] && dateFields[cname]['cellwidth']) {
      cellwidth = dateFields[cname]['cellwidth']
    }
    if(params.uncols && params.uncols.indexOf(cname+",")!=-1){
      continue;
    }
    if(params.recols[cname]){
      cname = params.recols[cname]
    }
    header.push({
      field: cname,
      title: headerDom[i]['outerText'],
      rowspan: headerDom[i]['attributes']['rowspan'].value,
      colspan: headerDom[i]['attributes']['colspan'].value,
      rowspand: headerDom[i]['attributes']['rowspan'].value,
      celltype: celltype,
      cellwidth: cellwidth,
      dateFormat: dateFormat,
      celliswrap: '0',
      cellwraplength: '0',
    })
  }

  let cheaders = []
  cheaders.push(header)

  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: cheaders,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| '',
      uexportcols:uexportcols
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}

export function exportRearEnds(el, params) {
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let tableHeaderTrs = tableHeader.childNodes[0].querySelectorAll('tr')
  let cheaders = []
  let tablecols = 0
  let tablecolsmap = {}
  let tablecolsheadmap = {}
  let uexportcols = {}
  for (let j = 0; j < tableHeaderTrs.length; j++) {
    let headerDom = tableHeaderTrs[j].querySelectorAll('th')
    let header = []
    for (let i = 0; i < headerDom.length; i++) {
      if (headerDom[i].childNodes.length == 0) {
        continue
      }
      let classnames = headerDom[i].childNodes[0]['classList']
      let cname = classnames[classnames.length - 1]
      if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
        continue
      }
      if (
        cname.indexOf('checkbox') != -1 ||
        cname.indexOf('lesoper') != -1 
      ) {
        uexportcols[j] = cname
        continue
      }
      if(params && !params.isserial && cname=='number'){
        continue
      }
      let celltype = 'text'
      let cellwidth = '18'
      if (dateFields[cname] && dateFields[cname]['celltype']) {
        celltype = dateFields[cname]['celltype']
      }
      if (dateFields[cname] && dateFields[cname]['cellwidth']) {
        cellwidth = dateFields[cname]['cellwidth']
      }

      let cn = headerDom[i].classList[0]
      let head = {
        field: cname,
        title: headerDom[i]['outerText'],
        rowspan: headerDom[i]['attributes']['rowspan'].value,
        colspan: headerDom[i]['attributes']['colspan'].value,
        rowspand: headerDom[i]['attributes']['rowspan'].value,
        celltype: celltype,
        cellwidth: cellwidth,
        celliswrap: '0',
        cellwraplength: '0',
        cellid: cn,
        cellpid: cn.substr(0, cn.lastIndexOf('_column_')),
      }
      header.push(head)
      tablecolsmap[cn] = j == 0 ? '0' : cn.substr(0, cn.lastIndexOf('_column_'))
      tablecolsheadmap[cn] = head
      if (j == 0) {
        tablecols =
          tablecols + parseInt(headerDom[i]['attributes']['colspan'].value)
      }
    }
    cheaders.push(header)
  }

  let heads = []
  for (let i = 0; i < tableHeaderTrs.length; i++) {
    let head = []
    for (let j = 0; j < tablecols; j++) {
      head.push({
        field: '',
        title: '',
        rowspan: 1,
        colspan: 1,
        rowspand: 1,
        celltype: 'text',
        cellwidth: 18,
        celliswrap: '0',
        cellwraplength: '0',
      })
    }
    heads.push(head)
  }

  let nheadcolmap = {}
  let rpcol = ''
  for (let i = 0; i < cheaders.length; i++) {
    let rcol = 0
    for (let j = 0; j < cheaders[i].length; j++) {
      let head = cheaders[i][j]
      if (!tablecolsheadmap[head.cellpid]) {
        heads[i][rcol] = head
        nheadcolmap[head.cellid] = rcol
        rcol = rcol + parseInt(head.colspan)
      } else {
        let phead = tablecolsheadmap[head.cellpid]
        if (rpcol != phead.cellid) {
          rpcol = phead.cellid
          rcol = 0
        }

        heads[i][rcol + nheadcolmap[phead.cellid]] = head
        nheadcolmap[head.cellid] = rcol + nheadcolmap[phead.cellid]
        rcol = rcol + parseInt(head.colspan)
      }
    }
    rcol = 0
  }
  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: heads,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| '',
      uexportcols:uexportcols
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}

export function exportRearEndDatas(el, params) {
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  
  let cheaders = []
  let tablecols = 0
  let tablecolsmap = {}
  let tablecolsheadmap = {}
  let heads = []
  let tableHeader = tableDom.querySelector('.el-table__body-wrapper')
  let tableHeaderTrs = tableHeader.childNodes[0].querySelectorAll('tr')
  for (let j = 0; j < tableHeaderTrs.length; j++) {
    let headerDom = tableHeaderTrs[j].querySelectorAll('td')
    let header = []
    for (let i = 0; i < headerDom.length; i++) {
      if (headerDom[i].childNodes.length == 0) {
        continue
      }
      let classnames = headerDom[i].childNodes[0]['classList']
      let cname = classnames[classnames.length - 1]
      if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
        continue
      }
      if (
        cname.indexOf('checkbox') != -1 ||
        cname.indexOf('lesoper') != -1 
      ) {
        continue
      }
      
      if(params && !params.isserial && classnames[0]!='cell'){
        continue
      }

      if(params && params.uncolnums && params.uncolnums.indexOf(i+',')!=-1){
        continue
      }

      if(params && params.uexportcols && params.uexportcols[i]){
        continue
      }

      let celltype = 'text'
      let cellwidth = '18'
      if (dateFields[cname] && dateFields[cname]['celltype']) {
        celltype = dateFields[cname]['celltype']
      }
      if (dateFields[cname] && dateFields[cname]['cellwidth']) {
        cellwidth = dateFields[cname]['cellwidth']
      }

      let cn = headerDom[i].classList[0]
      
      let head = {
        field: cname,
        title: headerDom[i]['outerText'].trim(),
        rowspan: headerDom[i]['attributes']['rowspan'].value,
        colspan: headerDom[i]['attributes']['colspan'].value,
        rowspand: headerDom[i]['attributes']['rowspan'].value,
        celltype: celltype,
        cellwidth: cellwidth,
        celliswrap: '0',
        cellwraplength: '0',
        cellid: cn,
        cellpid: cn.substr(0, cn.lastIndexOf('_column_')),
      }
      header.push(head)
      tablecolsmap[cn] = j == 0 ? '0' : cn.substr(0, cn.lastIndexOf('_column_'))
      tablecolsheadmap[cn] = head
      if (j == 0) {
        tablecols =
          tablecols + parseInt(headerDom[i]['attributes']['colspan'].value)
      }
    }
    cheaders.push(header)
  }

  for (let i = 0; i < tableHeaderTrs.length; i++) {
    let head = []
    
    for (let j = 0; j < tablecols; j++) {
      head.push({
        field: '',
        title: '',
        rowspan: 1,
        colspan: 1,
        rowspand: 1,
        celltype: 'text',
        cellwidth: 18,
        celliswrap: '0',
        cellwraplength: '0',
      })
    }
    heads.push(head)
  }

  let nheadcolmap = {}
  let rpcol = ''
  for (let i = 0; i < cheaders.length; i++) {
    let rcol = 0
    for (let j = 0; j < cheaders[i].length; j++) {
      let head = cheaders[i][j]
      if (!tablecolsheadmap[head.cellpid]) {
        heads[i][rcol] = head
        nheadcolmap[head.cellid] = rcol
        rcol = rcol + parseInt(head.colspan)
      } else {
        let phead = tablecolsheadmap[head.cellpid]
        if (rpcol != phead.cellid) {
          rpcol = phead.cellid
          rcol = 0
        }

        heads[i][rcol + nheadcolmap[phead.cellid]] = head
        nheadcolmap[head.cellid] = rcol + nheadcolmap[phead.cellid]
        rcol = rcol + parseInt(head.colspan)
      }
    }
    rcol = 0
  }
  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: heads,
      excelTitle: params.excelTitle || ''
    },
    {}
  )
}

export function exportGetPageData(qf,qd){
  let ja = qf.excelExps[qf.excelExps.length-1]
  let ed = []
  for(let i=0;i<qd.excelExps.length;i++){
    let rd = {}
    for(let j=0;j<qd.excelExps[i].length;j++){
      rd[ja[j].field] = qd.excelExps[i][j].title
    }
    ed.push(rd)
  }
  return ed
}



export function exportRearTmplVxe(params) {
  if(params.uncols){
    params.uncols = params.uncols + ","
  }
  let dateFields = params.dataFields || {}
  let header = []
  for (let i = 0; i < params.columns.length; i++) {
    let celltype = 'text'
    let cellwidth = '18'
    let dateFormat = 'yyyy-MM-dd HH:mm:ss'
    let cname = params.columns[i].prop
    if (dateFields[cname] && dateFields[cname]['celltype']) {
      celltype = dateFields[cname]['celltype']
    }
    if (dateFields[cname] && dateFields[cname]['dateFormat']) {
      dateFormat = dateFields[cname]['dateFormat']
    }
    if (dateFields[cname] && dateFields[cname]['cellwidth']) {
      cellwidth = dateFields[cname]['cellwidth']
    }
    if(params.uncols && params.uncols.indexOf(cname+",")!=-1){
      continue;
    }
    header.push({
      field: cname,
      title: params.columns[i].label,
      rowspan: 1,
      colspan: 1,
      rowspand: 1,
      celltype: celltype,
      cellwidth: cellwidth,
      dateFormat: dateFormat,
      celliswrap: '0',
      cellwraplength: '0',
    })
  }

  let cheaders = []
  cheaders.push(header)

  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: cheaders,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| ''
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}
