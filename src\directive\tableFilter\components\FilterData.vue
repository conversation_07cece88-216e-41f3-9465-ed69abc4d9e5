<template>
  <div>
    <el-row>
      <div style="padding: 5px">
        <el-input
          v-model="keyWords"
          clearable
          placeholder="请输入查询关键字"
          width="100%"
        />
        <el-scrollbar style="height: 150px; margin-top: 10px">
          <el-checkbox-group v-model="checkList" @change="handleCheck">
            <el-checkbox
              v-for="(item, index) in checkboxList"
              :key="index"
              :label="item"
            />
          </el-checkbox-group>
        </el-scrollbar>
      </div>
    </el-row>
    <el-row>
      <div style="float: right; margin-right: 10px">
        <slot name="buttonSlot"></slot>
      </div>
    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'FilterData',
    props: {
      selectItemList: {
        type: Array,
        default: () => {
          ;[]
        },
      },
    },
    data() {
      return {
        keyWords: '',
        checkList: [],
        checkboxList: this.selectItemList,
      }
    },
    watch: {
      keyWords: {
        handler: function (val) {
          this.checkboxList = this.selectItemList.filter((item) =>
            item.includes(val)
          )
        },
      },
    },
    methods: {
      handleCheck(val) {
        this.$emit('selectedList', val)
      },
      clear() {
        this.checkList = []
        this.keyWords = ''
      },
    },
  }
</script>
<style lang="scss" scoped>
  ::v-deep .el-checkbox {
    margin-top: 3px;
  }
</style>
