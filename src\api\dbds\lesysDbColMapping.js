import request from '@/utils/request'

//列表分页加载数据
export function lesysDbColMappingGetList(params) {
  return request({
    url: '/lesys-db-col-mapping/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbColMappingDoSave(data) {
  return request({
    url: '/lesys-db-col-mapping/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbColMappingDoUpdate(data) {
  return request({
    url: '/lesys-db-col-mapping/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbColMappingDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-col-mapping/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbColMappingDoDelete(data) {
  return request({
    url: '/lesys-db-col-mapping/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbColMappingDoExport(data) {
  return request({
    url: '/lesys-db-col-mapping/vExport',
    method: 'post',
    data,
  })
}
