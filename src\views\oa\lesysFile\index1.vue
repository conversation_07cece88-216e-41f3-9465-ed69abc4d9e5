<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model="key"
              placeholder="快速搜索"
              @keyup.enter.native="handleFind"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-upload
              :show-file-list="false"
              action=""
              :accept="fileAccept"
              auto-upload
              :disabled="fileUploadBtnText == '正在导入'"
              :http-request="uploadFile"
              >
              <el-button type="primary":icon="uploadBtnIcon">上传文件</el-button>
            </el-upload>
          </el-form-item>
          &nbsp;
          <el-form-item>
            <input type="file" @change="handleFiles" multiple>
            <button @click="uploadFile1">上传文件</button>
          </el-form-item>
          &nbsp;
          <el-form-item>
            <button @click="uploadFile2">表单提交</button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        
      </vab-query-form-right-panel>
    </vab-query-form>

    <vxe-table
      class="mylist-table"
      v-loading="listLoading"
      ref="table"
      id="mainData"
      border
      stripe
      show-overflow="tooltip"
      :height="height"
      :row-config="{ useKey: true,keyField:'fileId',isCurrent:true,isHover:true }"
      :column-config="{ resizable: true, useKey: true }"
      :data="list"
      size="small"
      :scroll-y="{enabled: true, gt: 300}"
      @current-change="currentSelectRow"
      :checkbox-config="{highlight: true}"
      @cell-dblclick="cellDblClick"
      :seq-config="seqConfig"
      :export-config="exportConfig"
    >
      <vxe-column field="seq" type="seq" width="70" align="center" fixed="left"></vxe-column>
      <vxe-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :title="item.label"
        :field="item.prop"
        align="center"
        v-bind="{ ...item }"
        header-align="center"
        minWidth="150"
      >
      </vxe-column>

      <vxe-column
        align="center"
        title="操作"
        fixed="right"
        show-overflow-tooltip
        width="200"
        field="oper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleView(row)" icon="el-icon-circle-plus">查看</el-button>
          <el-button type="text" @click="handleDown(row)" icon="el-icon-download">下载</el-button>
          <el-button type="text" @click="handleDelete(row)" icon="el-icon-delete">删除</el-button>
        </template>
      </vxe-column>

      <template #empty>
        <el-image
          style="height: 110px"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </vxe-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <FileUpload 
      ref="fileUpload1"
      :config="config1"
      @handleUploadScuess="handleUploadScuess1"/>
  </div>
</template>

<script>
  import { lesysFileGetPageList,lesysFileDown,lesysFileExport } from '@/api/oa/lesysFile'         
  import { exportRearTmplVxe } from '@/api/exportExcel'
  import { mapGetters } from 'vuex'
  import config from '@/config'
  import axios from 'axios'
  import store from '@/store'
  import { uuid } from '@/utils/index'
  import { LesFile } from '@/utils/lesFile'
  import FileUpload from '@/views/common/FileUpload.vue'
  export default {
    name: 'lesysFile1',
    props: {
      gheight: {
        type: Number,
      }
    },
    components: {
      FileUpload
    },
    data() {
      let searchForm = {
          pageNo: 1,
          pageSize: 20,
          sortField: '',
          sortOrder: ''
      };
      const seqConfig = {
        seqMethod ({ rowIndex }) {
          return `${(searchForm.pageNo - 1) * searchForm.pageSize + rowIndex + 1}`
        }
      };
      const exportConfig = {
        sheetMethod(params) {
          const { worksheet } = params;
          worksheet.eachRow(excelRow => {
              excelRow.height = 40;
              excelRow.eachCell(excelCell => {
                  // 设置单元格边框
                  excelCell.border = {
                      top: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      left: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      bottom: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      right: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      }
                  };
              });
          });
        }
      };

      return {
        fullscreenLoading: false,
        editType: '',
        queryFormDf: {},
        queryForm: {},
        form: {},
        formConfig: {
          labelPosition: 'right',
          labelWidth: '80px',
          size: 'small',
        },
        title: '',
        isFullscreen: false,
        height: !this.gheight ? this.$baseTableHeight(1, 1) : this.gheight,
        list: [],
        tableData: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm,
        rules: {},
        checkList: ['文件名称','文件时间','文件大小'],
        columns: [
          { prop:'fileName'     , label:'文件名称', sortable:false  },
          { prop:'fileTimestamp', label:'文件时间', sortable:false  },
          { prop:'fileSize'     , label:'文件大小', sortable:false  }
        ],
        multipleSelection: [],
        editWidth: '1200px', 
        checkListForms:{},
        seqConfig,
        exportConfig,
        key:'',
        uid:'',
        config1: {
          limit:1,
          isDecode: 0
        },
        fileUploadBtnText: "导入",
        uploadBtnIcon:"el-icon-upload2",
        fileAccept:".xls,.xlsx",
        files: []
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: { 
      handleAdd(){
        //this.$refs['fileUpload1'].show()
      },
      async handleUploadScuess1(files){
        this.$message({message:'上传附件操作成功!',type:'success'})
        this.fetchData() 
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val.row
      },
      // 双击行编辑事件
      cellDblClick(val) {
        this.row = val.row
      },
      handleView(row){
        LesFile(row.fileId, 'v')
      },
      handleDown(row){
        LesFile(row.fileId, 'l')
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗？', null, async () => {
          LesFile(row.fileId, 'd')
          this.fetchData()
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleFind() {
        let searchProps = []
        this.columns.forEach(key => {
          searchProps.push(key['prop'])
        })
        const filterVal = String(this.key).trim()
        if (filterVal) {
          const filterRE = new RegExp(filterVal, 'gi')
          const rest = this.tableData.filter(item => searchProps.some(key => String(item[key]).indexOf(filterVal) > -1))
          this.list = rest.map(row => {
            // 搜索为克隆数据，不会污染源数据
            const item = Object.assign({}, row)
            searchProps.forEach(key => {
              item[key] = String(item[key]).replace("null","")
            })
            return item
          })
        } else {
          this.list = this.tableData
        }
      },
      
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysFileGetPageList(this.searchForm)
        this.tableData = list
        this.list = list
        this.total = total
        this.listLoading = false
      },
      //导入
      async uploadFile(param){
        let file = param.file;
        this.uploadBtnIcon = "el-icon-loading";
        this.fileUploadBtnText = "正在导入";
        var formdata = new FormData()
        formdata.append('file', file)
        formdata.append('fileId','12345678')
        formdata.append('fileSec','1')
        var url = config.baseURL+"/file/multipart"
        axios({url,method: 'post',data:formdata,
          headers:{
            'Authorization': store.getters['user/token'],
            'Content-Type':'multipart/form-data'
          }
        }).then(res => {
          if(res.data.code==200){
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
            console.log(res.data)
          }else{
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
          }
        }).catch(err => {
          this.uploadBtnIcon = "el-icon-upload2";
          this.fileUploadBtnText = "导入";
        })
      },
      handleFiles(event) {
        this.files = event.target.files;
      },
      //导入
      async uploadFile1(){
        var formdata = new FormData()
        for(let i=0;i<this.files.length;i++){
          formdata.append('file'+i, this.files[i])
        } 
        var url = config.baseURL+"/file/multiparts"
        axios({url,method: 'post',data:formdata,
          headers:{
            'Authorization': store.getters['user/token'],
            'Content-Type':'multipart/form-data'
          }
        }).then(res => {
          if(res.data.code==200){
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
            console.log(res.data)
          }else{
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
          }
        }).catch(err => {
          this.uploadBtnIcon = "el-icon-upload2";
          this.fileUploadBtnText = "导入";
        })
      },
      async uploadFile2(){
        var formdata = new FormData()
        for(let i=0;i<this.files.length;i++){
          formdata.append('file', this.files[i])
        } 
        let data = {fileId:'123123123132',fileName: '中国🇨🇳'}
        formdata.append('lesfile', new Blob([JSON.stringify(data)], { type: 'application/json' }))
        var url = config.baseURL+"/file/rpart"
        axios({url,method: 'post',data:formdata,
          headers:{
            'Authorization': store.getters['user/token'],
            'Content-Type':'multipart/form-data'
          }
        }).then(res => {
          if(res.data.code==200){
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
            console.log(res.data)
          }else{
            this.uploadBtnIcon = "el-icon-upload2";
            this.fileUploadBtnText = "导入";
          }
        }).catch(err => {
          this.uploadBtnIcon = "el-icon-upload2";
          this.fileUploadBtnText = "导入";
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .mylist-table {
    ::v-deep(.keyword-highlight)  {
      background-color: #FFFF00;
    }
  }
</style>