<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <mainData1 mainName="UExBTlM=" mainTitle="计划"></mainData1>
  </div>
</template>

<script>
  import mainData1 from '@/views/data/data/index1.vue'
  export default {
    name: 'plan',
    props: {
      
    },
    components: {
      mainData1
    },
    
    data() {
      return {
        isFullscreen: false
      }
    },
    created() {
      
    },
    methods: {

    },
  }
</script>
