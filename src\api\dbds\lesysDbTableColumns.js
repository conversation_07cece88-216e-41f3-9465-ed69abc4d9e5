import request from '@/utils/request'

//列表分页加载数据
export function lesysDbTableColumnsGetList(params) {
  return request({
    url: '/lesys-db-table-columns/vList',
    method: 'get',
    params,
  })
}
export function lesysDbTableColumnsGetPage(params) {
  return request({
    url: '/lesys-db-table-columns/vPageList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbTableColumnsDoSave(data) {
  return request({
    url: '/lesys-db-table-columns/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbTableColumnsDoUpdate(data) {
  return request({
    url: '/lesys-db-table-columns/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbTableColumnsDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-table-columns/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbTableColumnsDoDelete(data) {
  return request({
    url: '/lesys-db-table-columns/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbTableColumnsDoExport(data) {
  return request({
    url: '/lesys-db-table-columns/vExport',
    method: 'post',
    data,
  })
}
