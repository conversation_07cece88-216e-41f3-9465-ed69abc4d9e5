import request from '@/utils/request'

//列表分页加载数据
export function leswfe15TaskGetList(params) {
  return request({
    url: '/leswfe15-task/vPage',
    method: 'get',
    params,
  })
}

//取回流程
export function leswfe15TaskDoReciveTask(data) {
  return request({
    url: '/leswfe15-task/reciveTask',
    method: 'post',
    data,
  })
}

//保存新增,不记录日志
export function leswfe15TaskDoSave(data) {
  return request({
    url: '/leswfe15-task/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function leswfe15TaskDoUpdate(data) {
  return request({
    url: '/leswfe15-task/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function leswfe15TaskDoSaveOrUpd(data) {
  return request({
    url: '/leswfe15-task/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function leswfe15TaskDoSaveOrUpdLog(data) {
  return request({
    url: '/leswfe15-task/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function leswfe15TaskDoDelete(data) {
  return request({
    url: '/leswfe15-task/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function leswfe15TaskDoDeleteLog(data) {
  return request({
    url: '/leswfe15-task/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function leswfe15TaskDoDeleteELog(data) {
  return request({
    url: '/leswfe15-task/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function leswfe15TaskGetTreeList(params){
  return request({
    url: '/leswfe15-task/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function leswfe15TaskDoExport(data) {
  return request({
    url: '/leswfe15-task/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function leswfe15TaskGetStat(params) {
  return request({
    url: '/leswfe15-task/vStat',
    method: 'get',
    params,
  })
}
