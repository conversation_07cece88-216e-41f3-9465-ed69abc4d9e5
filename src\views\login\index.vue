<template>
  <div class="login-container">
    <div class="sys_title">
      <img src="~@/assets/logo.png" alt="" />
      <p>{{ title }}</p>
    </div>
    <el-row>
      <el-col :lg="14" :md="11" :sm="24" :xl="14" :xs="24">
        <div style="color: transparent">占位符</div>
      </el-col>
      <el-col :lg="9" :md="12" :sm="24" :xl="9" :xs="24">
        <el-form
          ref="form"
          class="login-form"
          label-position="left"
          :model="form"
          :rules="rules"
        >
          <!-- <div class="title">hello !</div> -->
          <div class="title-tips">
            {{ translateTitle('欢迎来到') }}{{ title }}！
          </div>
          <el-form-item prop="username" style="margin-top: 40px">
            <el-input
              v-model.trim="form.username"
              v-focus
              :placeholder="translateTitle('请输入用户名')"
              tabindex="1"
              type="text"
            >
              <template #prefix>
                <vab-icon icon="user-line" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              :key="passwordType"
              ref="password"
              v-model.trim="form.password"
              :placeholder="translateTitle('请输入密码')"
              tabindex="2"
              :type="passwordType"
              @keyup.enter.native="handleLogin"
            >
              <template #prefix>
                <vab-icon icon="lock-line" />
              </template>
              <template v-if="passwordType === 'password'" #suffix>
                <vab-icon
                  class="show-password"
                  icon="eye-off-line"
                  @click="handlePassword"
                />
              </template>
              <template v-else #suffix>
                <vab-icon
                  class="show-password"
                  icon="eye-line"
                  @click="handlePassword"
                />
              </template>
            </el-input>
          </el-form-item>
          <!-- 验证码验证逻辑需自行开发，如不需要验证码功能建议注释 -->
          <!-- <el-form-item prop="verificationCode">
            <el-input
              v-model.trim="form.verificationCode"
              :placeholder="translateTitle('验证码') + previewText"
              tabindex="3"
              type="text"
            >
              <template #prefix>
                <vab-icon icon="barcode-box-line" />
              </template>
            </el-input>
            <el-image class="code" :src="codeUrl" @click="changeCode" />
          </el-form-item> -->
          <el-button
            class="login-btn"
            :loading="loading"
            type="primary"
            @click="handleLogin"
            
          >
            {{ translateTitle('登录') }}
          </el-button>
          <!--router-link to="/register">
            <div style="margin-top: 20px">{{ translateTitle('注册') }}</div>
          </router-link-->
        </el-form>
      </el-col>
      <el-col :lg="1" :md="1" :sm="24" :xl="1" :xs="24">
        <div style="color: transparent">占位符</div>
      </el-col>
      <Verify
        @success="success"          
        :mode="'pop'"              
        :captchaType="'clickWord'"      
        :imgSize="{ width: '330px', height: '155px' }" 
        ref="verify"
      ></Verify>
    </el-row>
    
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex'
  import { translateTitle } from '@/utils/i18n'
  import { isPassword } from '@/utils/validate'
  import { useCaptcha} from '@/config'
   //引入组件
  import Verify from "../../components/verifition/Verify"

  import { getConsignByLcFromUser, cancelConsign} from '@/api/oa/lesysConsign'

  export default {
    name: 'Login',
    components: {
      Verify
    },
    directives: {
      focus: {
        inserted(el) {
          el.querySelector('input').focus()
        },
      },
    },
    beforeRouteLeave(to, from, next) {
      clearInterval(this.timer)
      next()
    },
    data() {
      const validateUsername = (rule, value, callback) => {
        if ('' === value)
          callback(new Error(this.translateTitle('用户名不能为空')))
        else callback()
      }
      const validatePassword = (rule, value, callback) => {
        if (!isPassword(value))
          callback(new Error(this.translateTitle('密码不能少于6位')))
        else callback()
      }
      return {
        form: {
          username: '',
          password: '',
          tenantId: 'default',
          grant_type: 'password',
          client_id: 'les_cetc007',
          is3y:'0',
          //verificationCode: '',
        },
        rules: {
          username: [
            {
              required: true,
              trigger: 'blur',
              validator: validateUsername,
            },
          ],
          password: [
            {
              required: true,
              trigger: 'blur',
              validator: validatePassword,
            },
          ],
          /* verificationCode: [
            {
              required: true,
              trigger: 'blur',
              message: '验证码不能空',
            },
          ], */
        },
        loading: false,
        passwordType: 'password',
        redirect: undefined,
        timer: 0,
        codeUrl: 'https://www.oschina.net/action/user/captcha',
        previewText: '',
      }
    },
    computed: {
      ...mapGetters({
        title: 'settings/title',
      }),
    },
    watch: {
      $route: {
        handler(route) {
          this.redirect = (route.query && route.query.redirect) || '/'
        },
        immediate: true,
      },
    },
    created() {
      this.form.is3y = this.$route.query.ssa?this.$route.query.ssa:'0'
    },
    mounted() {
      this.form.username = 'xiaobai'
      this.form.password = '123456'
      
      // 为了演示效果，会在官网演示页自动登录到首页，正式开发可删除
      if (
        document.domain === 'vue-admin-beautiful.com' ||
        document.domain === 'chu1204505056.gitee.io'
      ) {
        this.previewText = '（演示地址验证码可不填）'
        this.timer = setTimeout(() => {
          this.handleLogin()
        }, 5000)
      }
    },
    methods: {
      ...mapActions({
        login: 'user/login'
      }),
      translateTitle,
      handlePassword() {
        this.passwordType === 'password'
          ? (this.passwordType = '')
          : (this.passwordType = 'password')
        this.$nextTick(() => {
          this.$refs.password.focus()
        })
      },
      handleRoute() {
        return this.redirect === '/404' || this.redirect === '/403'
          ? '/'
          : this.redirect
      },
      _handleLogin() {
        this.$refs.form.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              //如果三员登录无需处理委托
              if(this.form.is3y=='1'){
                await this.login(this.form).catch(() => {})
                await this.$router.push(this.handleRoute())
              }else{
                //启用流程委托，登录先判断该账号密码是否正确，如正确判断是否委托
                //let res = await getConsignByLcFromUser(this.form)
                //if(res.code!=200){
                //  this.$message({message:res.data||'账号或者密码错误!',type:'warning'})
                //}else{
                //  if(res.data.length>0){
                    //已委托，提醒是否取回，取回则继续登录系统
                //    this.$confirm(
                //      `您的账号“${res.data[0].lcFromLuid}(${res.data[0].lcFromOename})”目前处于委托状态，<br/>
                //      目前受托人是：${res.data[0].lcToLuid}(${res.data[0].lcToOename})<br/><br/>如果您要登录系统，必须收回委托，确定继续吗?`,
                //      '温馨提示',
                //      { dangerouslyUseHTMLString: true }
                //    ).then(async ()=>{
                //      let res1 = await cancelConsign({lcFromLuid: this.form.username,lcId:res.data[0].lcId})
                //      if(res1.code == 200){
                //        await this.login(this.form).catch(() => {})
                //        await this.$router.push(this.handleRoute())
                //      }else{
                //        this.$message({message:'任务委托取回失败，请重新登录系统!',type:'warning'})
                //      }
                //    })
                //  }else{
                    //无委托
                //    await this.login(this.form).catch(() => {})
                //    await this.$router.push(this.handleRoute())
                //  }
                //}
                //不启用委托
                await this.login(this.form).catch(() => {})
                await this.$router.push(this.handleRoute())
                
                  
              }
            } finally {
              this.loading = false
            }
        })
      },
      handleLogin(){
        console.log(useCaptcha);
        if(useCaptcha){
          this.showVerify()
        }else{
          this._handleLogin()
        }
      },
      changeCode() {
        this.codeUrl = `https://www.oschina.net/action/user/captcha?timestamp=${new Date().getTime()}`
      },
      success(params){
        // params 返回的二次验证参数
        console.log("成功了。")
        console.log(params)
        this._handleLogin()
      },
      showVerify(){
        //当mode="pop"时,调用组件实例的show方法显示组件
        this.$refs.verify.show();
      }
    },
  }
</script>

<style lang="scss" scoped>
  .sys_title {
    width: 100%;
    display: flex;
    align-items: center;
    position: fixed;
    top: 40px;
    left: 40px;

    img {
      width: 116px;
    }
    p {
      font-size: 40px;
      color: #FFF;
      margin-left: 20px;
    }

  }
  .login-container {
    height: 100vh;
    background: url('~@/assets/login_images/background.jpg') center center fixed
      no-repeat;
    background-size: cover;
  }

  .login-form {
    position: relative;
    max-width: 100%;
    padding: 4.5vh;
    margin: calc((100vh - 475px) / 2) 5vw 5vw;
    overflow: hidden;
    background: url('~@/assets/login_images/login_form.png');
    background-size: 100% 100%;

    .title {
      font-size: 54px;
      font-weight: 500;
      color: $base-color-white;
    }

    .title-tips {
      margin-top: 29px;
      font-size: 26px;
      font-weight: 400;
      color: $base-color-white;
    }

    .login-btn {
      display: inherit;
      width: 100%;
      height: 50px;
      margin-top: 5px;
      border: 0;

      &:hover {
        opacity: 0.9;
      }

      .forget-passwordword {
        width: 100%;
        margin-top: 40px;
        text-align: left;

        .forget-password {
          width: 129px;
          height: 19px;
          font-size: 20px;
          font-weight: 400;
          color: rgba(92, 102, 240, 1);
        }
      }
    }

    .tips {
      margin-bottom: 10px;
      font-size: $base-font-size-default;
      color: $base-color-white;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .title-container {
      position: relative;

      .title {
        margin: 0 auto 40px auto;
        font-size: 34px;
        font-weight: bold;
        color: $base-color-blue;
        text-align: center;
      }
    }

    i {
      position: absolute;
      top: 8px;
      left: 5px;
      z-index: $base-z-index;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    .show-password {
      position: absolute;
      right: 25px;
      left: -35px;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    :deep() {
      .el-form-item {
        padding-right: 0;
        margin: 20px 0;
        color: #454545;
        background: transparent;
        border: 1px solid transparent;
        border-radius: 2px;

        &__content {
          min-height: $base-input-height;
          line-height: $base-input-height;
        }

        &__error {
          position: absolute;
          top: 100%;
          left: 18px;
          font-size: $base-font-size-small;
          line-height: 18px;
          color: $base-color-blue;
        }
      }

      .el-input {
        box-sizing: border-box;

        input {
          height: 48px;
          padding-left: 35px;
          font-size: $base-font-size-default;
          line-height: 58px;
          background: #f6f4fc;
          border: 0;
        }
      }

      .code {
        position: absolute;
        top: 4px;
        right: 4px;
        cursor: pointer;
        border-radius: $base-border-radius;
      }
    }
  }
</style>
