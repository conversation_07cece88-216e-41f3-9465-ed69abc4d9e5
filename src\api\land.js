import request from '@/utils/request'

// 查询土地列表 - 参考buildings页面的实现
export function getLandsList(query) {
  return request({
    url: '/zcgl-land-info/vPageList',
    method: 'get',
    params: query
  })
}

// 查询土地详情
export function getLandDetail(id) {
  return request({
    url: `/zcgl-land-info/get-by-id/${id}`,
    method: 'get'
  })
}

// 导出土地数据
export function exportLands(query) {
  return request({
    url: '/zcgl-land-info/vExport',
    method: 'post',
    data: query
  })
}

// 统计土地数量和价值 - 参考buildings页面的实现
export function getLandsStats() {
  return request({
    url: '/zcgl-land-info/getStatistic',
    method: 'get'
  })
}

// 新增土地信息
export function saveLand(data) {
  return request({
    url: '/zcgl-land-info/save',
    method: 'post',
    data
  })
}

// 更新土地信息
export function updateLand(data) {
  return request({
    url: '/zcgl-land-info/update',
    method: 'post',
    data
  })
}

// 新增或更新土地信息
export function saveOrUpdateLand(data) {
  return request({
    url: '/zcgl-land-info/saveOrUpd',
    method: 'post',
    data
  })
}

// 删除土地信息
export function deleteLand(id) {
  return request({
    url: `/zcgl-land-info/delete-by-id/${id}`,
    method: 'delete'
  })
}

// 上传文件
export function uploadLandFile(data) {
  return request({
    url: '/zcgl-land-info/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
