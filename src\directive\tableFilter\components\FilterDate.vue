<template>
  <div>
    <el-row>
      <div style="padding: 5px; display: flex; justify-content: center">
        <el-date-picker
          v-model="time"
          :append-to-body="false"
          format="yyyy-MM-dd"
          placeholder="选择日期"
          :teleported="false"
          type="date"
          value-format="yyyy-MM-dd"
          @change="dateChanged"
        />
      </div>
    </el-row>
    <el-row>
      <div style="float: right; margin-right: 10px">
        <slot name="buttonSlot"></slot>
      </div>
    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'FilterData',
    data() {
      return {
        time: '',
      }
    },
    watch: {
      value1: {
        time: function (val) {
          this.checkboxList = this.selectItemList.filter((item) =>
            item.includes(val)
          )
        },
      },
    },
    methods: {
      dateChanged(val) {
        this.$emit('dateChanged', val)
      },
      clear() {
        this.time = ''
      },
    },
  }
</script>
<style lang="scss" scoped></style>
