<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
      
        <el-col :span="24">
          <el-form-item label="名称" prop="bcName">
            <el-input v-model.trim="form.bcName" maxlength="30" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="编码" prop="bcCode">
            <el-input v-model.trim="form.bcCode" maxlength="30" show-word-limit clearable :disabled="type=='update'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="bcId"  style="display:none;">
            <el-input v-model.trim="form.bcId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型ID" prop="bcBmId"  style="display:none;">
            <el-input v-model.trim="form.bcBmId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标识" prop="bcSign"  style="display:none;">
            <el-input v-model.trim="form.bcSign" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { busiSortsGetList } from '@/api/busp/busiSorts'
  export default {
    name: 'busiConfigsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          bmStatus:[{name:'有效',value:'有效'},{name:'失效',value:'失效'}],
          bmSort:[],
          bmIsauto:[{text:'否',value:'否'},{text:'是',value:'是'}],
          bmType:[{name:'表',value:'表'},{name:'视图',value:'视图'}],
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const data = await busiSortsGetList(this.searchForm)
        this.optionsData.bmSort = data.data
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>