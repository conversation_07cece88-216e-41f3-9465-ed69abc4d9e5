import request from '@/utils/request'


export function busiConfigsGetData(params) {
  return request({
    url: '/busi-configs/vList',
    method: 'get',
    params,
  })
}

export function loadModelToRedis(data) {
  return request({
    url: '/busi-configs/loadingModel',
    method: 'post',
    data,
  })
}

//保存新增或更新,不记录日志
export function busiConfigsDoSaveOrUpd(data) {
  return request({
    url: '/busi-configs/saveOrUpd',
    method: 'post',
    data,
  })
}
export function busiConfigsDoCopy(data) {
  return request({
    url: '/busi-configs/copy',
    method: 'post',
    data,
  })
}

//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiConfigsDoDelete(data) {
  return request({
    url: '/busi-configs/delete',
    method: 'post',
    data,
  })
}

