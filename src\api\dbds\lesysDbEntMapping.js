import request from '@/utils/request'

//列表分页加载数据
export function lesysDbEntMappingGetList(params) {
  return request({
    url: '/lesys-db-ent-mapping/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbEntMappingDoSave(data) {
  return request({
    url: '/lesys-db-ent-mapping/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbEntMappingDoUpdate(data) {
  return request({
    url: '/lesys-db-ent-mapping/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbEntMappingDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-ent-mapping/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbEntMappingDoDelete(data) {
  return request({
    url: '/lesys-db-ent-mapping/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbEntMappingDoExport(data) {
  return request({
    url: '/lesys-db-ent-mapping/vExport',
    method: 'post',
    data,
  })
}
