<template>
  <div class="Form-container" >
    <el-form
        ref="form"
        label-width="160px"
        :model="form">
      <el-row :gutter="20" v-for="(item0, index0) in queryList" :key="index0">
        <el-col :span="24/queryFormCols" v-for="(item,index) in item0" :key="item.bfcId">
          <el-form-item v-if="item.bfIspk == '是' || item.bfcEdittype == 'HIDDEN' " :label="item.bfName" :prop="item.bfField"  style="display:none;" >
            <el-input v-if="item.bfIspk == '是' || item.bfcEdittype == 'HIDDEN' " v-model.trim="form[item.bfField]" type="hidden"></el-input>
          </el-form-item>
          <el-form-item v-if="item.bfIspk == '否' && item.bfcEdittype != 'HIDDEN' " :label="item.bfName" :prop="item.bfField" >
            <span v-if="item.bfcEdittype == 'TEXT' || item.bfcEdittype == 'TEXTS'">
              <el-input v-model.trim="form[item.bfField]" clearable ></el-input>
            </span>
            <span v-else-if="item.bfcEdittype == 'NUMBER'">
              <span v-if="item.bfcIsrange=='是'">
                <el-input v-model.trim="form[item.bfField+'Start']"  style='width:48%' clearable ></el-input>~
                <el-input v-model.trim="form[item.bfField+'End']"  style='width:48%' clearable ></el-input>
              </span>
              <span v-else-if="item.bfcIsrange=='否'">
                <el-input v-model.trim="form[item.bfField]" clearable ></el-input>
              </span>   
            </span>
            <span v-else-if="item.bfcEdittype == 'DATE' || item.bfcEdittype == 'TIMESTAMP'">
              <span v-if="item.bfcIsrange=='是'">
                <el-date-picker v-model.trim="form[item.bfField+'Start']" style='width:48%' clearable type="date" value-format="yyyy-MM-dd"></el-date-picker>~
                <el-date-picker v-model.trim="form[item.bfField+'End']"   style='width:48%' clearable type="date" value-format="yyyy-MM-dd"></el-date-picker>
              </span>
              <span v-else-if="item.bfcIsrange=='否'">
                <el-date-picker v-model.trim="form[item.bfField]"         style='width:100%' clearable type="date" value-format="yyyy-MM-dd"></el-date-picker>
              </span> 
            </span>
            <span v-else-if="item.bfcEdittype == 'TEXT-SELECT' || item.bfcEdittype == 'TEXT-SELECTS'">
              <el-select v-model.trim="form[item.bfField]" style="width:100%">
                <el-option v-for="item in optionsData[item.bfField]" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
              </el-select>
            </span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { getOptionsLpvName,getOptionsLpvName2 } from '@/api/busp/busiUtil'
  export default {
    name: 'MasterQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      },
      queryList: {
        type:Array
      },
      queryOptionData: {
        type:Object
      },
      queryFormCols: {
        type: Number,
        default: 2
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:this.queryOptionData
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectData()
    },
    methods: {
      async getSelectData(){
        this.optionsData = await getOptionsLpvName2(this.queryList,{'defaultOption':{label:'全部',value:''}})
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>