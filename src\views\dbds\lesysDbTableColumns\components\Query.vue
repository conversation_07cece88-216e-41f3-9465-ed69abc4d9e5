<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="24">
          <el-form-item label="表id" prop="ldtcLdtId">
            <el-input v-model.trim="form.ldtcLdtId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="表名称" prop="ldtcLdtTableName">
            <el-input v-model.trim="form.ldtcLdtTableName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="列名称" prop="ldtcColumn">
            <el-input v-model.trim="form.ldtcColumn" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="列描述" prop="ldtcComment">
            <el-input v-model.trim="form.ldtcComment" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="列类型" prop="ldtcType">
            <el-input v-model.trim="form.ldtcType" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="列长度" prop="ldtcLength">
            <el-input-number v-model.trim="form.ldtcLengthStart" clearable></el-input-number> - <el-input-number v-model.trim="form.ldtcLengthEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="数值小数" prop="ldtcDataScale">
            <el-input-number v-model.trim="form.ldtcDataScaleStart" clearable></el-input-number> - <el-input-number v-model.trim="form.ldtcDataScaleEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="数值长度" prop="ldtcDataPrecision">
            <el-input-number v-model.trim="form.ldtcDataPrecisionStart" clearable></el-input-number> - <el-input-number v-model.trim="form.ldtcDataPrecisionEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否主键" prop="ldtcIsprimary">
            <el-input v-model.trim="form.ldtcIsprimary" clearable ></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'lesysDbTableColumnsQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>