<template>
  <div class="vab-system">
    <el-dropdown @command="handleCommand">
      <span class="el-dropdown-link">
        {{ currentSystem }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in systemList"
          :key="item.laId"
          :command="item"
        >
          {{ item.laName }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex'
  import request from '@/utils/request'
  import { getList } from '@/api/router'
  export default {
    name: 'VabSystem',
    data() {
      return {
        systemList: [],
        currentSystem: '',
      }
    },
    computed: {
      ...mapGetters({
        title: 'settings/title',
        theme: 'settings/theme',
        loginUser: 'user/loginUser',
      }),
    },

    created() {
      this.getProjectList()
    },
    methods: {
      ...mapActions({
        changeTitle: 'settings/changeTitle',
        changeLayout: 'settings/changeLayout',
        changeMenuMeta: 'routes/changeMenuMeta',
      }),
      async getProjectList() {
        let res = await request({
          url:
            '/lesys-appl/method=selectRootApplByLoginUserID&LoginUserID=' +
            this.loginUser.luId,
          method: 'get',
        })
        if (res.code == 0) {
          this.systemList = res.data
          this.currentSystem = res.data[0].laName
        }
      },
      async handleCommand(command) {
        this.changeTitle(command.laName)
        this.currentSystem = command.laName
        let params = {
          laId: command.laId,
          luId: this.loginUser.luId
        }
        let res = await getList(params)
        this.changeMenuMeta(res.data.list)
      },
    },
  }
</script>

<style scoped>
  .el-dropdown-link {
    cursor: pointer;
  }
</style>
