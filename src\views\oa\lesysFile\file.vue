<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px">
          <el-col :span="24">
            <el-form-item label="文件名称" prop="fileName">
              <el-select v-model="fileName" placeholder="请选择" style="width:100%;">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="" prop="fileNameButton">
             <el-button type="primary">上传<i class="el-icon-upload   el-icon--right"></i></el-button>
             <el-button type="warning">下载<i class="el-icon-download el-icon--right"></i></el-button> 
             <el-button type="success">查看<i class="el-icon-view     el-icon--right"></i></el-button>
             <el-button type="danger" >删除<i class="el-icon-delete   el-icon--right"></i></el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>

  export default {
    name: 'lesysVueFile',
    props: {

    },
    data() {
      return {
        tableForm: {},
        options: [
          
        ], 
        fileName:'' 
      }
    },
    created() {
      
    },
    methods: {
      async getSelectOptions(){
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>