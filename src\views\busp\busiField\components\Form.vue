<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="140px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="属性名称" prop="bfName">
            <el-input v-model.trim="form.bfName" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="属性字段名" prop="bfColumn">
            <el-input v-model.trim="form.bfColumn" maxlength="50" show-word-limit clearable  :disabled="type=='update'"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="属性字段长度" prop="bfLength">
            <el-input-number v-model.trim="form.bfLength" style="width:100%" :precision="0" :min="0" :max="4000"></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="属性字段类型" prop="bfColumnType">
            <el-select v-model.trim="tableForm.bfColumnType"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bfColumnType" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        
        <!--el-col :span="12">
          <el-form-item label="字段分类" prop="bfSort">
            <el-select v-model.trim="tableForm.bfSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bfSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col-->

        <!--el-col :span="12">
          <el-form-item label="属性实体名" prop="bfField">
            <el-input v-model.trim="form.bfField" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col-->

        <el-col :span="12">
          <el-form-item label="是否有效" prop="bfEffState" style="display:none;">
            <el-select v-model.trim="tableForm.bfEffState"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="是否接口列" prop="bfInterface" style="display:none;">
            <el-select v-model.trim="tableForm.bfInterface"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="使用状态" prop="bfUseState" style="display:none;">
            <el-select v-model.trim="tableForm.bfUseState"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bfUseState" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="属性级别" prop="bfLevel">
            <el-select v-model.trim="tableForm.bfLevel"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bfLevel" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="属性顺序" prop="bfOrder">
            <el-input-number v-model.trim="form.bfOrder" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="是否属性唯一标识" prop="bfUnion">
             <el-select v-model.trim="tableForm.bfUnion"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="是否主键" prop="bfIspk">
             <el-select v-model.trim="tableForm.bfIspk"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="是否创建" prop="bfIscreate">
            <el-select v-model.trim="tableForm.bfIscreate"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否存在" prop="bfIsreal" style="display:none;">
             <el-select v-model.trim="tableForm.bfIsreal"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.isToF" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="主键" prop="bfId"  style="display:none;">
            <el-input v-model.trim="form.bfId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模型ID" prop="bfBmId" style="display:none;">
            <el-input v-model.trim="form.bfBmId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表名" prop="bfBmTable" style="display:none;">
            <el-input v-model.trim="form.bfBmTable" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示列名" prop="bfTitle" style="display:none;">
            <el-input v-model.trim="form.bfTitle" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性类型" prop="bfType" style="display:none;">
            <el-input v-model.trim="form.bfType" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性实体类型" prop="bfFieldType" style="display:none;">
            <el-input v-model.trim="form.bfFieldType" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'busiFieldForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          bfSort:[{value:'业务字段',label:'业务字段'},{value:'编码字段',label:'编码字段'},{value:'增量字段',label:'增量字段'}],
          isToF:[{value:'是',label:'是'},{value:'否',label:'否'}],
          bfUseState:[{value:'未启用',label:'未启用'},{value:'启用',label:'启用'},{value:'停用',label:'停用'}],
          bfLevel:[{value:'业务属性',label:'业务属性'},{value:'管理属性',label:'管理属性'}],
          bfColumnType:[{value:'text',label:'字符'},{value:'date',label:'时间'},{value:'number',label:'数值'}]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>