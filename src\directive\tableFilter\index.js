import Vue from 'vue'
import TableFilter from './components/index.vue'

/**
 * @description 创建点击的图标
 * @param i
 * @return {HTMLDivElement}
 */
export function createFilterIcons(i) {
  const divDom = document.createElement('div')
  const iDom = document.createElement('i')
  divDom.appendChild(iDom)
  // divDom.style.position = 'absolute'
  divDom.style.transformOrigin = 'left center'
  divDom.style.zIndex = 1000
  iDom.classList.add('el-icon-s-open')
  iDom.classList.add(`vnode${i}`)
  iDom.style.cursor = 'pointer'
  return divDom
}

/**
 * @description 创建自定义组件的实例
 * @param handle
 * @param vm
 * @param columnVueComponent
 * @return {Vue<Record<string, any>, Record<string, any>, never, never, (event: string, ...args: any[]) => Vue> | UnwrapNestedRefs<UnwrapMixinsType<IntersectionMixin<ComponentOptionsBase<any, any, any, any, any, any, any, any, any, any>>, "D">> | object | UnwrapMixinsType<IntersectionMixin<ComponentOptionsBase<any, any, any, any, any, any, any, any, any, any>>, "M"> | ExtractComputedReturns<UnwrapMixinsType<IntersectionMixin<ComponentOptionsBase<any, any, any, any, any, any, any, any, any, any>>, "C">> | UnwrapMixinsType<IntersectionMixin<ComponentOptionsBase<any, any, any, any, any, any, any, any, any, any>>, "P"> | Record<never, any> | ShallowUnwrapRef<UnwrapMixinsType<IntersectionMixin<ComponentOptionsBase<any, any, any, any, any, any, any, any, any, any>>, "B">> | {}}
 */
function createComponent(handle, vm, columnVueComponent) {
  return new Vue({
    render: (h) =>
      h(TableFilter, {
        props: {
          handle,
          vm,
          columnVueComponent,
        },
      }),
  }).$mount()
}

/**
 * @description 用div 将新创建组件包一层，然后挂载到body上
 * @param handle 外层传进来的参数，fn : 搜索的函数名（key），ref: table的ref名（key） ， tableList: 放表格数据的对象名（key）
 * @param vm 外层页面的vm实例
 * @param columnVueComponent 列信息
 * @return {HTMLDivElement}
 */

export function createFilterBody(handle, vm, columnVueComponent) {
  const container = document.createElement('div')
  container.classList.add('tableFilter')
  document.body.appendChild(container)
  const componentInstance = createComponent(handle, vm, columnVueComponent)
  container.appendChild(componentInstance.$el)
  container.style.display = 'none'
  return container
}

/**
 *
 * @param container 新创建的div
 * @param left 点击横坐标
 * @param top  点击竖坐标
 */
export function setFilterBodyCss(container, left, top) {
  container.style.height = '300px'
  container.style.width = '100px'
  container.style.position = 'absolute'
  container.style.left = left + 'px'
  container.style.top = top + 'px'
  container.style.transformOrigin = 'left center'
  container.style.zIndex = 1000
  container.style.display =
    container.style.display === 'none' ? 'block' : 'none'
}

export function clickhandler(
  event,
  cache,
  vnodeCache,
  columnDom,
  vnode,
  fn,
  ref,
  tableList,
  filterParmas,
  getDirectiveParmas
) {
  if (event.target.matches('.el-icon-s-open,.el-icon-s-open *')) {
    const index = event.target.classList[1]
    let container = {}
    const tableFilterList = document.body.querySelectorAll('.tableFilter')
    if (tableFilterList?.length) {
      const domList = [...tableFilterList]
      domList.map((item) => item.remove())
    }
    if (Object.keys(cache).includes(`${columnDom[index].classList[0]}`)) {
      container = cache[`${columnDom[index].classList[0]}`]
      document.body.appendChild(container)
    } else {
      container = createFilterBody(
        { fn, ref, tableList, filterParmas, getDirectiveParmas },
        vnode.componentInstance.$parent,
        vnodeCache[index]
      )
      columnDom[index].style.position = 'relative'
      cache[`${columnDom[index].classList[0]}`] = container

      //将新创建的dom数据存到私有属性，方便销毁
      columnDom[index]._container = container
    }
    const left = event.clientX + 10
    const top = event.clientY - 10
    setFilterBodyCss(container, left, top)
  } else if (event.target.matches('.tableFilterLi,.tableFilterLi *')) {
    Object.values(columnDom)
      .filter((item) => item._container)
      .map((item) => (item._container.style.display = 'block'))
  } else {
    Object.values(columnDom)
      .filter((item) => item._container)
      .map((item) => item._container.remove())
  }
}
