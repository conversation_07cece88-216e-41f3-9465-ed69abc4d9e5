<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <viewLeswfe15TaskInstSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="viewLeswfe15TaskInstTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"
      @handleViewFlow="handleViewFlow"
      @handleDealFlow="handleDealFlow"
      @handleTask="handleTask"/>

    <el-table
      ref="viewLeswfe15TaskInstTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="ViewLeswfe15TaskInst"
      row-key="taskId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>

        <template slot-scope="scope">
          <span v-if="item.slot === 'taskIsNew' ">
             <span v-if="scope.row.taskIsNew==0">
                否
             </span>
             <span v-else>
                是
            </span>
          </span>
          <span v-else>
            {{ scope.row[item.prop] }}
          </span>
        </template>

      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleDealFlow(row)">办理</el-button>
          <el-button type="text" @click="handleViewFlow(row)">查看</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-search ref="viewLeswfe15TaskInstQuerySearch">
      <viewLeswfe15TaskInstQuery
        ref="viewLeswfe15TaskInstQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { viewLeswfe15TaskInstGetList,
           viewLeswfe15TaskInstDoExport } from '@/api/workflow/viewLeswfe15TaskInst'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import viewLeswfe15TaskInstSearch from './components/Search.vue'
  import viewLeswfe15TaskInstForm from './components/Form.vue'
  import viewLeswfe15TaskInstQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import { LesWorkFlowApi,LesWorkFlowRsa,LesWorkFlowShow } from '@/utils/lesworkFlow'

  export default {
    name: 'viewLeswfe15TaskInst',
    props: {
      gheight: {
        type:Number
      },
      instSort: {
        type:String,
        default:''
      },
      cjpiSort :{
        type:String,
        default:''
      },
    },
    components: {
      TableEdit,
      TableSearch,
      viewLeswfe15TaskInstSearch,
      viewLeswfe15TaskInstForm,
      viewLeswfe15TaskInstQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {instName:'',nodeName:'',instCreateUxm:'',instCreateTimeStart:undefined,instCreateTimeEnd:undefined},
        queryForm: {instName:'',nodeName:'',instCreateUxm:'',instCreateTimeStart:undefined,instCreateTimeEnd:undefined},
        form: {},
        rules: {
          
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['实例名称','任务名称','上一节点名称','办理人','任务时间','是否新任务','创建人','创建时间'],
        columns: [
                { prop:'instName', label:'实例名称', width:'auto' , sortable:false  },
                { prop:'nodeName', label:'任务名称', width:'auto' , sortable:false  },
                { prop:'lastNodeName', label:'上一节点名称', width:'auto' , sortable:false  },
                { prop:'taskUxm', label:'办理人', width:'auto' , sortable:false  },
                { prop:'taskTime', label:'任务时间', width:'auto' , sortable:false  },
                { prop:'taskIsNew', label:'是否新任务', width:'auto' , sortable:false,slot:'taskIsNew'  },
                { prop:'instCreateUxm', label:'创建人', width:'auto' , sortable:false  },
                { prop:'instCreateTime', label:'创建时间', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          instName:'',
          taskDoTime:'0',
          taskUid:'',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.searchForm.taskUid = this.loginUser.luId
      this.searchForm.instSort = this.instSort
      this.searchForm.cjpiSort = this.cjpiSort
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        
      },
      // 弹窗编辑取消按钮
      close() {
        
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleDealFlow(row)
      },
      handleViewFlow(row) {
        if(row){
          this.row = row
        }
        if(this.row.instId){
          let res = LesWorkFlowRsa(this.row.instId,this.username);
          res.then(res => {
            var win = LesWorkFlowShow(LesWorkFlowApi(null,"vurl") + "?sid=wf&uk="+res, 'inst_view_win_' + this.row.instId);
            if (win) win.focus();
            win.addEventListener("message", (event) => {
              if(event.data=='流程查看关闭'){
                this.fetchData();
              }
            }, false);
          })
        }
      },
      handleDealFlow(row){
        if(row){
          this.row = row
        }
        if(this.row.taskId){
          let res = LesWorkFlowRsa(this.row.taskId,this.loginUser.luId);
          res.then(res => {
            var win = LesWorkFlowShow(LesWorkFlowApi(null,"durl") + "?sid=wf&uk="+res, 'inst_view_win_' + this.row.taskId);
            if (win) win.focus();
            win.addEventListener("message", (event) => {
              if(event.data=='流程办理关闭'){
                this.fetchData();
                this.$refs['viewLeswfe15TaskInstTs'].reloadTaskUser(this.searchForm.taskUid)
              }
            }, false);
          })
        }
      },
      // 编辑行数据
      handleEdit(row) {
        
      },
      // 删除行数据
      handleDelete(row) {
        
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['viewLeswfe15TaskInstQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.viewLeswfe15TaskInstQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.viewLeswfe15TaskInstQuerySearch.close()
        this.fetchData()
      },
      handleTask(luId){
        this.searchForm.taskUid = luId
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await viewLeswfe15TaskInstGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"taskTime":{"celltype":"date"},"instCreateTime":{"celltype":"date"}},
                      "fileName":"流程任务视图.xls",
                      "isnumber":true,
                      "excelTitle":"流程任务视图",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#ViewLeswfe15TaskInst",params)
        const { msg }  =  await viewLeswfe15TaskInstDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"流程任务视图模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#ViewLeswfe15TaskInst",params)
        const { msg }  =  await viewLeswfe15TaskInstDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>