import request from '@/utils/request'
import requestForm from '@/utils/requestForm'
import { encryptedData } from '@/utils/encrypt'
import { loginRSA } from '@/config'

/**
 * post请求，参数通过body传递，form提交
 * @param data 
 * @returns 
 */
export async function loginForm(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return requestForm({
    url: '/oauth3/token',
    method: 'post',
    data
  })
}

/**
 * post请求，参数通过url传递
 * @param params 
 * @returns 
 */
export async function login(params) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/oauth3/token',
    method: 'post',
    params
  })
}
/**
 * post请求，参数通过body传递，且序列化
 * @param data 
 * @returns 
 */
export async function login1(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/oauth3/token1',
    method: 'post',
    data
  })
}
/**
 * post请求，参数通过body传递，form提交序列化字符串格式
 * @param data 
 * @returns 
 */
export async function login2(data) {
  const newParam = new URLSearchParams(data)
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/oauth3/token2',
    method: 'post',
    data:newParam
  })
}

export async function socialLogin(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/socialLogin',
    method: 'post',
    data,
  })
}

export function getUserInfo(params) {
  return request({
    url: '/lesysSys/getByLuId',
    method: 'get',
    params,
  })
}



export function logout(params) {
  return request({
    url: '/logout',
    method: 'get',
    params,
  })
}

export function register(data) {
  return request({
    url: '/register',
    method: 'post',
    data,
  })
}
