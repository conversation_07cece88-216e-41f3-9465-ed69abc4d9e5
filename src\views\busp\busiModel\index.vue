<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiModelSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="busiModelTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="busiModelTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiModel"
      row-key="bmId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="200"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <!--el-button type="text" @click="handleSure(row)">确认</el-button-->
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
          &nbsp;
          <el-dropdown trigger="click" @command="handleCommand">
            <el-button type="text">
              配置<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="field" icon="el-icon-sort-up">属性配置</el-dropdown-item>
              <el-dropdown-item command="list"  icon="el-icon-top">列表配置</el-dropdown-item>
              <el-dropdown-item command="form"  icon="el-icon-bottom">表单配置</el-dropdown-item>
              <el-dropdown-item command="sech"  icon="el-icon-zoom-in">查询配置</el-dropdown-item>
              <el-dropdown-item command="reds"  icon="el-icon-remove-outline">更新Redis</el-dropdown-item>
              <el-dropdown-item command="sign"  icon="el-icon-search">查看标识</el-dropdown-item>
              
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="busiModelEdit">
      <busiModelForm
        ref="busiModelForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="busiModelQuerySearch">
      <busiModelQuery
        ref="busiModelQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="busiFieldSel" :fullscreen="true">
      <busiField
        ref="busiField"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldSureSel" :fullscreen="true">
      <busiFieldSure
        ref="busiFieldSure"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldSureSure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 认
        </el-button>
        <el-button
          type="primary"
          @click="busiFieldSureclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          关 闭
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigListSel" :fullscreen="true">
      <busiFieldConfigList
        ref="busiFieldConfigList"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigListclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigFormSel" :fullscreen="true">
      <busiFieldConfigForm
        ref="busiFieldConfigForm"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigFormclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigSearchSel" :fullscreen="true">
      <busiFieldConfigSearch
        ref="busiFieldConfigSearch"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigSearchclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="sysLoadingTip" popWidth="500px">
      <sysLoading
        ref="sysLoading"
        :tips="tips"
        slot="form"/>
      <template slot="footerCont">
        
      </template>
    </table-edit >

    <table-edit ref="busiConfigsSel" :fullscreen="true">
      <busiConfigs
        ref="busiConfigs"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiConfigsclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

  </div>
</template>

<script>
  import { busiModelDoDeleteELog,
           busiModelGetList,
           busiModelDoSaveOrUpdLog,
           busiModelDoCopy,
           busiModelDoDef,
           busiModelDoExport,
           busiModelDoUpdate,
           loadModelToRedis,
           busiModelCreateTable,
           busiModelCreateTableOth } from '@/api/busp/busiModel'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiModelSearch from './components/Search.vue'
  import busiModelForm from './components/Form.vue'
  import busiModelQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import busiConfigs from '@/views/busp/busiConfigs/index.vue'
  import busiField from '@/views/busp/busiField/index.vue'
  import busiFieldSure from '@/views/busp/busiField/indexSure.vue'
  import busiFieldConfigList from '@/views/busp/busiFieldConfig/index_list.vue'
  import busiFieldConfigForm from '@/views/busp/busiFieldConfig/index_form.vue'
  import busiFieldConfigSearch from '@/views/busp/busiFieldConfig/index_search.vue'
  import sysLoading from '@/views/common/SysLoading.vue'
  import { Loading } from 'element-ui'
  export default {
    name: 'busiModel',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiModelSearch,
      busiModelForm,
      busiModelQuery,
      busiField,
      busiConfigs,
      busiFieldSure,
      busiFieldConfigList,
      busiFieldConfigForm,
      busiFieldConfigSearch,
      sysLoading
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {bmDesc:'',bmEntity:'',bmName:'',bmSign:'',bmSort:'',bmTable:''},
        queryForm: {bmDesc:'',bmEntity:'',bmName:'',bmSign:'',bmSort:'',bmTable:''},
        form: {bmDesc:'',bmEntity:'',bmName:'',bmSign:'',bmSort:'',bmTable:''},
        rules: {
          bmDesc: [
            { required: false, message: '请输入描述', trigger: 'blur' }
          ],
          bmEntity: [
            { required: false, message: '请输入实体名', trigger: 'blur' }
          ],
          bmName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ],
          bmSign: [
            { required: false, message: '请输入标识', trigger: 'blur' }
          ],
          bmSort: [
            { required: false, message: '请输入分类', trigger: 'blur' }
          ],
          bmTable: [
            { required: true, message: '请输入模型表名', trigger: 'blur' }
          ],
          bmCode: [
            { required: true, message: '请输入模型编码', trigger: 'blur' }
          ],
          bmType: [
            { required: true, message: '请输入模型实体类型', trigger: 'blur' }
          ],
          bmIscreate: [
            { required: true, message: '请输入模型实体是否创建', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['模型名称','模型描述','模型编码','是否有效','是否创建'],
        columns: [
                { prop:'bmName'   , label:'模型名称'  , width:'auto' , sortable:false  },
                { prop:'bmDesc'   , label:'模型描述'  , width:'auto' , sortable:false  },
                { prop:'bmCode'   , label:'模型编码'  , width:'auto' , sortable:false  },
                { prop:'bmStatus' , label:'是否有效'  , width:'auto' , sortable:false  },
                //{ prop:'bmIsauto' , label:'是否订阅'  , width:'auto' , sortable:false  },
                //{ prop:'bmConfirm', label:'是否确认'  , width:'auto' , sortable:false  },
                //{ prop:'bmTb64'   , label:'模型标识'  , width:'auto' , sortable:false  },
                //{ prop:'bmType'   , label:'实体类型'  , width:'auto' , sortable:false  },
                { prop:'bmIscreate' , label:'是否创建'  , width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bmSign:this.bmSign,
          pageNo: 1,
          pageSize: 20,
          sortField:'bmCode',
          sortOrder:'ascending'
        },
        tips:'模型实体创建中。。。。。'
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiModelForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.bmId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }

            const regex = /^[a-zA-Z0-9_]+$/
            if(!this.form.bmId){
              if(!regex.test(this.form.bmTable)){
                this.$message({message:'输入的模型表名错误，请输入英文、数字及下划线!',type:'warning'})
                return
              }
            }

            const regex1 = /^[a-zA-Z0-9]+$/
            if(!this.form.bmId){
              if(!regex1.test(this.form.bmCode)){
                this.$message({message:'输入的模型编码错误，请输入英文、数字!',type:'warning'})
                return
              }
            }

            let msg = {}
            if(this.editType == 'copy'){
              msg  = await busiModelDoCopy( this.form )
            }else{
              msg  = await busiModelDoSaveOrUpdLog( this.form )
            }
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:msg.msg||'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiModelEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      async handleAdd(command) {
        if(command == 'add'){
          this.form={'bmSign':this.bmSign,'bmStatus':'有效','bmVersion':'0','bmConfirm':'否','bmType':'表'}
          this.editType = 'add'
          this.$refs['busiModelEdit'].showEdit('添加模型')
        }
        else if(command == 'cop'){
          if(this.row && this.row.bmId){
            this.editType = 'copy'
            this.$refs['busiModelEdit'].showEdit('复制')
            this.form = Object.assign({},this.row)
            this.form.bmCode = ''
          }else{
            this.$message({message:'请选择模型!',type:'warning'})
          }
        }
        else if(command == 'db'){
          if(this.row && this.row.bmId){
            this.$baseConfirm('确定创建模型实体吗？', null, async () => {
              let loadingInstance = Loading.service({ fullscreen: true,text:'模型实体创建中。。。。。。。' });
              const  msg  = await busiModelCreateTable(this.row)
              if(msg.code == 200) {
                this.$nextTick(() => {
                  loadingInstance.close()
                })
                this.$message({message:'操作成功!',type:'success'})
                this.fetchData()
                this.row.bmIscreate = '是'
              }else{
                this.$nextTick(() => { 
                  loadingInstance.close()
                })
                this.$message({message:msg.msg||'操作失败!',type:'warning'})
              }
            })
          }
        }
        else if(command == 'dbchg' || command == 'dbgat'){
          if(this.row.bmIscreate!='是'){
            this.$message({message:'请先创建模型主数据实体!',type:'warning'})
          }else{
            this.$baseConfirm('确定创建模型'+(command=='dbchg'?'变动':'采集')+'实体吗？', null, async () => {
              let loadingInstance = Loading.service({ fullscreen: true,text:'模型实体创建中.....' });
              const  msg  = await busiModelCreateTableOth({bmId:this.row.bmId,bmtSign:command})
              if(msg.code == 200) {
                this.$nextTick(() => {
                  loadingInstance.close()
                })
                this.$message({message:'操作成功!',type:'success'})
              }else{
                this.$nextTick(() => { 
                  loadingInstance.close()
                })
                this.$message({message:msg.msg||'操作失败!',type:'warning'})
              }
            })
          }
        }
        else if(command == 'field'){
          this.handleSet(this.row)
        }
        else if(command == 'listset'){
          this.$refs['busiFieldConfigListSel'].showEdit('列表配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigList.init(this.row)
          })
        }
        else if(command == 'formset'){
          this.$refs['busiFieldConfigFormSel'].showEdit('表单配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigForm.init(this.row)
          })
        }
        else if(command == 'searchs'){
          this.$refs['busiFieldConfigSearchSel'].showEdit('查询配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigSearch.init(this.row)
          })
        }
        else if(command == 'def'){
          this.$baseConfirm('确定刷新默认配置数据？', null, async () => {
            const  msg  = await busiModelDoDef(this.row)
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
            }else{
              this.$message({message:msg.msg||'操作失败!',type:'warning'})
            }
          })
        }
        else if(command == 'seting'){
          this.$refs['busiConfigsSel'].showEdit('配置')
          this.$nextTick(()=> {
            this.$refs.busiConfigs.init(this.row)
          })
        }
      },

      handleCommand(command){
        this.row.bfcBcId = this.row.bmCode+'_DEF'
        if(command=='list'){
          this.$refs['busiFieldConfigListSel'].showEdit('列表配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigList.init(this.row)
          })
        }else if(command=='form'){
          this.$refs['busiFieldConfigFormSel'].showEdit('表单配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigForm.init(this.row)
          })
        }else if(command == 'sech'){
          this.$refs['busiFieldConfigSearchSel'].showEdit('查询配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigSearch.init(this.row)
          })
        }else if(command == 'reds'){
          this.handleRedis(this.row)
        }else if(command == 'field'){
          this.handleSet(this.row)
        }else if(command == 'sign'){
          this.$prompt('',{
            showCancelButton: false,
            inputValue:this.row.bmTb64
          }).then(({ value }) => {
            
          }).catch(() => {      
          })
        }
      },

      handleSet(row) {
        if(row.bmConfirm=='是'){
          this.$refs['busiFieldSureSel'].showEdit('模型预览')
          this.$nextTick(()=> {
            this.$refs.busiFieldSure.init(row)
          })
        }else{
          this.$refs['busiFieldSel'].showEdit('模型属性')
          this.$nextTick(()=> {
            this.$refs.busiField.init(row)
          })
        }  
      },
      handleSure(row) {
        this.row = row
        this.$refs['busiFieldSureSel'].showEdit('模型确认')
        this.$nextTick(()=> {
          this.$refs.busiFieldSure.init(row)
        })
      },
      busiFieldSureSure(){
        if(this.row.bmConfirm=='否'){
          this.$baseConfirm('模型确认吗？', null, async () => {
            const  msg  = await busiModelDoUpdate( {'bmId':this.row.bmId,'bmConfirm':'是'} )
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              this.fetchData()
              this.$refs.busiFieldSureSel.close()
            }else{
              this.$message({message:msg.msg||'操作失败!',type:'warning'})
            }
          })
        }else{
          this.$refs.busiFieldSureSel.close()
        }
      },
      busiFieldSureclose(){
        this.$refs.busiFieldSureSel.close()
      },
      busiFieldclose(){
        this.$refs.busiFieldSel.close()
      },
      busiConfigsclose(){
        this.$refs.busiConfigsSel.close()
      },
      busiFieldConfigListclose(){
        this.$refs.busiFieldConfigListSel.close()
      },
      busiFieldConfigFormclose(){
        this.$refs.busiFieldConfigFormSel.close()
      },
      busiFieldConfigSearchclose(){
        this.$refs.busiFieldConfigSearchSel.close()
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['busiModelEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.bmId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            let row1 = {} 
            row1.logData = JSON.stringify(row) 
            row1.logDesc = '删除模型模型表数据' 
            row1.bmId = row.bmId 
            const msg = await busiModelDoDeleteELog(row1)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      handleRedis(row) {
        this.$baseConfirm('确定更新Redis模型配置', null, async () => { 
          const msg = await loadModelToRedis(row)
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['busiModelQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.busiModelQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.busiModelQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await busiModelGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"模型模型表.xls",
                      "isnumber":true,
                      "excelTitle":"模型模型表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#BusiModel",params)
        const { msg }  =  await busiModelDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"模型模型表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#BusiModel",params)
        const { msg }  =  await busiModelDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>