<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysDbDsSearchSimple
      :queryForm="searchForm"
      ref="lesysDbDsTs"
      @handleSearch="handleSearch"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysDbDsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="lesysDbDs"
      row-key="ldId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-search ref="lesysDbDsQuerySearch">
      <lesysDbDsQuerySimple
        ref="lesysDbDsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { lesysDbDsGetPage } from '@/api/dbds/lesysDbDs'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysDbDsSearchSimple from './components/searchSimple.vue'
  import lesysDbDsQuerySimple from './components/querySimple.vue'

  export default {
    name: 'lesysDbDsSimple',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableSearch,
      lesysDbDsSearchSimple,
      lesysDbDsQuerySimple
    },
    data() {
      return {
        fullscreenLoading: false,
        queryFormDf: {ldName:'',ldType:'',ldHost:'',ldUsername:'',ldPassword:'',ldDb:'',ldPort:'',ldShort:'',ldUrl:'',ldSchema:'',ldPath:'',ldSort:''},
        queryForm: {ldName:'',ldType:'',ldHost:'',ldUsername:'',ldPassword:'',ldDb:'',ldPort:'',ldShort:'',ldUrl:'',ldSchema:'',ldPath:'',ldSort:''},
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','ip','实例','端口','简称'],
        columns: [
                { prop:'ldName', label:'名称', width:'auto' , sortable:false  },
                { prop:'ldHost', label:'ip', width:'auto' , sortable:false  },
                { prop:'ldDb', label:'实例', width:'auto' , sortable:false  },
                { prop:'ldPort', label:'端口', width:'auto' , sortable:false  },
                { prop:'ldShort', label:'简称', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        //this.row = val
        this.row = val
        try{
          this.$emit("lesysDbDsSimple",this.row);
        }catch(Ex){}
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysDbDsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysDbDsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          if(this.queryForm[key]!=''){
            this.searchForm[key] = this.queryForm[key]
          }
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysDbDsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysDbDsGetPage(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>