<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form">
       
        <el-col :span="24">
          <el-form-item label="模型名称" prop="bmrTarBmId">
            <el-select v-model.trim="tableForm.bmrTarBmId"   clearable   style="width:100%" @change="bmChange">
              <el-option v-for="item in optionsData.bmrTarBmId" :key="item.bmId" :label="item.bmName" :value="item.bmId"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模型列名" prop="bmrTarColumn">
            <el-select v-model.trim="tableForm.bmrTarColumn"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.bmrTarColumn" :key="item.bfColumn" :label="item.bfName" :value="item.bfColumn"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="主键" prop="bmrTarBmBt64"  style="display:none;">
            <el-input v-model.trim="tableForm.bmrTarBmBt64" type="hidden"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="主键" prop="bmrId"  style="display:none;">
            <el-input v-model.trim="tableForm.bmrId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="bmrSorBmId"  style="display:none;">
            <el-input v-model.trim="tableForm.bmrSorBmId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="bmrSorBmBt64"  style="display:none;">
            <el-input v-model.trim="tableForm.bmrSorBmBt64" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="bmrSorColumn"  style="display:none;">
            <el-input v-model.trim="tableForm.bmrSorColumn" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { busiFieldGetList } from '@/api/busp/busiField'
  import { busiModelGetData } from '@/api/busp/busiModel'
  export default {
    name: 'busiModelReaForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          bmrTarBmId:[],
          bmrTarColumn:[]
        },
        bmrTarBmIdMap:{}
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const data = await busiModelGetData({})
        this.optionsData.bmrTarBmId = data.data
        for(let i=0;i<data.data.length;i++){
          this.bmrTarBmIdMap[data.data[i].bmId] = data.data[i].bmTb64
        }
      },
      async bmChange(val){
        this.tableForm.bmrTarBmBt64 = this.bmrTarBmIdMap[val]
        const data = await busiFieldGetList({"bfBmId":val})
        this.optionsData.bmrTarColumn = []
        for(let i=0;i<data.data.length;i++){
          this.optionsData.bmrTarColumn.push({bfColumn:data.data[i].bfColumn,bfName:data.data[i].bfName+'-'+data.data[i].bfColumn})
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>