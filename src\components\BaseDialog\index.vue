<!--公共弹窗组件-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :width="dialogWidth"
    :append-to-body="appendToBody"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :modal="modal"
    :modal-append-to-body="modalAppendToBody"
    :lock-scroll="lockScroll"
    :custom-class="dialogClass"
    :center="center"
    :destroy-on-close="destroyOnClose"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 头部插槽 -->
    <template #title v-if="$slots.title">
      <slot name="title"></slot>
    </template>

    <!-- 主体内容插槽 -->
    <slot></slot>

    <!-- 底部插槽 -->
    <template #footer v-if="$slots.footer || showFooter">
      <slot name="footer">
        <div v-if="showFooter" class="dialog-footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button
            v-if="type === 'edit'"
            type="primary"
            @click="handleConfirm"
            :loading="confirmLoading"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'BaseDialog',
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: ''
    },
    // 弹窗类型：edit(编辑) / view(查看)
    type: {
      type: String,
      default: 'view',
      validator: function (value) {
        return ['edit', 'view'].includes(value)
      }
    },
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗尺寸：Max(90%) / Middle(70%) / Small(50%) 或自定义宽度
    size: {
      type: String,
      default: 'Middle',
      validator: function (value) {
        return ['Max', 'Middle', 'Small'].includes(value) || /^\d+(%|px)$/.test(value)
      }
    },
    // 自定义宽度（优先级高于size）
    width: {
      type: String,
      default: ''
    },
    // 弹窗最大高度
    maxHeight: {
      type: String,
      default: '80vh'
    },
    // 是否插入至 body 元素上
    appendToBody: {
      type: Boolean,
      default: true
    },
    // 是否可以通过点击 modal 关闭 Dialog
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    // 是否可以通过按下 ESC 关闭 Dialog
    closeOnPressEscape: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: true
    },
    // 是否需要遮罩层
    modal: {
      type: Boolean,
      default: true
    },
    // 遮罩层是否插入至 body 元素上
    modalAppendToBody: {
      type: Boolean,
      default: true
    },
    // 是否在 Dialog 出现时将 body 滚动锁定
    lockScroll: {
      type: Boolean,
      default: true
    },
    // Dialog 的自定义类名
    customClass: {
      type: String,
      default: ''
    },
    // 是否对头部和底部采用居中布局
    center: {
      type: Boolean,
      default: false
    },
    // 关闭时销毁 Dialog 中的元素
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    // 是否显示默认底部按钮（如果不传，根据type自动判断）
    showDefaultFooter: {
      type: Boolean,
      default: null
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确定'
    },
    // 确认按钮加载状态
    confirmLoading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 弹窗可见性双向绑定
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    // 计算弹窗宽度
    dialogWidth() {
      // 如果设置了自定义宽度，优先使用
      if (this.width) {
        return this.width
      }

      // 根据size计算宽度
      const sizeMap = {
        'Max': '90%',
        'Middle': '70%',
        'Small': '50%'
      }

      // 如果是预定义尺寸，返回对应宽度
      if (sizeMap[this.size]) {
        return sizeMap[this.size]
      }

      // 如果是自定义尺寸格式（如 "800px" 或 "60%"），直接返回
      return this.size
    },
    // 是否显示底部按钮
    showFooter() {
      // 如果明确设置了showDefaultFooter，使用该值
      if (this.showDefaultFooter !== null) {
        return this.showDefaultFooter
      }
      // 否则根据type自动判断：edit和view类型都显示底部按钮
      return ['edit', 'view'].includes(this.type)
    },
    // 弹窗类名
    dialogClass() {
      let classes = ['base-dialog']
      if (this.customClass) {
        classes.push(this.customClass)
      }
      return classes.join(' ')
    }
  },
  methods: {
    // 弹窗打开前的回调
    handleOpen() {
      this.$emit('open')
    },
    // 弹窗打开后的回调
    handleOpened() {
      this.$emit('opened')
    },
    // 弹窗关闭前的回调
    handleClose() {
      this.$emit('close')
      this.$emit('update:visible', false)
    },
    // 弹窗关闭后的回调
    handleClosed() {
      this.$emit('closed')
    },
    // 取消按钮点击事件
    handleCancel() {
      this.$emit('cancel')
      this.handleClose()
    },
    // 确认按钮点击事件
    handleConfirm() {
      this.$emit('confirm')
      this.$emit('submit') // 添加submit事件，方便表单提交
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>

<style>
.base-dialog .el-dialog__body {
  max-height: v-bind(maxHeight) !important;
  overflow-y: auto;
}

.base-dialog .el-dialog {
  margin-top: 5vh !important;
}
</style>

<style scoped>
:deep(.el-dialog) {
  margin-top: 4vh !important;
}
</style>
