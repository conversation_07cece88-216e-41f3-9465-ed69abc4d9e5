import request from '@/utils/request'

export function lesysConsignGetMeList(params) {
  return request({
    url: '/lesys-consign/vList',
    method: 'get',
    params,
  })
}

//列表分页加载数据
export function lesysConsignGetList(params) {
  return request({
    url: '/lesys-consign/vPage',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysConsignDoSave(data) {
  return request({
    url: '/lesys-consign/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysConsignDoUpdate(data) {
  return request({
    url: '/lesys-consign/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysConsignDoSaveOrUpd(data) {
  return request({
    url: '/lesys-consign/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function lesysConsignDoSaveOrUpdLog(data) {
  return request({
    url: '/lesys-consign/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysConsignDoDelete(data) {
  return request({
    url: '/lesys-consign/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function lesysConsignDoDeleteLog(data) {
  return request({
    url: '/lesys-consign/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function lesysConsignDoDeleteELog(data) {
  return request({
    url: '/lesys-consign/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function lesysConsignGetTreeList(params){
  return request({
    url: '/lesys-consign/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function lesysConsignDoExport(data) {
  return request({
    url: '/lesys-consign/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function lesysConsignGetStat(params) {
  return request({
    url: '/lesys-consign/vStat',
    method: 'get',
    params,
  })
}

// 获取用户委托信息
export function getConsignByLcFromUser(params) {
  return request({
    url: '/lesys-consign/vLesysConsignByLcFromUser',
    method: 'get',
    params,
  })
}

// 收回委托
export function cancelConsign(data) {
  return request({
    url: '/lesys-consign/cancel-consign',
    method: 'post',
    data
  })
}
