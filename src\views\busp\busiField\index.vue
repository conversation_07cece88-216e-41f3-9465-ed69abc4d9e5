<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiFieldSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :busiModelRow="busiModelRow"
      ref="busiFieldTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="busiFieldTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiField"
      row-key="bfId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        align="center"
        type="selection"
        label-class-name="number"
        width="55"/>

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <table-edit ref="busiFieldEdit">
      <busiFieldForm
        ref="busiFieldForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="busiFieldQuerySearch">
      <busiFieldQuery
        ref="busiFieldQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="busiFieldConfigImpEdit" :fullscreen="true">
      <busiFieldConfigImp
        ref="busiFieldConfigImp"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigImpClose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiModelRelationsEdit" popWidth="1200px">
      <busiModelRelations
        ref="busiModelRelations"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiModelRelationsClose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

  </div>
</template>

<script>
  import { busiFieldDoDeleteELog,
           busiFieldGetList,
           busiFieldDoDeletes,
           busiFieldDoSaveOrUpdLog,
           busiFieldDoExport } from '@/api/busp/busiField'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiFieldSearch from './components/Search.vue'
  import busiFieldForm from './components/Form.vue'
  import busiFieldQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  import busiFieldConfigImp from '@/views/busp/busiFieldConfig/index_imp.vue'
  import busiModelRelations from '@/views/busp/busiModelRelations/index.vue'
  export default {
    name: 'busiField',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiFieldSearch,
      busiFieldForm,
      busiFieldQuery,
      busiFieldConfigImp,
      busiModelRelations
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {bfBmId:'',bfBmTable:'',bfColumn:'',bfColumnType:'',bfEffState:'',bfField:'',bfFieldType:'',bfInterface:'',bfIspk:'',bfName:'',bfOrderStart:undefined,bfOrderEnd:undefined,bfOrder:'',bfSort:'',bfTitle:'',bfType:'',bfUseState:''},
        queryForm: {bfBmId:'',bfBmTable:'',bfColumn:'',bfColumnType:'',bfEffState:'',bfField:'',bfFieldType:'',bfInterface:'',bfIspk:'',bfName:'',bfOrderStart:undefined,bfOrderEnd:undefined,bfOrder:'',bfSort:'',bfTitle:'',bfType:'',bfUseState:''},
        form: {bfBmId:'',bfBmTable:'',bfColumn:'',bfColumnType:'',bfEffState:'',bfField:'',bfFieldType:'',bfInterface:'',bfIspk:'',bfName:'',bfOrder:'',bfSort:'',bfTitle:'',bfType:'',bfUseState:''},
        rules: {
          bfBmId: [
            { required: false, message: '请输入模型ID', trigger: 'blur' }
          ],
          bfBmTable: [
            { required: false, message: '请输入表名', trigger: 'blur' }
          ],
          bfColumn: [
            { required: true, message: '请输入字段名', trigger: 'blur' }
          ],
          bfColumnType: [
            { required: true, message: '请输入字段类型', trigger: 'blur' }
          ],
          bfEffState: [
            { required: false, message: '请输入是否有效', trigger: 'blur' }
          ],
          bfField: [
            { required: false, message: '请输入属性名', trigger: 'blur' }
          ],
          bfFieldType: [
            { required: true, message: '请输入属性类型', trigger: 'blur' }
          ],
          bfInterface: [
            { required: true, message: '请输入是否接口列', trigger: 'blur' }
          ],
          bfIspk: [
            { required: true, message: '请输入是否主键', trigger: 'blur' }
          ],
          bfName: [
            { required: true, message: '请输入属性中文', trigger: 'blur' }
          ],
          bfOrder: [
            { required: true, message: '请输入属性顺序', trigger: 'blur' }
          ],
          bfSort: [
            { required: false, message: '请输入属性分类', trigger: 'blur' }
          ],
          bfTitle: [
            { required: false, message: '请输入显示列名', trigger: 'blur' }
          ],
          bfType: [
            { required: false, message: '请输入属性类型', trigger: 'blur' }
          ],
          bfUseState: [
            { required: true, message: '请输入使用状态', trigger: 'blur' }
          ],
          bfLevel: [
            { required: true, message: '请输入属性级别', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '120px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['属性名','属性字段名','属性字段长度','属性字段类型','属性顺序','属性级别','是否属性唯一标识','是否主键','是否创建'],
        columns: [
                { prop:'bfName'     , label:'属性名'      , width:'auto' , sortable:false ,width:'200' },
                { prop:'bfColumn'   , label:'属性字段名'      , width:'auto' , sortable:false  },
                { prop:'bfLength'   , label:'属性字段长度'    , width:'auto' , sortable:false  },
                { prop:'bfColumnType', label:'属性字段类型'    , width:'auto' , sortable:false  },
                { prop:'bfOrder'    , label:'属性顺序'    , width:'auto' , sortable:false  },
                { prop:'bfLevel'    , label:'属性级别'    , width:'auto' , sortable:false  },
                { prop:'bfUnion'     , label:'是否属性唯一标识'   , width:'auto' , sortable:false  } ,
                { prop:'bfIspk'     , label:'是否主键'    , width:'auto' , sortable:false  },
                { prop:'bfIscreate' , label:'是否创建'    , width:'auto' , sortable:false  }     
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfBmId:'',
          bfName:'',
          sortField:'',
          sortOrder:''
        },
        busiModelRow:{},
        multipleSelection:[]
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      //this.fetchData()
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfBmId = row.bmId
        this.fetchData()
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiFieldForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.bfId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const regex = /^[a-zA-Z0-9_]+$/
            if(!this.form.bfId){
              if(!regex.test(this.form.bfColumn)){
                this.$message({message:'输入的模型字段名错误，请输入英文、数字及下划线!',type:'warning'})
                return
              }
            }

            const toc = {"text":"String","date":"Date","number":"Double"}
            this.form.bfFieldType = toc[this.form.bfColumnType]
            this.form.bfcBcId = this.busiModelRow.bmCode+"_DEF"
            const  msg  = await busiFieldDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiFieldEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      // 添加按钮事件
      handleAdd(command) {
        if(command=='add'){
          this.form={bfEffState:'是',bfInterface:'否',bfIspk:'否',bfUseState:'启用',
                   bfLevel:'业务属性',bfBmTable:this.busiModelRow.bmTable,
                   bfBmId:this.busiModelRow.bmId,bfOrder:this.list.length,
                   bfSort:'业务字段',bfFieldType:'String','bfColumnType':'text',
                   bfLength:200,bfIsreal:'是','bfVersion':'0','bfUnion':'否','bfIscreate':'否'}
          this.editType = 'add'
          this.$refs['busiFieldEdit'].showEdit('添加')
        }else if(command=='imp'){
          this.$refs['busiFieldConfigImpEdit'].showEdit('模型导入列配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigImp.init(this.busiModelRow)
          })
        }else if(command == 'rea'){
          if(this.row && this.row.bfId){
            this.$refs['busiModelRelationsEdit'].showEdit('关联模型配置')
            this.$nextTick(()=> {
              this.$refs.busiModelRelations.init(this.busiModelRow,this.row)
            }) 
          }    
        }else if(command == 'del'){
          if(this.multipleSelection.length==0){
            this.$message({message:'勾选删除的模型属性!',type:'warning'})
          }else{
            let rss = []
            for(let i=0;i<this.multipleSelection.length;i++){
              rss.push({bfId:this.multipleSelection[i].bfId})
            }
            this.$baseConfirm('确定删除属性吗', null, async () => {
              const msg = await busiFieldDoDeletes({busiFieldList:rss})
              if(msg.code == 200) {
                this.$message({message:'删除操作成功!',type:'success'})
                await this.fetchData()
              }else{
                this.$message({message:'删除操作失败!',type:'warning'})
              }
            })
          }
        } 
      },
      busiModelRelationsClose(){
        this.$refs.busiModelRelationsEdit.close()
      },
      busiFieldConfigImpClose(){
        this.$refs.busiFieldConfigImpEdit.close()
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        if(row.bfIsquote=='是'){
          this.$message({message:'引用列不可编辑!',type:'warning'})
          return
        }
        this.row = row 
        this.editType = 'update'
        this.$refs['busiFieldEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if(row.bfIscreate=='是'){
          this.$message({message:'属性已创建，无法删除!',type:'warning'})
          return
        }
        this.$baseConfirm('确定删除吗', null, async () => {
          row.logData = JSON.stringify(row) 
          row.logDesc = '删除模型属性表数据' 
          const msg = await busiFieldDoDeleteELog(row)
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'删除操作失败!',type:'warning'})
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['busiFieldQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.busiFieldQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.busiFieldQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":this.busiModelRow.bmName+".xls",
                      "isnumber":true,
                      "excelTitle":this.busiModelRow.bmName,
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#BusiField",params)
        const { msg }  =  await busiFieldDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"模型属性模板.xls",
                      "excelIstmpl":true,
                      "excelTmplDataCcs":"bfEffState#bfInterface#bfIspk#bfIsreal,bfColumnType,bfLevel,bfUseState,bfSort,bfFieldType",
                      "excelTmplDataDds":"是#否,text#date#number,业务属性#管理属性,未启用#启用#停用,业务字段#编码字段#增量字段,String#Date#Double"}
        let qf = exportRearEnd("#BusiField",params)
        
        qf.excelExps[0].push({"field":"bfBmTable","title":"表名","rowspan":"1","colspan":"1","rowspand":"1","celltype":"text","cellwidth":"18","dateFormat":"yyyy-MM-dd HH:mm:ss","celliswrap":"0","cellwraplength":"0"})              
        const { msg }  =  await busiFieldDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>