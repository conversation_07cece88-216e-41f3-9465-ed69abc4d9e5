<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <leswfe15IdeaHistorySearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="leswfe15IdeaHistoryTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="leswfe15IdeaHistoryTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="Leswfe15IdeaHistory"
      row-key="ideaId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="leswfe15IdeaHistoryEdit">
      <leswfe15IdeaHistoryForm
        ref="leswfe15IdeaHistoryForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="leswfe15IdeaHistoryQuerySearch">
      <leswfe15IdeaHistoryQuery
        ref="leswfe15IdeaHistoryQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { leswfe15IdeaHistoryDoDeleteELog,
           leswfe15IdeaHistoryGetList,
           leswfe15IdeaHistoryDoSaveOrUpdLog,
           leswfe15IdeaHistoryDoExport } from '@/api/workflow/leswfe15IdeaHistory'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import leswfe15IdeaHistorySearch from './components/Search.vue'
  import leswfe15IdeaHistoryForm from './components/Form.vue'
  import leswfe15IdeaHistoryQuery from './components/Query.vue'
  import { exportRearEnd,exportRearEndDatas,exportGetPageData } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'leswfe15IdeaHistory',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      leswfe15IdeaHistorySearch,
      leswfe15IdeaHistoryForm,
      leswfe15IdeaHistoryQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {ideaText:''},
        queryForm: {ideaText:''},
        form: {ideaTime:'',ideaUser:'',ideaText:''},
        rules: {
          ideaTime: [
            { required: false, message: '请输入添加/使用时间', trigger: 'blur' }
          ],
          ideaUser: [
            { required: false, message: '请输入所属用户', trigger: 'blur' }
          ],
          ideaText: [
            { required: true, message: '请输入审批词', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['审批词','添加/使用时间'],
        columns: [
                { prop:'ideaText', label:'审批词', width:'auto' , sortable:true  },
                { prop:'ideaTime', label:'添加/使用时间', width:'auto' , sortable:true  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          ideaUser:'',
          ideaText:'',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.searchForm.ideaUser = this.loginUser.luId
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.leswfe15IdeaHistoryForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.ideaId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await leswfe15IdeaHistoryDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        });
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.leswfe15IdeaHistoryEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={'ideaUser':this.loginUser.luId};
        this.editType = 'add'
        this.$refs['leswfe15IdeaHistoryEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['leswfe15IdeaHistoryEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.ideaId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            row.logData = JSON.stringify(row) 
            row.logDesc = '删除工作流--办理意见审批词表数据' 
            const msg = await leswfe15IdeaHistoryDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['leswfe15IdeaHistoryQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.leswfe15IdeaHistoryQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.leswfe15IdeaHistoryQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await leswfe15IdeaHistoryGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(command){
        if(command == 'page'){
          let params = {"dataFields":{"ideaTime":{"celltype":"date","dateFormat":"yyyy-MM-dd"}},
                      "fileName":"工作流--办理意见审批词表.xls",
                      "isnumber":true,
                      "excelTitle":"工作流--办理意见审批词表",
                      "queryForm":this.searchForm||{}}
          let qf = exportRearEnd("#Leswfe15IdeaHistory",params)
          let qd = exportRearEndDatas("#Leswfe15IdeaHistory",qf);
          qf.expData = exportGetPageData(qf,qd)
          const { msg }  =  await leswfe15IdeaHistoryDoExport(qf)
          window.open(baseURL+"/"+msg)
          
        }else{
          let params = {"dataFields":{"ideaTime":{"celltype":"date","dateFormat":"yyyy-MM-dd"}},
                      "fileName":"工作流--办理意见审批词表.xls",
                      "isnumber":true,
                      "excelTitle":"工作流--办理意见审批词表",
                      "queryForm":this.searchForm||{}}
          let qf = exportRearEnd("#Leswfe15IdeaHistory",params)
          const { msg }  =  await leswfe15IdeaHistoryDoExport(qf)
          window.open(baseURL+"/"+msg)
        } 
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"工作流--办理意见审批词表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#Leswfe15IdeaHistory",params)
        const { msg }  =  await leswfe15IdeaHistoryDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>