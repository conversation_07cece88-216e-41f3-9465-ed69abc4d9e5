import request from '@/utils/request'

function busiFieldConfigGetList(params) {
  return request({
    url: '/busi-field-config/getFieldConfigList',
    method: 'get',
    params,
  })
}

export function busiFieldConfigs(tableName, sortField) {
  return busiFieldConfigGetList({
    bfBmTb64: tableName,
    bfEffState: '是',
    bfUseState: '启用',
    sortField: sortField ? sortField : 'BFC_LORDER',
    sortOrder: 'ASC',
  })
}

export function getCheckListColumnsForms(data, param) {
  param = param||{}
  if(param.formCols==undefined){
    param.formCols = -1
  }
  if(param.queryFormCols==undefined){
    param.queryFormCols = -1
  }
  let checkList = []
  let columns = []
  if(param && param.checkList){
    checkList = param.checkList
  }
  if(param && param.columns){
    columns = param.columns
  }
  let formList = []
  let formList2 = []
  let rules = {}
  let formOptionData = {}
  let excelTmplListIds = []
  let excelTmplListCcs = []
  let excelTmplDataDds = []
  let excelTmplDataCcs = []

  let queryList = []
  let queryList2= []
  let queryOptionData = {}
  let queryQs = []
  let formMulList = {}
  let formNums = 0
  let formPk = {}

  if (data.code == 200) {
    for (let i = 0; i < data.data.length; i++) {
      if(data.data[i].bfIspk == '是'){
        formPk = data.data[i]
      }

      if (data.data[i].bfcIslist == '是') {
        if(!(param && param.hasBfcIsQuOte=='0' && data.data[i].bfcIsquote=='是')){
          checkList.push(data.data[i].bfName)
          columns.push({
            prop: param[data.data[i].bfField] || data.data[i].bfField,
            label: data.data[i].bfName,
            field: param[data.data[i].bfField] || data.data[i].bfField,
            filter:data.data[i].bfcIsfilter||'否',
            //width:
            //  data.data[i].bfcListwidth &&
            //  data.data[i].bfcListwidth != '' &&
            //  data.data[i].bfcListwidth != '0'
            //    ? data.data[i].bfcListwidth
            //    : 'auto',
            sortable: false,
          })
        }
        
      }
      if (data.data[i].bfcIsedit == '是') {
        if(data.data[i].bfcEdittype!='HIDDEN'){
          formNums = formNums + 1
        }
        formList.push(data.data[i])
        rules[data.data[i].bfField] = [
          {
            required: data.data[i].bfcRequire == '是' ? true : false,
            message: '请输入' + data.data[i].bfName,
            trigger: 'blur',
          },
        ]
        if (
          data.data[i].bfcEdittype == 'TEXT-SELECT' ||
          data.data[i].bfcEdittype == 'TEXT-SELECTS' ||
          data.data[i].bfcEdittype == 'TEXT-RADIO' ||
          data.data[i].bfcEdittype == 'TEXT-CHECKBOX'
        ) {

          if(data.data[i].bfcEdittype == 'TEXT-SELECTS' || data.data[i].bfcEdittype == 'TEXT-CHECKBOX'){
            formMulList[data.data[i].bfField] = 1
          }

          formOptionData[data.data[i].bfField] = []

          if (data.data[i].bfcDictionary) {
            let sd = JSON.parse(data.data[i].bfcDictionary)
            if (sd.type == 'url') {
              excelTmplListCcs.push(data.data[i].bfField)
              excelTmplListIds.push(sd.data)
            } else {
              excelTmplDataDds.push(data.data[i].bfField)
              let t = []
              for (let d in sd.data) {
                t.push(sd.data[d].value)
              }
              excelTmplDataCcs.push(t.join('#'))
            }
          }
        }
      }
      if (
        data.data[i].bfcIsquery == '是' ||
        data.data[i].bfcIsquery == '快速查询列'
      ) {
        queryList.push(data.data[i])
        if (
          data.data[i].bfcEdittype == 'TEXT-SELECT' ||
          data.data[i].bfcEdittype == 'TEXT-SELECTS' ||
          data.data[i].bfcEdittype == 'TEXT-RADIO' ||
          data.data[i].bfcEdittype == 'TEXT-CHECKBOX'
        ) {
          queryOptionData[data.data[i].bfField] = []
        }
        if (data.data[i].bfcIsquery == '快速查询列') {
          queryQs.push(data.data[i])
        }
      }
    }
  }
  formList.sort((a, b) => a.bfcOrder - b.bfcOrder);
  queryList.sort((a, b) => a.bfcQorder - b.bfcQorder);
  if(param.formCols==-1){
    param.formCols = (formNums<=10 ? 1 : (formNums<=30 ? 2 : 3) )
  }
  let ft =[]
  let indx = 0
  for(let i=0;i<formList.length;i++){
    ft.push(formList[i])
    indx = indx + 1
    if(indx%param.formCols==0){
      formList2.push(ft)
      indx = 0
      ft = []
    }
  }
  if(param.queryFormCols==-1){
    param.queryFormCols = (queryList.length<=10 ? 1 : (queryList.length<=30 ? 2 : 3) )
  }
  let ft1 =[]
  let indx1 = 0
  for(let i=0;i<queryList.length;i++){
    ft1.push(queryList[i])
    indx1 = indx1 + 1
    if(indx1%param.queryFormCols==0){
      queryList2.push(ft1)
      indx1 = 0
      ft1 = []
    }
  }

  return {
    checkList: checkList,
    columns: columns,
    formList: formList,
    formList2: formList2,
    rules: rules,
    formOptionData: formOptionData,
    excelTmplListIds: excelTmplListIds,
    excelTmplListCcs: excelTmplListCcs,
    excelTmplDataDds: excelTmplDataDds,
    excelTmplDataCcs: excelTmplDataCcs,
    queryList: queryList,
    queryList2: queryList2,
    queryOptionData: queryOptionData,
    queryQs: queryQs,
    formMulList:formMulList,
    formCols:param.formCols,
    queryFormCols:param.queryFormCols,
    formPk:formPk
  }
}

export function getCheckListColumns(data, param) {
  let checkList = []
  let columns = []
  if (data.code == 200) {
    for (let i = 0; i < data.data.length; i++) {
      if (data.data[i].bfcIslist == '是') {
        checkList.push(data.data[i].bfName)
        columns.push({
          prop: param[data.data[i].bfField] || data.data[i].bfField,
          label: data.data[i].bfName,
          width: 'auto',
          sortable: false,
        })
      }
    }
  }
  return { checkList: checkList, columns: columns }
}

export async function getOptions(formList, param) {
  let optionsData = {}
  for (let i = 0; i < formList.length; i++) {
    let sss = formList[i].bfcEdittype
    if (
      sss == 'TEXT-SELECT' ||
      sss == 'TEXT-SELECTS' ||
      sss == 'TEXT-RADIO' ||
      sss == 'TEXT-CHECKBOX'
    ) {
      if (
        formList[i].bfcDictionary != null &&
        formList[i].bfcDictionary != ''
      ) {
        let sd = JSON.parse(formList[i].bfcDictionary)
        if (sd.type == 'url') {
          let params = { lpvLpdId: sd.data }
          const data = await request({
            //url: '/lesys-sysparamvals/list',
            url: '/lesys-paramvals-vue/vListRedis',
            method: 'get',
            params,
          })
          if (data.code == 200) {
            optionsData[formList[i].bfField] = getOptionsData(
              data.data,
              param[formList[i].bfField] || 'lpvId',
              'lpvName'
            )
          }
        } else {
          optionsData[formList[i].bfField] = getOptionsData(
            sd.data,
            'value',
            'label'
          )
        }
      }
    }
  }
  return optionsData
}

export async function getOptionsLpvName(formList, param) {
  let optionsData = {}
  for (let i = 0; i < formList.length; i++) {
    if(param && param.bfLevel && param.bfLevel!='自定义'){
      continue
    }
    let sss = formList[i].bfcEdittype
    if (
      sss == 'TEXT-SELECT' ||
      sss == 'TEXT-SELECTS' ||
      sss == 'TEXT-RADIO' ||
      sss == 'TEXT-CHECKBOX'
    ) {
      if (
        formList[i].bfcDictionary != null &&
        formList[i].bfcDictionary != ''
      ) {
        let sd = JSON.parse(formList[i].bfcDictionary)
        if (sd.type == 'url') {
          let params = { lpvLpdId: sd.data }
          const data = await request({
            //url: '/lesys-sysparamvals/list',
            url: '/lesys-paramvals-vue/vListRedis',
            method: 'get',
            params,
          })
          if (data.code == 200) {
            optionsData[formList[i].bfField] = getOptionsData(
              data.data,
              'lpvName',
              'lpvName',
              param
            )
          }
        } else {
          optionsData[formList[i].bfField] = getOptionsData(
            sd.data,
            'value',
            'label',
            param
          )
        }
      }
    }
  }
  return optionsData
}

export async function getOptionsLpvName2(formList, param) {
  let optionsData = {}
  let lpdidsMap = {}
  let lpdids = []
  let optionMap = {}
  for (let i = 0; i < formList.length; i++) {
    for (let j=0; j < formList[i].length; j++) {
      if(param && param.bfLevel && param.bfLevel!='自定义'){
        continue
      }
      let sss = formList[i][j].bfcEdittype
      if (
        sss == 'TEXT-SELECT' ||
        sss == 'TEXT-SELECTS' ||
        sss == 'TEXT-RADIO' ||
        sss == 'TEXT-CHECKBOX'
      ) {
        if (
          formList[i][j].bfcDictionary != null &&
          formList[i][j].bfcDictionary != ''
        ) {
          let sd = JSON.parse(formList[i][j].bfcDictionary)
          if (sd.type == 'url') {
            if(!lpdidsMap[sd.data]){
              lpdidsMap[sd.data] = 1
              lpdids.push(sd.data)
            }
            optionMap[formList[i][j].bfField] = sd.data
          } else {
            optionsData[formList[i][j].bfField] = getOptionsData(
              sd.data,
              'value',
              'label',
              param
            )
          }
        }
      }
    }
  }

  if(lpdids.length>0){
    let params = { lpvLpdId: lpdids.join(",") }
    const data = await request({
      //url: '/lesys-sysparamvals/list',
      //url: '/lesys-paramvals-vue/vListRedis',
      url:'/lesys-paramvals-vue/vJsonRedis',
      method: 'get',
      params,
    })
    if (data.code == 200) {
      for(let key in optionMap){
        if(data.data[optionMap[key]]){
          optionsData[key] = getOptionsData(
            data.data[optionMap[key]],
            'lpvName',
            'lpvName',
            {}
          )
        }
      }
    }
  }

  return optionsData
}

export async function getOptionsLpvName3(formList, param) {
  let optionsData = {}
  let lpdidsMap = {}
  let lpdids = []
  let optionMap = {}
  for (let i = 0; i < formList.length; i++) {
    for (let j=0; j < formList[i].length; j++) {

      let sss = formList[i][j].bfcIsfilter
      if (
        sss == '是'
      ) {
        if (
          formList[i][j].bfcDictionary != null &&
          formList[i][j].bfcDictionary != ''
        ) {
          let sd = JSON.parse(formList[i][j].bfcDictionary)
          if (sd.type == 'url') {
            if(!lpdidsMap[sd.data]){
              lpdidsMap[sd.data] = 1
              lpdids.push(sd.data)
            }
            optionMap[formList[i][j].bfField] = sd.data
          } else {
            optionsData[formList[i][j].bfField] = getOptionsData(
              sd.data,
              'value',
              'label',
              param
            )
          }
        }
      }
    }
  }

  if(lpdids.length>0){
    let params = { lpvLpdId: lpdids.join(",") }
    const data = await request({
      url:'/lesys-paramvals-vue/vJsonRedis',
      method: 'get',
      params,
    })
    if (data.code == 200) {
      for(let key in optionMap){
        if(data.data[optionMap[key]]){
          optionsData[key] = getOptionsData(
            data.data[optionMap[key]],
            'lpvName',
            'lpvName',
            {}
          )
        }
      }
    }
  }

  return optionsData
}

function getOptionsData(data, valfield, labfield, param) {
  let options = []
  if (param && param.defaultOption) {
    options.push(param.defaultOption)
  }
  if (data.length > 0) {
    for (let d in data) {
      options.push({ value: data[d][valfield], label: data[d][labfield] })
    }
  }
  return options
}

export async function getDimen(data){
  let dimen = []
  if (data.code == 200) {
    for (let i = 0; i < data.data.length; i++) {
      if (data.data[i].bfSort == '维度') {
        dimen.push({name:data.data[i].bfName,value:data.data[i].bfColumn,field:data.data[i].bfField})
      }
    }
  }
  return dimen
}

export function getQueryExpre(searchList,queryForm) {
  let equery = []
  for(let i=0;i<searchList.length;i++){
    let qsf = searchList[i][0]
    if(queryForm[qsf.bfField] && queryForm[qsf.bfField]!=''){
      equery.push({field:qsf.bfField,value:queryForm[qsf.bfField],expre:qsf.bfcExpre})
    }
    else{
      if(queryForm[qsf.bfField+'Start'] && queryForm[qsf.bfField+'Start']!=''){
        equery.push({field:qsf.bfField+'Start',value:queryForm[qsf.bfField+'Start'],expre:'>='})
      }
      if(queryForm[qsf.bfField+'End'] && queryForm[qsf.bfField+'End']!=''){
        equery.push({field:qsf.bfField+'End',value:queryForm[qsf.bfField+'End'],expre:'<='})
      }
    }
  }
  return equery
}
