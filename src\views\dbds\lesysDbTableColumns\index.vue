<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysDbTableColumnsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysDbTableColumnsTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysDbTableColumnsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="lesysDbTableColumns"
      row-key="ldtcId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <table-edit ref="lesysDbTableColumnsEdit">
      <lesysDbTableColumnsForm
        ref="lesysDbTableColumnsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysDbTableColumnsQuerySearch">
      <lesysDbTableColumnsQuery
        ref="lesysDbTableColumnsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { lesysDbTableColumnsDoDelete,
           lesysDbColumnsGetList,
           lesysDbTableColumnsDoSaveOrUpd,
           lesysDbTableColumnsDoExport } from '@/api/dbds/lesysDbTableColumns'
  import { lesysDbDsGetList} from '@/api/dbds/lesysDbDs'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysDbTableColumnsSearch from './components/Search.vue'
  import lesysDbTableColumnsForm from './components/Form.vue'
  import lesysDbTableColumnsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'lesysDbTableColumns',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysDbTableColumnsSearch,
      lesysDbTableColumnsForm,
      lesysDbTableColumnsQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {ldtcLdtId:'',ldtcLdtTableName:'',ldtcColumn:'',ldtcComment:'',ldtcType:'',ldtcLengthStart:undefined,ldtcLengthEnd:undefined,ldtcLength:'',ldtcDataScaleStart:undefined,ldtcDataScaleEnd:undefined,ldtcDataScale:'',ldtcDataPrecisionStart:undefined,ldtcDataPrecisionEnd:undefined,ldtcDataPrecision:'',ldtcIsprimary:''},
        queryForm: {ldtcLdtId:'',ldtcLdtTableName:'',ldtcColumn:'',ldtcComment:'',ldtcType:'',ldtcLengthStart:undefined,ldtcLengthEnd:undefined,ldtcLength:'',ldtcDataScaleStart:undefined,ldtcDataScaleEnd:undefined,ldtcDataScale:'',ldtcDataPrecisionStart:undefined,ldtcDataPrecisionEnd:undefined,ldtcDataPrecision:'',ldtcIsprimary:''},
        form: {ldtcLdtId:'',ldtcLdtTableName:'',ldtcColumn:'',ldtcComment:'',ldtcType:'',ldtcLength:'',ldtcDataScale:'',ldtcDataPrecision:'',ldtcIsprimary:''},
        rules: {
          ldtcLdtId: [
            { required: true, message: '请输入表id', trigger: 'blur' }
          ],
          ldtcLdtTableName: [
            { required: true, message: '请输入表名称', trigger: 'blur' }
          ],
          ldtcColumn: [
            { required: true, message: '请输入列名称', trigger: 'blur' }
          ],
          ldtcComment: [
            { required: true, message: '请输入列描述', trigger: 'blur' }
          ],
          ldtcType: [
            { required: true, message: '请输入列类型', trigger: 'blur' }
          ],
          ldtcLength: [
            { required: true, message: '请输入列长度', trigger: 'blur' }
          ],
          ldtcDataScale: [
            { required: true, message: '请输入数值小数', trigger: 'blur' }
          ],
          ldtcDataPrecision: [
            { required: true, message: '请输入数值长度', trigger: 'blur' }
          ],
          ldtcIsprimary: [
            { required: true, message: '请输入是否主键', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1):this.gheight,
        checkList: ['列名称','列描述','列类型','列长度','是否主键','是否同步'],
        columns: [
                { prop:'ldtcColumn'       , label:'列名称', width:'auto' , sortable:false  },
                { prop:'ldtcComment'      , label:'列描述', width:'auto' , sortable:false  },
                { prop:'ldtcType'         , label:'列类型', width:'auto' , sortable:false  },
                { prop:'ldtcLength'       , label:'列长度', width:'auto' , sortable:false  },
                { prop:'ldtcIsprimary'    , label:'是否主键', width:'auto' , sortable:false  },
                { prop:'hasColumn'        , label:'是否同步', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          sortField:'',
          sortOrder:''
        },
        lesysDbDsRow:{},
        lesysDbTableRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      }
    },
    created() {
      //this.fetchData()
    },
    methods: {
      loadSchema(ldr,ltt){
        this.lesysDbDsRow = ldr
        this.lesysDbTableRow = ltt
        if(ltt!=null){
          this.searchForm.ldtcLdtId = ltt.ldtId
          this.searchForm.ldId      = ldr.ldId
          this.searchForm.ldtcLdtTableName = ltt.ldtTableName
          this.fetchData()
        }else{
          this.list = []
          this.listLoading = false
        }
      },

      count(index) {
        return index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.lesysDbTableColumnsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await lesysDbTableColumnsDoSaveOrUpd( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.lesysDbTableColumnsEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.$refs['lesysDbTableColumnsEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['lesysDbTableColumnsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗', null, async () => {
          const msg = await lesysDbTableColumnsDoDelete({id:row.ldtcId})
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'删除操作失败!',type:'warning'})
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysDbTableColumnsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysDbTableColumnsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysDbTableColumnsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await lesysDbDsGetList(this.searchForm)
        this.list = data.data
        this.total = data.data.length
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"数据源表字段表.xls",
                      "isnumber":true,
                      "excelTitle":"数据源表字段表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#lesysDbTableColumns",params)
        const { msg }  =  await lesysDbTableColumnsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"数据源表字段表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#lesysDbTableColumns",params)
        const { msg }  =  await lesysDbTableColumnsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>