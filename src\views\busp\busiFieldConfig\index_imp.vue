<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  > 
    <div class="search-container">
      <el-button icon="el-icon-save" type="success" @click="handleAdd5">
        保存配置
      </el-button>
    </div>
    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bficId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >
      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in cols"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>
  </div>
</template>

<script>
  import { busiFieldImpConfigGetList,busiFieldImpConfigDoSave } from '@/api/busp/busiFieldImpConfig'
  export default {
    name: 'busiFieldConfigImp',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['属性名称','属性列名','Excel列号'],
        columns: [
                { prop:'bfName'     , label:'属性名称' , width:'auto' , sortable:false},
                { prop:'bfColumn'   , label:'属性列名' , width:'auto' , sortable:false},
                { prop:'bficCol'    , label:'Excel列号', width:'auto' , sortable:false  ,editType:'select'}      
        ],
        list: [],
        imageList: [],
        listLoading: true,
        
        total: 0,
        row: '',
        searchForm: {
          bficBmId:''
        },
        busiModelRow:{},
        cols:[],
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getCols()
    },
    methods: {
      getCols(){
        let colb = "ABCDEFGHIJKLMNOPQRSTUVWSYZ"
        let cols = []
        for(let i=0;i<5;i++){
          let xfs = i-1<0?'':colb.charAt(i-1)
          for(let j=0;j<colb.length;j++){
            cols.push({value:xfs+colb.charAt(j),label:xfs+colb.charAt(j)})
          }
        }
        this.cols = cols
      },
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bficBmId = row.bmId
        this.fetchData()
      },

      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldImpConfigGetList(this.searchForm)
        this.list = data.data
        for(let i=0;i<this.list.length;i++){
          this.list[i].bficCol = this.cols[i].value
        }
        this.listLoading = false
      },
      
      async handleAdd5(){
        let lir = []
        for(let i=0;i<this.list.length;i++){
          lir.push({bficBfId:this.list[i].bfId,bficBmId:this.busiModelRow.bmId,bficBmTb64:this.busiModelRow.bmTb64,bficCol:this.list[i].bficCol})
        }

        const msg = await busiFieldImpConfigDoSave({busiFieldImpConfigList:lir})
        if(msg.code == 200) {
          this.$message({message:'操作成功!',type:'success'})
          await this.fetchData()
        }else{
          this.$message({message:'操作失败!',type:'warning'})
        }
      }
    }
  }
</script>