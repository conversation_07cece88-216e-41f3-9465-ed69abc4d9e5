<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input placeholder="流程实例名称" v-model="tableQueryForm.instName"  @keyup.enter.native="handleSearch" />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              高级查询
            </el-button>
            <el-button icon="el-icon-view" type="primary" @click="handleViewFlow" title="查看流程">
              查看流程
            </el-button>
            <el-button icon="el-icon-plus" type="primary" @click="handleDealFlow">
              取回流程
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button icon="el-icon-download" type="primary" @click="handleExportRear" title="导出表格">
        </el-button>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
          title="表格全屏"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          
        </el-button>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="taleCheckList" @change="handleCheckedChange">
            <vab-draggable v-bind="dragOptions" :list="tableColums">
              <div v-for="(item, index) in tableColums" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
              title="可拖拽列设置"
            >
              
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'
  import axios from 'axios'
  import config from '@/config'
  import store from '@/store'

  export default {
    name: 'viewLeswfe15TaskInstSearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal;
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('viewLeswfe15TaskInstHandleSearch',this.tableQueryForm);
      },
      handleQuery() {
        this.$emit('viewLeswfe15TaskInstHandleQuery');
      },
      clickFullScreen() {
        this.isFullscreen = !this.isFullscreen;
        this.$emit("viewLeswfe15TaskInstHandleHeight",this.isFullscreen);
      },
      handleCheckedChange(val) {
        this.$emit("viewLeswfe15TaskInstHandleCheckedChange",val);
      },
      // 导出
      handleExportRear() {
        this.$emit("viewLeswfe15TaskInstHandleExportRear")
      },
      handleViewFlow(){
        this.$emit("handleViewFlow");
      },
      handleDealFlow(){
        this.$emit("handleDealFlow");
      }
    },
  }
</script>