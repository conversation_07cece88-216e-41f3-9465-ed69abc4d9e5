<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >

    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item>
            <el-button
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd('add')"
            >
              添加
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="busiModelRelationsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="500"
      stripe
      @cell-dblclick="cellDblClick"
      id="busiModelRelations"
      row-key="bmrId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="80"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

    <table-edit ref="busiModelReaFormEdit">
      <busiModelReaForm
        ref="busiModelReaForm"
        slot="form"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { busiModelRelationsGetData,busiModelRelationsDoDelete,busiModelRelationsDoSave } from '@/api/busp/busiModelRelations'
  import TableEdit from '@/views/common/TableEdit.vue'
  import busiModelReaForm from './Form.vue'
  export default {
    name: 'busiModelRelations',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      busiModelReaForm
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['模型名称','模型列名','模型列'],
        columns: [
                { prop:'bmName'       , label:'模型名称'  , width:'auto' , sortable:false  },
                { prop:'bfName'       , label:'模型列名'  , width:'auto' , sortable:false  },
                { prop:'bmrTarColumn' , label:'模型列'    , width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        row: '',
        searchForm: {
          sortField:'',
          sortOrder:''
        },
        busiModelRow:{},
        busiFieldRow:{}
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      init(row,row1){
        this.busiModelRow = row
        this.busiFieldRow = row1
        this.searchForm.bmrSorBmId = row.bmId
        this.searchForm.bmrSorColumn = row1.bfColumn
        this.fetchData()
      },
      close(){
        this.$refs.busiModelReaFormEdit.close()
      },
      save(){
        this.$refs.busiModelReaForm.$refs.form.validate(async (valid) => {
          if (valid) {
            if(this.form.bmrSorBmId == this.form.bmrTarBmId){
              this.$message({message:'不能选择相同模型!',type:'warning'})
              return
            }
            const  msg  = await busiModelRelationsDoSave( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:msg.msg||'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      count(index) {
        return index + 1
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd(command) {
        this.form={'bmrSorBmId':this.busiModelRow.bmId,'bmrSorBmBt64':this.busiModelRow.bmTb64,'bmrSorColumn':this.busiFieldRow.bfColumn}
        this.editType = 'add'
        this.$refs['busiModelReaFormEdit'].showEdit('添加模型')
      },
      // 双击行编辑事件
      cellDblClick(row) {
      },
      // 编辑行数据
      handleEdit(row) {
      },
      // 删除行数据
      handleDelete(row) {
        if (row.bmrId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await busiModelRelationsDoDelete(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiModelRelationsGetData(this.searchForm)
        this.list = data.data
        this.listLoading = false
      }
    },
  }
</script>