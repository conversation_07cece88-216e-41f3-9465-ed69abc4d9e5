<template>
  <div class="vab-drop-down-tab">
    <el-menu
      class="el-menu-demo"
      :default-active="activeIndex"
      mode="horizontal"
    >
      <el-submenu
        v-for="item in route"
        v-if="!item.meta.hidden"
        :key="item.name"
        :index="item.name"
      >
        <template slot="title" :id="'tab_test' + item.name">
          {{ item.meta.title }}
        </template>
        <div class="menu-dropdown-inner" :style="{ width: bannerWidth }">
          <div class="navBar_left">
            <p class="describeTitlt">专业文档、工具和社区 让云上开发更便捷</p>
            <p class="describeCont">
              提供官方的技术文档、丰富的开发工具和资源；支持开发者技术交流和能力成长，与千万开发者共同探索云上创新。
            </p>
          </div>
          <div class="navBar_right">
            <ul
              v-for="(that, index) in item.children"
              v-if="item.children && item.children.length"
              class="navBar_right_item"
            >
              <li v-if="that.children.length > 0">
                <div class="second_menu">{{ that.meta.title }}</div>
                <p
                  v-for="children in that.children"
                  class="third_menu_item"
                  :class="{ isActive: children.path === currentPath }"
                  @click="handleLink(children.path)"
                >
                  {{ children.meta.title }}
                </p>
              </li>
              <li v-else>
                <div
                  class="second_menu isChildren"
                  :index="that.path"
                  @click="handleLink(that.path)"
                >
                  {{ that.meta.title }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </el-submenu>
    </el-menu>
  </div>
</template>

<script>
  export default {
    name: 'VabDropDownTab',
    props: {
      route: {
        type: Array,
        default: () => {
          return []
        },
      },
    },
    data() {
      return {
        activeIndex: '1',
        bannerWidth: '',
        currentPath: '',
      }
    },
    created() {
      this.bannerWidth = window.innerWidth + 'px'
      this.handleFindChildPath(
        this.route.filter((item) => {
          return !item.meta.hidden
        })[0]
      )
    },
    methods: {
      handleLink(routePath) {
        this.currentPath = routePath

        this.$router.push(routePath)
      },
      handleFindChildPath(arr) {
        this.getLastChild(arr)
      },
      getLastChild(node) {
        if (node.children !== null) {
          this.getLastChild(node.children[0])
        } else {
          this.path = node.path
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  ul li {
    list-style: none;
  }
  .menu-dropdown-inner {
    min-height: 300px;
    padding: 32px;
    display: flex;
    .navBar_left {
      width: 372px;
      border-right: 1px solid #ebedf0;

      .describeTitlt {
        width: 240px;
        font-size: 20px;
        font-weight: 500;
        color: #303133;
        margin: 0px;
      }
      .describeCont {
        width: 280px;
        font-size: 14px;
        font-weight: 400;
        color: #909399;
        margin-top: 24px;
      }
    }

    .navBar_right {
      width: calc(100% - 372px);
      display: flex;
      flex-wrap: wrap;
      .navBar_right_item {
        width: 370px;
        flex-shrink: 0;
      }
      .second_menu {
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
        margin-bottom: 24px;
      }

      .isChildren {
        cursor: pointer;
      }
      .third_menu_item {
        font-size: 14px;
        font-weight: 400;
        color: #303133;
        line-height: 20px;
        margin-top: 16px;
        cursor: pointer;
      }
    }
  }
</style>
