<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <div class="search-container">
      <vab-query-form>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
        >
          <el-form-item>
            <el-select v-model.trim="ldId" style="width:100%" @change="changeType">
              <el-option v-for="item in optionsData.ldIdData" :key="item.ldId" :label="item.ldName" :value="item.ldId"> </el-option> 
            </el-select>
          </el-form-item>
        </el-form>
      </vab-query-form>
    </div>

    <el-table
      ref="lesysDbTablesTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="lesysDbTables"
      row-key="ldtId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <!--el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button-->

          <!--el-button type="text" @click="handleEdit(row)">注册</el-button-->
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="lesysDbTablesEdit">
      <lesysDbTablesForm
        ref="lesysDbTablesForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysDbTablesQuerySearch">
      <lesysDbTablesQuery
        ref="lesysDbTablesQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { lesysDbTablesDoDelete,
           lesysDbTablesGetPage,
           lesysDbTablesDoSaveOrUpd,
           lesysDbTablesDoExport } from '@/api/dbds/lesysDbTables'
  
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysDbTablesSearch from './components/Search.vue'
  import lesysDbTablesForm from './components/Form.vue'
  import lesysDbTablesQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  import { lesysDbDsGetPage } from '@/api/dbds/lesysDbDs'

  export default {
    name: 'lesysDbTables',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysDbTablesSearch,
      lesysDbTablesForm,
      lesysDbTablesQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {ldtLdId:'',ldtTableName:'',ldtTableComment:'',ldtType:''},
        queryForm: {ldtLdId:'',ldtTableName:'',ldtTableComment:'',ldtType:''},
        form: {ldtLdId:'',ldtTableName:'',ldtTableComment:'',ldtType:''},
        rules: {
          ldtLdId: [
            { required: true, message: '请输入数据源ID', trigger: 'blur' }
          ],
          ldtTableName: [
            { required: true, message: '请输入表名', trigger: 'blur' }
          ],
          ldtTableComment: [
            { required: true, message: '请输入表描述', trigger: 'blur' }
          ],
          ldtType: [
            { required: true, message: '请输入类型', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['表名','表描述','类型'],
        columns: [
                { prop:'ldtTableName', label:'表名', width:'auto' , sortable:false  },
                { prop:'ldtTableComment', label:'表描述', width:'auto' , sortable:false  },
                { prop:'ldtType', label:'类型', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: false,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        lesysDatasources:{},
        optionsData:{
          ldIdData:[]
        },
        ldId:''
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      }
    },
    created() {
      this.loadDbDs()
    },
    methods: {
      async loadDbDs(){
        const {
          data: { list, total },
        } = await lesysDbDsGetPage({pageNo: 1,pageSize: 20})
        this.optionsData.ldIdData = list
        if(list.length>0){
          this.ldId = list[0].ldId
          this.fetchData()
        }
      },
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      loadTables(lesysDatasources) {
        this.lesysDatasources = lesysDatasources
        this.searchForm.ldtLdId = lesysDatasources.ldId
        this.fetchData()
      },

      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.lesysDbTablesForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await lesysDbTablesDoSaveOrUpd( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.lesysDbTablesEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.$refs['lesysDbTablesEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      async handleEdit(row) {
        this.row = row 
        //const  msg  = await lesysDatasourceTableReg( {tableName:row.ldtTableName,tableDesc:row.ldtTableComment,
        //          tableExplain:row.ldtTableComment,tableView:row.ldtType,tableLdid:row.ldtLdId,tableIscreate:'是',
        //          tableSource:'1',tableSort:'c103399837ec720b3b501d646fdbd5d1',ldtId:row.ldtId} )
        //if(msg.code == 200) {
        //  this.row.ldtReg = '是'
        //  this.$message({message:'操作成功!',type:'success'})
        //}else{
        //  this.$message({message:msg.msg||'操作失败!',type:'warning'})
        //}
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗', null, async () => {
			    const msg = await lesysDbTablesDoDelete({id:row.ldtId})
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'删除操作失败!',type:'warning'})
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysDbTablesQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysDbTablesQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysDbTablesQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysDbTablesGetPage(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"数据源数据表表.xls",
                      "isnumber":true,
                      "excelTitle":"数据源数据表表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#lesysDbTables",params)
        const { msg }  =  await lesysDbTablesDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"数据源数据表表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#lesysDbTables",params)
        const { msg }  =  await lesysDbTablesDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>