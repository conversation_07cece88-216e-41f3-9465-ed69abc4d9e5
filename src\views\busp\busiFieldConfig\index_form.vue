<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <div class="search-container">
      <!--el-input placeholder="业务字段" v-model="searchForm.bfColumn"  @change="handleSearch" style="width:150px;" />&nbsp;
      <el-input placeholder="业务属性" v-model="searchForm.bfName"    @change="handleSearch" style="width:150px;" />&nbsp;-->
      <el-button icon="el-icon-save" type="primary" @click="handleAdd3">
        编辑列(否)
      </el-button>
      <el-button icon="el-icon-save" type="primary" @click="handleAdd4">
        编辑列(是)
      </el-button>

      <el-button icon="el-icon-save" type="primary" @click="handleAdd6">
        编辑列必填(否)
      </el-button>
      <el-button icon="el-icon-save" type="primary" @click="handleAdd7">
        编辑列必填(是)
      </el-button>
      &nbsp;<el-input-number v-model="bfcOrderStart" :precision="0" :step="1" :max="500" :min="0"></el-input-number>
      &nbsp;<el-input-number v-model="bfcOrder"      :precision="0" :step="1" :max="500" :min="0"></el-input-number>&nbsp;
      
      <el-dropdown @command="handleAdd8">
        <el-button type="primary">
          保存顺序<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command='-'>向前</el-dropdown-item>
          <el-dropdown-item command='+'>向后</el-dropdown-item>
          <el-dropdown-item command='0'>重置</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>&nbsp;

      <el-button icon="el-icon-save" type="primary" @click="handleAdd5">
        保存配置
      </el-button> 
      <el-button icon="el-icon-save" type="primary" @click="handleAdd10">
        重置配置
      </el-button> 
      <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
        刷新
      </el-button>
    </div>

    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'selectbtn'" #default="{ row }">
          <el-input v-model="row[item.prop]" style="width:70%;"></el-input>
          <el-button icon="el-icon-search" circle @click="selectParamDir(row)"></el-button>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template v-else-if="item.editType === 'bfcEdittype'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in bfcEdittype"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template v-else-if="item.editType === 'bfcFunction'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in bfcFunction"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="80"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">保存</el-button>
          <!--el-button type="text" @click="handleDele(row)" :disabled="row.bfcIsquote=='是'">删除</el-button-->
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>
    <table-edit ref="lesysParamdirsSelRef">
      <lesysParamdirsSel
        ref="lesysParamdirsSel"
        :lpdSsign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="lesysParamdirsSelClose">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="lesysParamdirsSelSave"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,busiFieldConfigDoUpdateOrd,busiFieldConfigDoReset,busiFieldConfigDoDelete } from '@/api/busp/busiFieldConfig'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import TableEdit from '@/views/common/TableEdit.vue'
  import lesysParamdirsSel from '@/views/oa/lesysParamdirs/index_sel.vue'
  export default {
    name: 'busiFieldConfigForm',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      lesysParamdirsSel,
      TableEdit
    },
    data() {
      return {
        bfcOrder:0,
        bfcOrderStart: 0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['业务字段','业务实体','业务属性','业务类型','是否编辑','编辑类型','编辑顺序','最大长度','是否必填','编辑字典','是否禁用','是否引用'],
        columns: [
                { prop:'bfColumn'     , label:'业务字段'  , width:'auto' , sortable:false},
                { prop:'bfField'      , label:'业务实体'  , width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性'  , width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfColumnType' , label:'业务类型'  , width:'auto' , sortable:false  ,width:'90'},
                { prop:'bfcIsedit'    , label:'是否编辑'  , width:'auto' , sortable:false  ,editType:'select',width:'100'},
                { prop:'bfcEdittype'  , label:'编辑类型'  , width:'auto' , sortable:false  ,editType:'bfcEdittype',width:'140'},
                { prop:'bfcOrder'     , label:'编辑顺序'  , width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcMaxl'      , label:'最大长度'  , width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcRequire'   , label:'是否必填'  , width:'auto' , sortable:false  ,editType:'select',width:'100'},
                { prop:'bfcDictionary', label:'编辑字典'  , width:'auto' , sortable:false  ,editType:'selectbtn'},
                //{ prop:'bfcFunction'  , label:'事件函数'  , width:'auto' , sortable:false  ,editType:'bfcFunction',width:'80'},
                { prop:'bfcDisabled'  , label:'是否禁用'  , width:'auto' , sortable:false  ,editType:'select',width:'100'},
                { prop:'bfcIsquote'   , label:'是否引用'  , width:'auto' , sortable:false  ,width:'80'}        
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          bfVersion:'0',
          sortField:'BFC_ORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'}],
        busiModelRow:{},
        bfcEdittype:[{value:'TEXT',label:'文本'},{value:'TEXT-SELECT',label:'下拉框-单选'},
        {value:'TEXT-SELECTS',label:'下拉框-多选'},{value:'TEXT-RADIO',label:'单选框'},
        {value:'TEXT-CHECKBOX',label:'复选框'},{value:'HIDDEN',label:'隐藏域'},
        {value:'TEXTS',label:'文本域'},{value:'NUMBER',label:'数值'},
        {value:'DATE',label:'时间'}],
        bfcFunction:[{value:'',lable:''},{value:'change',label:'change'}]
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfVersion = row.bmVersion
        this.searchForm.bfcBmId = row.bmId
        this.searchForm.bfcBcId = row.bfcBcId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      selectParamDir(row){
        this.$refs['lesysParamdirsSelRef'].showEdit('选择字典')
      },
      lesysParamdirsSelClose(){
        this.$refs.lesysParamdirsSelRef.close()
      },
      lesysParamdirsSelSave(){
        var row = this.$refs.lesysParamdirsSel.getSel()
        this.row.bfcDictionary = "{\"name\":\""+row.lpdName+"\",\"type\":\"url\",\"data\":\""+row.lpdId+"\"}";
        this.$refs.lesysParamdirsSelRef.close()
      },
      handleAdd3(){
        this.handleAdd('configIsedit0','bfcIsedit','否')
      },
      handleAdd4(){
        this.handleAdd('configIsedit1','bfcIsedit','是')
      },
      handleAdd6(){
        this.handleAdd('configIsrequ0','bfcRequire','否')
      },
      handleAdd7(){
        this.handleAdd('configIsrequ1','bfcRequire','是')
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        var slist = []
        slist.push({bfcIsedit:row.bfcIsedit,
                        bfcOrder:row.bfcOrder,
                        bfcDictionary:row.bfcDictionary,
                        bfcRequire:row.bfcRequire,
                        bfcMaxl:row.bfcMaxl,
                        bfcEdittype:row.bfcEdittype,
                        bfName:row.bfName,
                        bfcId:row.bfcId,
                        bfcFunction:row.bfcFunction,
                        bfcDisabled:row.bfcDisabled,
                        bfcBfId:row.bfcBfId})
        const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
        if(msg.code == 200) {
          this.$message({message:'保存操作成功!',type:'success'})
        }else{
          this.$message({message:'保存操作失败!',type:'warning'})
        }
      },
      handleDele(row){
        if(row.bfcIsquote=='否'){
          this.$message({message:'非引用模型属性配置，无法删除!',type:'warning'})
        }else{
          this.$baseConfirm('确定删除引用的属性配置吗', null, async () => {
            const  msg  = await busiFieldConfigDoDelete( {'bfcId':row.bfcId} )
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
      },
      handleAdd10(){
        this.$baseConfirm('重置将清除所有配置信息，确定吗', null, async () => {
          const msg = await busiFieldConfigDoReset({bfBmId:this.busiModelRow.bmId,bfcBcId:this.busiModelRow.bfcBcId})
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      handleAdd8(command){
        if(command=='+'){
          this.$baseConfirm('确定将编辑顺序向后'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_ORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='-'){
          this.$baseConfirm('确定将编辑顺序向前'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_ORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='0'){
          this.$baseConfirm('确定重置编辑顺序吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_ORDER',bfcPlus:command,bfcPlusVal:0,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        } 
      },
      // 添加按钮事件
      async handleAdd(command,cm,cv) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIsedit:this.list[i].bfcIsedit,
                        bfcOrder:this.list[i].bfcOrder,
                        bfcDictionary:this.list[i].bfcDictionary,
                        bfcRequire:this.list[i].bfcRequire,
                        bfcMaxl:this.list[i].bfcMaxl,
                        bfcEdittype:this.list[i].bfcEdittype,
                        bfName:this.list[i].bfName,
                        bfcId:this.list[i].bfcId,
                        bfcFunction:this.list[i].bfcFunction,
                        bfcDisabled:this.list[i].bfcDisabled,
                        bfcBfId:this.list[i].bfcBfId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else{
          var slist = []
          for(var i=0;i<this.list.length;i++){
            var o = {bfcId:this.list[i].bfcId};
            o[cm] = cv
            slist.push(o)
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>