<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">


        <el-col :span="24">
          <el-form-item label="委托人" prop="lcFromOename">
            <el-input v-model.trim="form.lcFromOename" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="受托人" prop="lcToOename">
            <el-input v-model.trim="form.lcToOename" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="操作人" prop="lcOename">
            <el-input v-model.trim="form.lcOename" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="主键" prop="lcId"  style="display:none;">
            <el-input v-model.trim="form.lcId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上级ID" prop="lcPid"  style="display:none;">
            <el-input v-model.trim="form.lcPid" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="委托人ID" prop="lcFromLuid"  style="display:none;">
            <el-input v-model.trim="form.lcFromLuid" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="受托人ID" prop="lcToLuid"  style="display:none;">
            <el-input v-model.trim="form.lcToLuid" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作者ID" prop="lcLuid"  style="display:none;">
            <el-input v-model.trim="form.lcLuid" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="lcState"  style="display:none;">
            <el-input v-model.trim="form.lcState" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'lesysConsignForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>