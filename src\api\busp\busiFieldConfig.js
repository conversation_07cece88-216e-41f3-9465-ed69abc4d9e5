import request from '@/utils/request'

//列表分页加载数据
export function busiFieldConfigGetList(params) {
  return request({
    url: '/busi-field-config/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function busiFieldConfigDoSave(data) {
  return request({
    url: '/busi-field-config/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiFieldConfigDoUpdate(data) {
  return request({
    url: '/busi-field-config/update',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiFieldConfigDoUpdates(data) {
  return request({
    url: '/busi-field-config/updates',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiFieldConfigDoSaveQuote(data) {
  return request({
    url: '/busi-field-config/saveQuote',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiFieldConfigDoUpdateOrd(data) {
  return request({
    url: '/busi-field-config/updateOrd',
    method: 'post',
    data,
  })
}
export function busiFieldConfigDoReset(data) {
  return request({
    url: '/busi-field-config/updateReset',
    method: 'post',
    data,
  })
}
export function busiFieldConfigDoImp(data) {
  return request({
    url: '/busi-field-config/impField',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function busiFieldConfigDoSaveOrUpd(data) {
  return request({
    url: '/busi-field-config/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function busiFieldConfigDoSaveOrUpdLog(data) {
  return request({
    url: '/busi-field-config/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiFieldConfigDoDelete(data) {
  return request({
    url: '/busi-field-config/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function busiFieldConfigDoDeleteLog(data) {
  return request({
    url: '/busi-field-config/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function busiFieldConfigDoDeleteELog(data) {
  return request({
    url: '/busi-field-config/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function busiFieldConfigGetTreeList(params){
  return request({
    url: '/busi-field-config/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function busiFieldConfigDoExport(data) {
  return request({
    url: '/busi-field-config/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function busiFieldConfigGetStat(params) {
  return request({
    url: '/busi-field-config/vStat',
    method: 'get',
    params,
  })
}
