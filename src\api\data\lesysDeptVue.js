import request from '@/utils/request'

//列表分页加载数据
export function getList(params) {
  return request({
    url: '/lesys-dept-vue/vList',
    method: 'get',
    params,
  })
}
//初始化部门扩展信息
export function updateDeptExifs(data) {
  return request({
    url: '/lesys-dept-vue/updateDeptExifs',
    method: 'post',
    data,
  })
}
//保存新增,不记录日志
export function doSave(data) {
  return request({
    url: '/lesys-dept-vue/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function doUpdate(data) {
  return request({
    url: '/lesys-dept-vue/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function doSaveOrUpd(data) {
  return request({
    url: '/lesys-dept-vue/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function doSaveOrUpdLog(data) {
  return request({
    url: '/lesys-dept-vue/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function doDelete(data) {
  return request({
    url: '/lesys-dept-vue/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function doDeleteLog(data) {
  return request({
    url: '/lesys-dept-vue/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function doDeleteELog(data) {
  return request({
    url: '/lesys-dept-vue/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function getTreeList(params){
  return request({
    url: '/lesys-dept-vue/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function doExport(data) {
  return request({
    url: '/lesys-dept-vue/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function getStat(params) {
  return request({
    url: '/lesys-dept-vue/vStat',
    method: 'get',
    params,
  })
}

//保存更新,不记录日志
export function doUp(data) {
  return request({
    url: '/lesys-dept-vue/up',
    method: 'post',
    data,
  })
}

//保存更新,不记录日志
export function doDown(data) {
  return request({
    url: '/lesys-dept-vue/down',
    method: 'post',
    data,
  })
}
