<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysParamdirsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysParamdirsTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysParamdirsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="LesysParamdirs"
      row-key="lpdId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="250"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleVals(row)">参数维护</el-button>
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>

          <el-button type="text" @click="handleRedis(row)">更新Redis</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="lesysParamdirsEdit">
      <lesysParamdirsForm
        ref="lesysParamdirsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysParamdirsQuerySearch">
      <lesysParamdirsQuery
        ref="lesysParamdirsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="lesysParamvalsEdit" :fullscreen="false" :popWidth="windowWidth">
      <lesysParamvals
        ref="lesysParamvals"
        slot="form"/>
      <template slot="footerCont">

      </template>
    </table-edit >

    <table-edit ref="lesysParamvals1Edit" :fullscreen="false" :popWidth="windowWidth">
      <lesysParamvals1
        ref="lesysParamvals1"
        slot="form"/>
      <template slot="footerCont">

      </template>
    </table-edit >

  </div>
</template>

<script>
  import { lesysParamdirsDoDeleteELog,
           lesysParamdirsGetList,
           lesysParamdirsDoSaveOrUpdLog,
           lesysParamdirsDoExport } from '@/api/oa/lesysParamdirs'
  import { loadParamsToRedis } from '@/api/lesysparamvals'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysParamdirsSearch from './components/Search.vue'
  import lesysParamdirsForm from './components/Form.vue'
  import lesysParamdirsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  import lesysParamvals from '@/views/oa/lesysParamvals/index.vue'
  import lesysParamvals1 from '@/views/oa/lesysParamvals/index1.vue'
  export default {
    name: 'lesysParamdirs',
    props: {
      gheight: {
        type:Number
      },
      lpdSsign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysParamdirsSearch,
      lesysParamdirsForm,
      lesysParamdirsQuery,
      lesysParamvals,
      lesysParamvals1
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {lpdName:''},
        queryForm: {lpdName:''},
        form: {lpdName:'',lpdIstree:'',lpdSsign:'',lpdOrder:'',lpdDesc:''},
        rules: {
          lpdName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ],
          lpdIstree: [
            { required: true, message: '请输入是否为树形', trigger: 'blur' }
          ],
          lpdSsign: [
            { required: false, message: '请输入标识', trigger: 'blur' }
          ],
          lpdOrder: [
            { required: true, message: '请输入顺序', trigger: 'blur' }
          ],
          lpdDesc: [
            { required: false, message: '请输入描述', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','是否为树形','顺序','描述'],
        columns: [
                { prop:'lpdName', label:'名称', width:'auto' , sortable:true  },
                { prop:'lpdIstree', label:'是否为树形', width:'auto' , sortable:true  },
                { prop:'lpdOrder', label:'顺序', width:'auto' , sortable:true  },
                { prop:'lpdDesc', label:'描述', width:'auto' , sortable:true  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          lpdName:'',
          lpdSsign:this.lpdSsign,
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        windowWidth: '0px'
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    mounted() {
      window.addEventListener('resize', this.handleResize)
      this.handleResize()
    },
    methods: {
      handleResize() {
        this.windowWidth = window.innerWidth * 0.9 +'px'
        
      },
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.lesysParamdirsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.lpdId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await lesysParamdirsDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        });
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.lesysParamdirsEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={lpdIstree:'否','lpdOrder':this.total,'lpdSsign':this.lpdSsign};
        this.editType = 'add'
        this.$refs['lesysParamdirsEdit'].showEdit('添加')
      },
      //维护参数
      handleVals(row) {
        if(row.lpdIstree=='是'){
          this.$refs['lesysParamvals1Edit'].showEdit('目录参数维护')
          this.$nextTick(()=> {
            this.$refs.lesysParamvals1.init(row)
          })
        }else{
          this.$refs['lesysParamvalsEdit'].showEdit('目录参数维护')
          this.$nextTick(()=> {
            this.$refs.lesysParamvals.init(row)
          })
        } 
      },
      handleRedis(row){
        this.$baseConfirm('确定更新字典到内存数据库吗？', null, async () => {
          const msg = await loadParamsToRedis({lpvLpdId:row.lpdId})
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['lesysParamdirsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.lpdId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            row.logData = JSON.stringify(row) 
            row.logDesc = '删除参数目录表数据' 
            const msg = await lesysParamdirsDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        } else {
          if (this.selectRows.length > 0) {
            const ids = this.selectRows.map((item) => item.lpdId).join()
            this.$baseConfirm('确定删除吗', null, async () => {
              const msg = await lesysParamdirsDoDeleteELog({ "lpdId": ids,"logDesc":"删除参数目录表数据" })
              if(msg.code == 200) {
                this.$message({message:'删除操作成功!',type:'success'})
                await this.fetchData()
              }else{
                this.$message({message:'删除操作失败!',type:'warning'})
              }
            })
          } else {
            this.$baseMessage('请选择要删除的数据', 'error', 'vab-hey-message-error')
          }
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysParamdirsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysParamdirsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysParamdirsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.searchForm.lpdSsign = this.lpdSsign
        const {
          data: { list, total },
        } = await lesysParamdirsGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"createdTime":{"celltype":"date"},"updatedTime":{"celltype":"date"}},
                      "fileName":"参数目录表.xls",
                      "isnumber":true,
                      "excelTitle":"参数目录表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#LesysParamdirs",params)
        const { msg }  =  await lesysParamdirsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"参数目录表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#LesysParamdirs",params)
        const { msg }  =  await lesysParamdirsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>