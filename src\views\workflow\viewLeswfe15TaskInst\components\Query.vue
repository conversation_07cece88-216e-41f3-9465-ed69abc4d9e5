<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="24">
          <el-form-item label="实例名称" prop="instName">
            <el-input v-model.trim="form.instName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务名称" prop="nodeName">
            <el-input v-model.trim="form.nodeName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="创建人" prop="instCreateUxm">
            <el-input v-model.trim="form.instCreateUxm" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="创建时间" prop="instCreateTime">
            <el-date-picker v-model.trim="form.instCreateTimeStart" clearable type="date" value-format="yyyy-MM-dd"></el-date-picker> - <el-date-picker v-model.trim="form.instCreateTimeEnd" type="date" clearable value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'viewLeswfe15TaskInstQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>