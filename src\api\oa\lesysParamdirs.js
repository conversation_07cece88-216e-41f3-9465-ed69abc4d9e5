import request from '@/utils/request'

//列表分页加载数据
export function lesysParamdirsGetList(params) {
  return request({
    url: '/lesys-paramdirs-vue/vPage',
    method: 'get',
    params,
  })
}

export function lesysParamdirsGetSelList(params) {
  return request({
    url: '/lesys-paramdirs-vue/vPageSel',
    method: 'get',
    params,
  })
}

//保存新增,不记录日志
export function lesysParamdirsDoSave(data) {
  return request({
    url: '/lesys-paramdirs-vue/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysParamdirsDoUpdate(data) {
  return request({
    url: '/lesys-paramdirs-vue/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysParamdirsDoSaveOrUpd(data) {
  return request({
    url: '/lesys-paramdirs-vue/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function lesysParamdirsDoSaveOrUpdLog(data) {
  return request({
    url: '/lesys-paramdirs-vue/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysParamdirsDoDelete(data) {
  return request({
    url: '/lesys-paramdirs-vue/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function lesysParamdirsDoDeleteLog(data) {
  return request({
    url: '/lesys-paramdirs-vue/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function lesysParamdirsDoDeleteELog(data) {
  return request({
    url: '/lesys-paramdirs-vue/deleteLog/',
    method: 'post',
    data,
  })
}
//树形表格查询
export function lesysParamdirsGetTreeList(params){
  return request({
    url: '/lesys-paramdirs-vue/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function lesysParamdirsDoExport(data) {
  return request({
    url: '/lesys-paramdirs-vue/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function lesysParamdirsGetStat(params) {
  return request({
    url: '/lesys-paramdirs-vue/vStat',
    method: 'get',
    params,
  })
}
