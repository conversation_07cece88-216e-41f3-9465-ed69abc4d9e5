<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiFieldSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :busiModelRow="busiModelRow"
      ref="busiFieldTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="busiFieldTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiField"
      row-key="bfId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <table-edit ref="busiFieldEdit">
      <busiFieldForm
        ref="busiFieldForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="busiFieldQuerySearch">
      <busiFieldQuery
        ref="busiFieldQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

  </div>
</template>

<script>
  import { busiFieldDoDeleteELog,
           busiFieldGetList,
           busiFieldDoSaveOrUpdLog,
           busiFieldDoExport } from '@/api/busp/busiField'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiFieldSearch from './components/Search.vue'
  import busiFieldForm from './components/Form.vue'
  import busiFieldQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'busiFieldManage',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiFieldSearch,
      busiFieldForm,
      busiFieldQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          bfBmId: [
            { required: false, message: '请输入模型ID', trigger: 'blur' }
          ],
          bfBmTable: [
            { required: false, message: '请输入表名', trigger: 'blur' }
          ],
          bfColumn: [
            { required: false, message: '请输入字段名', trigger: 'blur' }
          ],
          bfColumnType: [
            { required: false, message: '请输入字段类型', trigger: 'blur' }
          ],
          bfEffState: [
            { required: false, message: '请输入是否有效', trigger: 'blur' }
          ],
          bfField: [
            { required: false, message: '请输入属性名', trigger: 'blur' }
          ],
          bfFieldType: [
            { required: false, message: '请输入属性类型', trigger: 'blur' }
          ],
          bfInterface: [
            { required: false, message: '请输入是否接口列', trigger: 'blur' }
          ],
          bfIspk: [
            { required: false, message: '请输入是否主键', trigger: 'blur' }
          ],
          bfName: [
            { required: false, message: '请输入属性中文', trigger: 'blur' }
          ],
          bfOrder: [
            { required: false, message: '请输入属性顺序', trigger: 'blur' }
          ],
          bfSort: [
            { required: false, message: '请输入统计/维度/其他', trigger: 'blur' }
          ],
          bfTitle: [
            { required: false, message: '请输入显示列名', trigger: 'blur' }
          ],
          bfType: [
            { required: false, message: '请输入属性类型', trigger: 'blur' }
          ],
          bfUseState: [
            { required: false, message: '请输入使用状态', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['属性名','字段分类','属性实体名','是否有效','使用状态','属性级别','属性顺序'],
        columns: [
                { prop:'bfName'     , label:'属性名'      , width:'auto' , sortable:false  },
              
                { prop:'bfSort'     , label:'字段分类'    , width:'auto' , sortable:false  },
                { prop:'bfField'    , label:'属性实体名'   , width:'auto' , sortable:false  },
                
                { prop:'bfEffState' , label:'是否有效'    , width:'auto' , sortable:false  },
                { prop:'bfUseState' , label:'使用状态'    , width:'auto' , sortable:false  },
                { prop:'bfLevel'    , label:'属性级别'    , width:'auto' , sortable:false  },
                { prop:'bfOrder'    , label:'属性顺序'    , width:'auto' , sortable:false  }
                
                
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfBmId:'',
          bfName:'',
          sortField:'',
          sortOrder:''
        },
        busiModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      //this.fetchData()
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfBmId = row.bmId
        this.fetchData()
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.busiFieldForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData=JSON.stringify(this.form)
            if(this.form.bfId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await busiFieldDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.busiFieldEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={bfEffState:'是',bfInterface:'否',bfIspk:'否',bfUseState:'启用',
                   bfLevel:'系统定义',bfBmTable:this.busiModelRow.bmTable,
                   bfBmId:this.busiModelRow.bmId,bfOrder:this.list.length,
                   bfSort:'其它',bfFieldType:'String',bfLength:0,bfIsreal:'是'}
        this.editType = 'add'
        this.$refs['busiFieldEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['busiFieldEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      // 删除行数据
      handleDelete(row) {
        if (row.bfId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            let row1 = {} 
            row1.logData = JSON.stringify(row) 
            row1.logDesc = '删除业务属性表数据' 
            row1.bfId = row.bfId 
            const msg = await busiFieldDoDeleteELog(row1)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        } else {
          if (this.selectRows.length > 0) {
            const ids = this.selectRows.map((item) => item.bfId).join()
            this.$baseConfirm('确定删除吗', null, async () => {
              const msg = await busiFieldDoDeleteELog({ "bfId": ids,"logDesc":"删除业务属性表数据" })
              if(msg.code == 200) {
                this.$message({message:'删除操作成功!',type:'success'})
                await this.fetchData()
              }else{
                this.$message({message:'删除操作失败!',type:'warning'})
              }
            })
          } else {
            this.$baseMessage('请选择要删除的数据', 'error', 'vab-hey-message-error')
          }
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['busiFieldQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.busiFieldQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.busiFieldQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"业务属性表.xls",
                      "isnumber":true,
                      "excelTitle":"业务属性表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#BusiField",params)
        const { msg }  =  await busiFieldDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"业务属性模板.xls",
                      "excelIstmpl":true,
                      "excelTmplDataCcs":"bfEffState#bfInterface#bfIspk#bfIsreal,bfColumnType,bfLevel,bfUseState,bfSort",
                      "excelTmplDataDds":"是#否,字符#时间#数值,系统定义#自定义,未启用#启用#停用,统计#维度#其它"}
        let qf = exportRearEnd("#BusiField",params)
        
        qf.excelExps[0].push({"field":"bfBmTable","title":"表名","rowspan":"1","colspan":"1","rowspand":"1","celltype":"text","cellwidth":"18","dateFormat":"yyyy-MM-dd HH:mm:ss","celliswrap":"0","cellwraplength":"0"})              
        const { msg }  =  await busiFieldDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>