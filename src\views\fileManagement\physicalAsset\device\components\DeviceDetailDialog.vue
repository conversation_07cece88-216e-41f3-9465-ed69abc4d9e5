<!--设备资产详情弹窗组件-->
<template>
  <el-dialog title="设备资产详情" :visible.sync="visibleShow" width="80%" append-to-body>
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产名称：</span>
              <span class="detail-value">{{ detailData.zcfaAssetsName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产状态：</span>
              <span class="detail-value">{{ detailData.zcfaAssetsState || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产类型：</span>
              <span class="detail-value">{{ detailData.zcfaType || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">产权单位：</span>
              <span class="detail-value">{{ detailData.companyName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">制造厂商：</span>
              <span class="detail-value">{{ detailData.zcfaManufactor || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">型号：</span>
              <span class="detail-value">{{ detailData.zcfaModel || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">序号：</span>
              <span class="detail-value">{{ detailData.zcfaSerialNumber || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">生产日期：</span>
              <span class="detail-value">{{ detailData.zcfaProductDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">入账日期：</span>
              <span class="detail-value">{{ detailData.zcfaRecordDate || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否两非资产：</span>
              <span class="detail-value">{{ detailData.zcfaIfTwonon || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否军工关键基础设施设备：</span>
              <span class="detail-value">{{ detailData.zcfaIfMilitary || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">设备是否停产：</span>
              <span class="detail-value">{{ detailData.zcfaIfStoppage || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="位置信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">国家：</span>
              <span class="detail-value">{{ detailData.zcfaCountryName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">省份：</span>
              <span class="detail-value">{{ detailData.zcfaIncityEconomize || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">城市：</span>
              <span class="detail-value">{{ detailData.zcfaIncityMarket || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">区/县：</span>
              <span class="detail-value">{{ detailData.zcfaIncityDistinguish || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">存放地点：</span>
              <span class="detail-value">{{ detailData.zcfaStoragePlace || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="财务信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">账面原值(万元)：</span>
              <span class="detail-value">{{ detailData.zcfaBookValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">账面净值(万元)：</span>
              <span class="detail-value">{{ detailData.zcfaNetbookValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资金来源：</span>
              <span class="detail-value">{{ detailData.zcfaFundsSource || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">折旧年限(年)：</span>
              <span class="detail-value">{{ detailData.zcfaUsefulLife || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否开票：</span>
              <span class="detail-value">{{ detailData.zcfaBill || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="技术信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">大类：</span>
              <span class="detail-value">{{ detailData.zcfaLargeCategory || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">类别：</span>
              <span class="detail-value">{{ detailData.zcfaCategory || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">专业类别：</span>
              <span class="detail-value">{{ detailData.zcfaSpecialCategory || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">共享类别：</span>
              <span class="detail-value">{{ detailData.zcfaSharedCategory || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">关键指标：</span>
              <span class="detail-value">{{ detailData.zcfaKeyFigures || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">设备能力等级：</span>
              <span class="detail-value">{{ detailData.zcfaEquipment || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="管理信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系人：</span>
              <span class="detail-value">{{ detailData.zcfaContacts || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系电话：</span>
              <span class="detail-value">{{ detailData.zcfaContactsPhone || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产用途：</span>
              <span class="detail-value">{{ detailData.zcfaUse || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">处置方式：</span>
              <span class="detail-value">{{ detailData.zcfaDisposition || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">闲置开始时间：</span>
              <span class="detail-value">{{ detailData.zcfaIdleStartTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="系统信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ detailData.createdTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value">{{ detailData.createdBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">更新时间：</span>
              <span class="detail-value">{{ detailData.updatedTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">备注：</span>
              <span class="detail-value">{{ detailData.zcfaRemark || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDeviceDetail } from '@/api/device'
export default {
  data () {
    return {
      visibleShow: false,
      detailData: {}
    }
  },
  methods: {
    showDialog (row) {
      getDeviceDetail(row.zcfaId).then(response => {
        if (response && response.data) {
          this.detailData = response.data
          this.visibleShow = true
        }
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },

    closeDialog () {
      // 手动关闭对话框
      this.visibleShow = false
    },

  }
}
</script>

<style scoped>
.detail-item {
  margin-bottom: 15px;
  line-height: 24px;
}
.detail-label {
  display: inline-block;
  min-width: 120px;
  color: #606266;
  font-weight: bold;
}
.detail-value {
  color: #303133;
}
</style>
