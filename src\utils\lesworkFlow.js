/*******************************************************************************/
/**  全局变量 ******************************************************************/

import axios from 'axios'

import request from '@/utils/request'
import { baseURL } from '@/config'

var __LESWORKFLOWURL = 'http://127.0.0.1:8401'
/* 定义流程查看 和 办理界面的 URL */
var __leswfeapi_inst_view_url = '/LesSystem/LesWorkflow/inst/inst_view.jsp'
var __leswfeapi_task_open_url = '/LesSystem/LesWorkflow/inst/task_open.jsp'

/** 屏幕的大小 **/
var _screen_width_height_pix = [
  window.screen.availWidth,
  window.screen.availHeight,
]

var windowOpen = null //子页面对象

/* 全局禁止流程回退, 不显示回退按钮, 不定义此参数则默认为 false(不禁止) */
//var __leswfe_disable_back  = true;

/* 限定每个节点最多可选办理人, 主要是为了提高性能, 不定义此参数则不做限制  */
//var __leswfe_node_max_user = 300;

/* 禁止表单缺少事件的警告提示 */
var __leswfe_disable_form_event_warning = true

/* 全局禁止表单可编辑域气泡提示 */
var __leswfe_disable_form_show_tips = true

/*******************************************************************************/
/*******************************************************************************/
/* 公用的流程表单注册类型的定义 */
var __leswfeapi_form_regtypes = {
  text: 1,
  hidden: 2,
  password: 3,
  textarea: 4,
  button: 5,
  checkbox: 6,
  'select-one': 7,
  'select-multiple': 8,
  reset: 5,
  submit: 5,
  iWebRevision: 100,
}
/* iWebRevision : 金格手写签批控件 */
/*******************************************************************************/
async function __call_rpc_method(func_name, param_arr) {
  var met = $_tostr(func_name)
  var pma = JSON.stringify(param_arr) // pure js array object

  var req = { jsonrpc: '2.0', method: met, params: pma, id: met }

  var url = '/LesWorkflowRpcService'
  // 配置请求头
  const config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  }

  let res = await axios.post(url, req, config)
  if (res.status == 200) {
    if (res.data.error) {
      var a = [
        '错误信息: ' + res.data.error['message'],
        '方法名称: ' + met,
        '错误代码:' + res.data.error['code'],
      ]
      throw new Error(a.join('\n'))
    } else if (res.data.result) {
      return res.data.result
    }
  }
}
/*************流程相关操作***********/
export function LesWorkFlowApi(param, flag) {
  if (flag == 'deal') {
    return leswfeapi_task_open(param.user_id, param.task_id)
  } else if (flag == 'view') {
    return leswfeapi_inst_view(param.inst_id, param.user_id)
  } else if (flag == 'attent') {
    return leswfeapi_inst_attent(param.user_id, param.inst_id, param.attent)
  } else if (flag == 'cancel') {
    return leswfeapi_inst_cancel(param.user_id, param.inst_id)
  } else if (flag == 'revoke') {
    return leswfeapi_task_revoke(param.task_id, param.user_id)
  } else if (flag == 'create') {
    return leswfeapi_inst_create(
      param.user_id,
      param.tmpl_id,
      param.inst_data || '',
      param.form_vars || {}
    )
  } else if (flag == 'durl') {
    return __leswfeapi_task_open_url
  } else if (flag == 'vurl') {
    return __leswfeapi_inst_view_url
  } else if (flag == 'open') {
  } else {
    return null
  }
}
/*******************************************************************************/
/* 创建流程实例: 启动人usre_id, 模版ID, 实例附加数据, 预置的表单值 {'fm01.inp01':'some value'...} */
function leswfeapi_inst_create(user_id, tmpl_id, x_inst_data, form_vars) {
  return __call_rpc_method('InstCreate', [
    $_tostr(user_id),
    $_tostr(tmpl_id),
    $_tostr(x_inst_data),
    form_vars || {},
  ])
}
/*******************************************************************************/
/* 设置或取消流程关注 attent = true/false */
function leswfeapi_inst_attent(user_id, task_inst_id, attent) {
  return __call_rpc_method('InstAttent', [
    $_tostr(user_id),
    $_tostr(task_inst_id),
    !!attent,
  ])
}
/*******************************************************************************/
/* 取消流程 */
function leswfeapi_inst_cancel(user_id, task_inst_id) {
  return __call_rpc_method('InstCancel', [
    $_tostr(user_id),
    $_tostr(task_inst_id),
  ])
}
/*******************************************************************************/
/* 打开流程查看界面 */
function leswfeapi_inst_view(task_inst_id, user_id) {
  var param = JSON.stringify({
    user_id: $_tostr(user_id),
    task_id: $_tostr(task_inst_id),
    sid: 'wf',
  })
  request({
    url: '/lesys-login-rsa/rsa-encode',
    method: 'post',
    data: param,
  }).then((res) => {
    if (res.code == 200) {
      var win = ShowWin(
        __leswfeapi_inst_view_url + '?sid=wf&uk=' + res.data,
        'inst_view_win_' + task_inst_id
      )
      if (win) win.focus()
      return win
    }
  })
}

export async function LesWorkFlowRsa(task_inst_id, user_id) {
  var param = JSON.stringify({
    user_id: $_tostr(user_id),
    task_id: $_tostr(task_inst_id),
    sid: 'wf',
  })
  let res = await request({
    url: '/lesys-login-rsa/rsa-encode',
    method: 'post',
    data: param,
  })
  if (res.code == 200) {
    return res.message
  } else {
    return null
  }
}

/*******************************************************************************/
/* 获取用户的待办任务列表 */
function leswfeapi_task_getlist(user_id, full_list) {
  return __call_rpc_method('TaskGetList', [$_tostr(user_id), !!full_list])
}
/*******************************************************************************/
/* 打开流程办理界面 */
function leswfeapi_task_open(user_id, task_id) {
  // var param = JSON.stringify({'user_id': $_tostr(user_id), 'task_id': $_tostr(task_id), 'sid': 'wf'});
  // request({url: '/lesys-login-rsa/rsa-encode',method: 'post',data:param}).then(res => {
  //     if(res.code==200){
  //         var win = ShowWin(__leswfeapi_task_open_url + "?sid=wf&uk="+res.data, 'task_open_win_' + task_id);
  //         if (win) win.focus();
  //         return win;
  //     }
  // })
  return ShowWin(
    __leswfeapi_task_open_url + '?sid=wf&uk=' + res.data,
    'task_open_win_' + task_id
  )
}
/*******************************************************************************/
/* 设置流程为 已读 状态 */
function leswfeapi_task_setread(task_id) {
  return __call_rpc_method('TaskSetRead', [task_id - 0])
}
/*******************************************************************************/
/* 获取流程表单全部 */
export function leswfeapi_task_getform(task_inst_id) {
  return __call_rpc_method('TaskGetForm', [$_tostr(task_inst_id)])
}
/*******************************************************************************/
/* 获取task_id任务有权限的表单域 */
export function leswfeapi_task_getperm(task_id) {
  return __call_rpc_method('TaskGetPerm', [task_id - 0])
}
/*******************************************************************************/
/* 获取task_id任务的所有相关属性 */
export function leswfeapi_task_getobj(task_id) {
  return __call_rpc_method('TaskGetObj', [task_id - 0])
}
/*******************************************************************************/
/* 暂存task_id任务 表单输入 */
export function leswfeapi_form_savetemp(task_id, form_vars) {
  var fv = $_str(form_vars) ? form_vars : JSON.stringify(form_vars)
  return __call_rpc_method('FormSaveTemp', [task_id - 0, fv]) // int , string
}
/*******************************************************************************/
/* 读取task_id任务 表单输入暂存 */
export function leswfeapi_form_readtemp(task_id) {
  var s = __call_rpc_method('FormReadTemp', [task_id - 0]) // int
  if (s.charAt(0) != '{') return ''
  return JSON.parse(s)
}
/*******************************************************************************/
function leswfeapi_atts_getlist(inst_id, task_id) {
  //todo: 暂缺...
  /* 获取流程的全部附件:
       - 只有 inst_id , 则只查 与 inst_id 相关的(即已经正式发送提交)的附件
       - 只有 task_id , 则只查 与 task_id 相关的(即在task_id这一步上传但未发送)的附件
       - inst_id 和 task_id 都有, 首先检查 task_id是否合法,
         同时检查 inst_id 是否与 task_id 关联
         如果都 ok, 则查询全部附件, 不区分是否提交 */
}

/*******************************************************************************/

/* 获取任务递交的所有可选目标节点和可选用户, send_routing = -1,0,+1 , 代表回退,传阅,发送 */
function leswfeapi_task_getnode(user_id, task_id, send_routing, form_vars) {
  var fv = form_vars
  if ($_str(fv) && fv.charAt(0) == '{') fv = JSON.parse(fv)
  return __call_rpc_method('TaskGetNode', [
    task_id - 0,
    $_tostr(user_id),
    send_routing - 0,
    fv || {},
  ])
}

/*******************************************************************************/

/* 执行任务递交(回退, 传阅, 发送)... */
function leswfeapi_task_commit(user_id, task_id, send_data, form_vars) {
  var sd = send_data
  if ($_str(sd) && sd.charAt(0) == '{') sd = JSON.parse(sd)
  var fv = form_vars
  if ($_str(fv) && fv.charAt(0) == '{') fv = JSON.parse(fv)
  return __call_rpc_method('TaskCommit', [
    task_id - 0,
    $_tostr(user_id),
    sd || {},
    fv || {},
  ])
}

/*******************************************************************************/

/* 执行任务取回, 参数 task_id 的下级 task 必须全部是未办理的任务,
   返回 成功取回的任务个数, 或 抛异常 */
function leswfeapi_task_revoke(task_id, user_id) {
  return __call_rpc_method('TaskRevoke', [task_id - 0, $_tostr(user_id)])
}

/*******************************************************************************/

/* 获取用户的相关属性 */
function leswfeapi_user_getprop(user_id) {
  return __call_rpc_method('UserGetProp', [$_tostr(user_id)])
}

/*******************************************************************************/

/** 转 string, undefined, null, NaN, 正负无穷 都返回空字符串 **/
function $_tostr(v) {
  return $_und(v) || $_null(v) || $_nan(v) ? '' : '' + v
}

/*******************************************************************************/

/** 类型判断 实用函数 **/
function $_und(v) {
  return !!(typeof v == 'undefined')
}
function $_null(v) {
  return !!(v === null)
}
function $_nan(v) {
  return !(v === v)
}
function $_num(v) {
  return !!isFinite(v - 0)
}
function $_obj(v) {
  return !!(
    (v && typeof v == 'object') ||
    (v && '' + v.constructor == '' + Object)
  )
}
function $_str(v) {
  return !!(typeof v == 'string' || (v && '' + v.constructor == '' + String))
}
function $_bool(v) {
  return !!(typeof v == 'boolean' || (v && '' + v.constructor == '' + Boolean))
}
function $_func(v) {
  return !!(
    typeof v == 'function' ||
    (v && '' + v.constructor == '' + Function)
  )
}
function $_arr(v) {
  return !!(
    (Array.isArray && Array.isArray(v)) ||
    (v && typeof v.push == 'function' && typeof v.shift == 'function')
  )
}
function $_date(v) {
  return !!(
    (Date.isDate && Date.isDate(v)) ||
    (v &&
      typeof v.getFullYear == 'function' &&
      typeof v.getUTCFullYear == 'function')
  )
}

/*******************************************************************************/

/** 转 number, 如果转不成功返回 def **/
function $_tonum(v, def) {
  var nv = $_tostr(v)
  if (nv == '') return def
  return $_num(nv) ? nv - 0 : def
}

/**
 * 获取当前页面url传递的参数（例如http://xxxxxx?paramName1=value&paramName2=value）
 * @param paramName
 * @returns {string|null}
 */
export function getParam(paramName) {
  var reg = new RegExp('(^|&)' + paramName + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) {
    return decodeURI(r[2])
  } else {
    return null
  }
}

/*******************************************************************************/

/** 打开简化的子窗口, name名称可选, 宽度sizeW, 高度sizeH, 左边posL, 上边posT 为像素值 **/
export function LesWorkFlowShow(url, name, sizeW, sizeH, posL, posT) {
  var fc = false
  if (arguments.length <= 2) fc = true // 不传大小, 默认全屏

  var w = $_tonum(sizeW, 600)
  var h = $_tonum(sizeH, 400)

  if (w >= _screen_width_height_pix[0] && h >= _screen_width_height_pix[1])
    fc = true

  var l = 0,
    t = 0
  if (fc) {
    w = _screen_width_height_pix[0] - 18 // 全屏
    h = _screen_width_height_pix[1] - 64
    l = 0
    t = 0
  } else {
    if (w > 0 && w < 1) w = w * _screen_width_height_pix[0]
    if (h > 0 && h < 1) h = h * _screen_width_height_pix[1]
    w = Math.max(100, Math.min(w, _screen_width_height_pix[0] - 0))
    h = Math.max(100, Math.min(h, _screen_width_height_pix[1] - 0))
    l = $_tonum(posL, (_screen_width_height_pix[0] - w) / 2)
    t = $_tonum(posT, (_screen_width_height_pix[1] - h) / 2)
  }

  windowOpen = window.open(
    url,
    $_tostr(name),
    (fc ? 'xxfullscreen=yes,' : '') +
      'height=' +
      h +
      ',width=' +
      w +
      ',top=' +
      t +
      ',left=' +
      l +
      ',toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=yes,status=no'
  )
  try {
    windowOpen.focus()
  } catch (ex) {}
  return windowOpen
}
