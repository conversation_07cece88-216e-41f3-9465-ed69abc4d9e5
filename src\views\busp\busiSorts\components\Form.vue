<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="名称" prop="busiName">
            <el-input v-model.trim="form.busiName" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="顺序" prop="busiIndx">
            <el-input-number v-model.trim="form.busiIndx" style="width:100%" :precision="0" :min="0" :max="100"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="busiId"  style="display:none;">
            <el-input v-model.trim="form.busiId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: 'busiSortsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition
      }
    },
    created() {
      
    },
    methods: {
      
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>