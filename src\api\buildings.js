import request from '@/utils/request'

// 查询房屋列表
export function getBuildingsList(query) {
  return request({
    url: '/zcgl-house-info/vPageList',
    method: 'get',
    params: query
  })
}

// 查询房屋详情
export function getBuildingDetail(id) {
  return request({
    url: `/zcgl-house-info/get-by-id/${id}`,
    method: 'get'
  })
}

// 导出房屋数据
export function exportBuildings(query) {
  return request({
    url: '/zcgl-house-info/vExport',
    method: 'post',
    data: query
  })
}

// 统计房屋数量和价值
export function getBuildingsStats() {
  return request({
    url: '/zcgl-house-info/getStatistic',
    method: 'get'
  })
} 