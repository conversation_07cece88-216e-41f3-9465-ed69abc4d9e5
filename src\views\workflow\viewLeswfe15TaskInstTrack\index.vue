<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <viewLeswfe15TaskInstSearch
      :checkList="viewLeswfe15TaskInstCheckList"
      :columns="viewLeswfe15TaskInstColumns"
      :queryForm="viewLeswfe15TaskInstSearchForm"
      ref="viewLeswfe15TaskInstTs"
      @viewLeswfe15TaskInstHandleHeight="viewLeswfe15TaskInstHandleHeight"
      @viewLeswfe15TaskInstHandleSearch="viewLeswfe15TaskInstHandleSearch"
      @viewLeswfe15TaskInstHandleCheckedChange="viewLeswfe15TaskInstHandleCheckedChange"
      @viewLeswfe15TaskInstHandleExportRear="viewLeswfe15TaskInstHandleExportRear"
      @viewLeswfe15TaskInstHandleQuery="viewLeswfe15TaskInstHandleQuery"
      @handleViewFlow="handleViewFlow"
      @handleDealFlow="handleDealFlow"/>

    <el-table
      ref="viewLeswfe15TaskInstTable"
      v-loading="listLoading"
      border
      :data="viewLeswfe15TaskInstList"
      :height="height"
      stripe
      @cell-dblclick="viewLeswfe15TaskInstCellDblClick"
      id="ViewLeswfe15TaskInst"
      row-key="taskId"
      @sort-change="viewLeswfe15TaskInstSortChange"
      highlight-current-row 
      @current-change="viewLeswfe15TaskInstCurrentSelectRow"
    >
      <!--el-table-column
        align="center"
        type="selection"
        width="55"
        label-class-name="checkbox" /-->

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleViewFlow(row)">查看</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="viewLeswfe15TaskInstSearchForm.pageNo"
      :layout="layout"
      :page-size="viewLeswfe15TaskInstSearchForm.pageSize"
      :total="viewLeswfe15TaskInstTotal"
      @current-change="viewLeswfe15TaskInstHandleCurrentChange"
      @size-change="viewLeswfe15TaskInstHandleSizeChange"
    />

    <table-search ref="viewLeswfe15TaskInstQuerySearch">
      <viewLeswfe15TaskInstQuery
        ref="viewLeswfe15TaskInstQueryForm"
        slot="form"
        :form="viewLeswfe15TaskInstQueryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="viewLeswfe15TaskInstQueryClose">
          取 消
        </el-button>
        <el-button
          @click="viewLeswfe15TaskInstQueryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="viewLeswfe15TaskInstQuerySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="leswfe15TaskTrack" :fullscreen="false" :popWidth="popWidth">
      <leswfe15Task
        ref="leswfe15Task"
        slot="form"
        @trackClose="trackClose"/>
      <template slot="footerCont">
        <el-button
          @click="trackClose">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="trackSure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

  </div>
</template>

<script>
  import { viewLeswfe15TaskInstDoDeleteELog,viewLeswfe15TaskInstGetTrackList,viewLeswfe15TaskInstDoSaveOrUpdLog,viewLeswfe15TaskInstDoExport } from '@/api/workflow/viewLeswfe15TaskInst'
  import TableSearch from '@/views/common/TableSearch.vue'
  import viewLeswfe15TaskInstSearch from './components/Search.vue'
  import viewLeswfe15TaskInstQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import TableEdit from '@/views/common/TableEdit.vue'
  import { LesWorkFlowApi,LesWorkFlowRsa,LesWorkFlowShow } from '@/utils/lesworkFlow'

  import leswfe15Task from '@/views/workflow/leswfe15Task/index.vue'

  export default {
    name: 'viewLeswfe15TaskInstTrack',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableSearch,
      viewLeswfe15TaskInstSearch,
      viewLeswfe15TaskInstQuery,
      leswfe15Task,
      TableEdit
    },
    data() {
      return {
        fullscreenLoading: false,
        viewLeswfe15TaskInstQueryFormDf: {instName:'',nodeName:'',instCreateUxm:'',tmplType:''},
        viewLeswfe15TaskInstQueryForm: {instName:'',nodeName:'',instCreateUxm:'',tmplType:''},
        viewLeswfe15TaskInstForm: {instId:'',nodeId:'',nodeName:'',taskPid:'',taskGid:'',taskUid:'',taskUxm:'',taskTime:'',taskIsNew:'',taskDoUid:'',taskDoUxm:'',taskDoTime:'',taskDoIdea:'',lastNodeName:'',instCreateDept:'',tmplId:'',instName:'',instState:'',instSec:'',instCreateUid:'',instCreateUxm:'',instCreateTime:'',tmplType1:'',tmplType2:'',tmplType3:'',tmplType:''},

        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        viewLeswfe15TaskInstCheckList: ['流程类型','实例名称','任务名称','任务时间','创建用户','创建时间'],
        viewLeswfe15TaskInstColumns: [
                { prop:'tmplType', label:'流程类型', width:'auto' , sortable:true  },
                { prop:'instName', label:'实例名称', width:'auto' , sortable:true  },
                { prop:'nodeName', label:'任务名称', width:'auto' , sortable:true  },
                { prop:'taskTime', label:'任务时间', width:'auto' , sortable:true  },
                { prop:'instCreateUxm', label:'创建用户', width:'auto' , sortable:true  },
                { prop:'instCreateTime', label:'创建时间', width:'auto' , sortable:true  }
        ],
        viewLeswfe15TaskInstList: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        viewLeswfe15TaskInstTotal: 0,
        viewLeswfe15TaskInstRow: '',
        viewLeswfe15TaskInstSearchForm: {
          instName:'',
          taskUid:'',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        popWidth:'1080px'
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username'
      }),
      finallyColumns() {
        return this.viewLeswfe15TaskInstColumns.filter((item) =>
          this.viewLeswfe15TaskInstCheckList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.viewLeswfe15TaskInstSearchForm.pageNo - 1) * this.viewLeswfe15TaskInstSearchForm.pageSize + index + 1
      },
      //列排序事件
      viewLeswfe15TaskInstSortChange(sortColumn){
        this.viewLeswfe15TaskInstSearchForm.sortField=sortColumn.prop
        this.viewLeswfe15TaskInstSearchForm.sortOrder=sortColumn.order
        this.fetchData()
      },
      // 可拖拽列复选框点击事件
      viewLeswfe15TaskInstHandleCheckedChange($event) {
        this.viewLeswfe15TaskInstCheckList = $event
      },
      // 全屏事件
      viewLeswfe15TaskInstHandleHeight($event) {
        this.isFullscreen = $event
        if ($event) this.height = this.$baseTableHeight(1,1) + 150
        else this.height = this.$baseTableHeight(1,1)
      },
      // 行点击切换事件
      viewLeswfe15TaskInstCurrentSelectRow(val) {
        this.viewLeswfe15TaskInstRow = val
      },
      // 双击行编辑事件
      viewLeswfe15TaskInstCellDblClick(row) {
        this.viewLeswfe15TaskInstRow = row
        this.handleViewFlow()
      },
      // 分页每页条数改变
      viewLeswfe15TaskInstHandleSizeChange(val) {
        this.viewLeswfe15TaskInstSearchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      viewLeswfe15TaskInstHandleCurrentChange(val) {
        this.viewLeswfe15TaskInstSearchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      viewLeswfe15TaskInstHandleSearch($event) {
        this.viewLeswfe15TaskInstSearchForm = $event
        this.viewLeswfe15TaskInstSearchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      viewLeswfe15TaskInstHandleQuery() {
        this.viewLeswfe15TaskInstQueryForm = Object.assign(this.viewLeswfe15TaskInstQueryForm,this.viewLeswfe15TaskInstSearchForm)
        this.$refs['viewLeswfe15TaskInstQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      viewLeswfe15TaskInstQueryClose(){
        this.$refs.viewLeswfe15TaskInstQuerySearch.close()
      },
      //高级查询清空
      viewLeswfe15TaskInstQueryClear(){
        this.viewLeswfe15TaskInstQueryForm = Object.assign(this.viewLeswfe15TaskInstQueryForm,this.viewLeswfe15TaskInstQueryFormDf)
      },
      //高级查询
      viewLeswfe15TaskInstQuerySure(){
        //this.viewLeswfe15TaskInstSearchForm = Object.assign(this.viewLeswfe15TaskInstSearchForm,this.viewLeswfe15TaskInstQueryForm)
        for(let key in this.viewLeswfe15TaskInstQueryForm){
          this.viewLeswfe15TaskInstSearchForm[key] = this.viewLeswfe15TaskInstQueryForm[key]
        }
        this.viewLeswfe15TaskInstSearchForm.pageNo = 1
        this.$refs.viewLeswfe15TaskInstQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.viewLeswfe15TaskInstSearchForm.taskUid=this.username
        this.listLoading = true
        const {
          data: { list, total },
        } = await viewLeswfe15TaskInstGetTrackList(this.viewLeswfe15TaskInstSearchForm)
        this.viewLeswfe15TaskInstList = list
        this.viewLeswfe15TaskInstTotal = total
        this.listLoading = false
      },
      // 后端导出
      async viewLeswfe15TaskInstHandleExportRear(){
        let params = {"dataFields":{"taskTime":{"celltype":"date"},"instCreateTime":{"celltype":"date"}},
                      "fileName":"待办任务视图.xls","isnumber":true,"excelTitle":"待办任务视图","queryForm":this.viewLeswfe15TaskInstSearchForm||{}}
        let qf = exportRearEnd("#ViewLeswfe15TaskInst",params)
        const { msg }  =  await viewLeswfe15TaskInstDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      handleViewFlow(row) {
        if(row){
          this.viewLeswfe15TaskInstRow = row
        }
        if(this.viewLeswfe15TaskInstRow.instId){
          let res = LesWorkFlowRsa(this.viewLeswfe15TaskInstRow.instId,this.username)
          res.then(res => {
            var win = LesWorkFlowShow(LesWorkFlowApi(null,"vurl") + "?sid=wf&uk="+res, 'inst_view_win_' + this.viewLeswfe15TaskInstRow.instId)
            if (win) win.focus()
            win.addEventListener("message", (event) => {
              if(event.data=='流程查看关闭'){
                this.fetchData()
              }
            }, false)
          }) 
        }
      },
      handleDealFlow(){
        if(this.viewLeswfe15TaskInstRow.taskId){
          this.$refs['leswfe15TaskTrack'].showEdit('任务取回')
          this.$nextTick(()=> {
            this.$refs.leswfe15Task.initTask(this.viewLeswfe15TaskInstRow)
          })
        }
      },
      trackClose(){
        this.$refs.leswfe15TaskTrack.close()
      },
      trackSure(){
        this.$nextTick(()=> {
          this.$refs.leswfe15Task.rebackTask(this.viewLeswfe15TaskInstRow)
        })
        //this.$refs.leswfe15TaskTrack.close()
      }
    },
  }
</script>