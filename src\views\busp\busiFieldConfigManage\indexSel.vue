<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-row :gutter="20">
      <el-col :span="6" >
        <busiModelSimple @busiModelRowSelect="busiModelRowSelect" :bmSign="bdSsign">
          
        </busiModelSimple>
      </el-col>
      <el-col :span="18">
        <busiFieldConfig1 ref="busiFieldConfig1">
          
        </busiFieldConfig1>
      </el-col>
    </el-row>  
  </div>
</template>

<script>
  import busiModelSimple from '@/views/busp/busiModel/indexSimple.vue'
  import busiFieldConfig1 from './index1.vue'

  export default {
    name: 'busiModelFieldConfigSel',
    props: {
      bdSsign: {
        type:String
      },
      bmId:{
        type:String
      }
    },
    components: {
      busiModelSimple,
      busiFieldConfig1
    },
    data() {
      return {
        isFullscreen:false
      }
    },
    computed: {

    },
    created() {
      
    },
    methods: {
      init(row){

      },
      busiModelRowSelect(row){
        this.$refs.busiFieldConfig1.init(row,this.bmId)
      }  
    }
  }
</script>