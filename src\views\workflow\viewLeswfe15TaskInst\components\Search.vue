<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-badge :value="taskNums" class="item">
              <el-dropdown style="margin-right: 10px" trigger="click">
                <el-button type="primary">
                  委托人任务
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-radio-group v-model="radio" @change="changeTaskUser">
                    <el-dropdown-item>
                      <el-radio :label="loginUser.luId">{{ '我的任务' }}</el-radio>
                    </el-dropdown-item>
                    <el-dropdown-item v-for="(item, i) in userList" :key="i">
                      <el-radio :label="item.lcFromLuid">
                        <span style="width: 80px">{{ item.lcFromOename }}({{item.taskNums}})</span>
                      </el-radio>
                    </el-dropdown-item>
                  </el-radio-group>
                  <el-dropdown-item />
                </el-dropdown-menu>
              </el-dropdown>
            </el-badge>
          </el-form-item>
          <el-form-item>
            <el-input placeholder="实例名称" v-model="tableQueryForm.instName"  @keyup.enter.native="handleSearch" />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>

            <el-button icon="el-icon-view" type="primary" @click="handleViewFlow">
              查看
            </el-button>
            <el-button icon="el-icon-success" type="primary" @click="handleDealFlow">
              办理
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <!--el-button icon="el-icon-download" type="primary" @click="handleExportRear" title="导出表格"> 
        </el-button>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
          title="表格全屏"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          
        </el-button>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="taleCheckList" @change="handleCheckedChange">
            <vab-draggable v-bind="dragOptions" :list="tableColums">
              <div v-for="(item, index) in tableColums" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
              title="可拖拽列设置"
            >
            </el-button>
          </template>
        </el-popover-->
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'
  import axios from 'axios'
  import config from '@/config'
  import store from '@/store'
  import { mapGetters } from 'vuex'

  import { lesysConsignGetMeList } from '@/api/oa/lesysConsign'
  export default {
    name: 'viewLeswfe15TaskInstSearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList,
        fileUploadBtnText: "导入",
        uploadBtnIcon:"el-icon-upload2",
        fileAccept:".xls,.xlsx",
        radio:'',
        userList:[],
        taskNums:0
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    created() {
      this.radio = this.loginUser.luId
      this.getTaskUser()
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal;
      }
    },
    methods: {
      // 查询委托人任务
      getTaskTable(param) {
        this.$emit('handleTask',param)
      },
      // 获取委托人列表
      async getTaskUser() {
        let res = await lesysConsignGetMeList({'lcFromLuid':this.loginUser.luId})
        if(res.code==200){
          this.userList = res.data
          this.taskNums = 0
          for(let i=0;i<this.userList.length;i++){
            this.taskNums = this.taskNums + this.userList[i].taskNums
          }
        }else{
          this.userList = []
          this.taskNums = 0
        }
      },
      // 切换委托人任务
      changeTaskUser(val) {
        this.getTaskTable(val)
      },
      //办理完成后，刷新待办
      reloadTaskUser(taskUid){
        this.radio = taskUid
        this.getTaskUser()
      },
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm)
      },
      handleQuery() {
        this.$emit('handleQuery')
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd')
      },
      clickFullScreen() {
        this.isFullscreen = !this.isFullscreen
        this.$emit("handleHeight",this.isFullscreen)
      },
      handleCheckedChange(val) {
        this.$emit("handleCheckedChange",val)
      },
      // 导出
      handleExportRear() {
        this.$emit("handleExportRear")
      },
      //导出excel模板
      handleExportTmpl() {
        this.$emit("handleExportTmpl")
      },
      handleViewFlow(){
        this.$emit("handleViewFlow")
      },
      handleDealFlow(){
        this.$emit("handleDealFlow")
      },
      //导入
      async uploadFile(param){
        
      }
    },
  }
</script>

<style>
  .item {
    margin-right: 10px;
  }
  </style>