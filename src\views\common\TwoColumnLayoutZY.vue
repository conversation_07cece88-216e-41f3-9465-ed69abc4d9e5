<template>
  <div class="threeColumnLayout-container">
    <el-row style="display: flex;justify-content: space-between;">
      <el-col :span="leftGrid">
        <div class="grid-content">
          <slot name="left" />
        </div>
      </el-col>
      <el-col :span="rightGrid">
        <div class="grid-content">
          <slot name="right" />
        </div>
      </el-col>
      
    </el-row>
  </div>
</template>

<script>
  export default {
    name: 'TwoColumnLayoutZY',
    props: {
      leftGrid: {
        type: Number,
        default: 6
      },
      rightGrid: {
        type: Number,
        default: 18
      }
    }
  }
</script>
