<!--房屋资产详情弹窗组件-->
<template>
  <el-dialog title="房屋资产详情" :visible.sync="visibleShow" width="80%" append-to-body>
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产编号：</span>
              <span class="detail-value">{{ detailData.zchiAssetsNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">资产名称：</span>
              <span class="detail-value">{{ detailData.zchiAssetsName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">现状用途：</span>
              <span class="detail-value">{{ detailData.zchiUseDescribe || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">产权单位：</span>
              <span class="detail-value">{{ detailData.companyName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">产权证号：</span>
              <span class="detail-value">{{ detailData.zchiCertificateCode || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">地理位置：</span>
              <span class="detail-value">{{ detailData.zchiAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">取得方式：</span>
              <span class="detail-value">{{ detailData.zchiHouseSource || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">取得时间：</span>
              <span class="detail-value">{{ detailData.zchiDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">境内/境外：</span>
              <span class="detail-value">{{ detailData.zchiCountry || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否两非资产：</span>
              <span class="detail-value">{{ detailData.zchiIfAssets || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否取得房屋产权证：</span>
              <span class="detail-value">{{ detailData.zchiIfExist || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否存在纠纷：</span>
              <span class="detail-value">{{ detailData.zchiIfDispute || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否可处置：</span>
              <span class="detail-value">{{ detailData.zchiIfDispose || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">是否存在抵押：</span>
              <span class="detail-value">{{ detailData.zchiIfMortgage || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系人：</span>
              <span class="detail-value">{{ detailData.zchiOperator || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="位置信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">省份：</span>
              <span class="detail-value">{{ detailData.zchiProvince || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">城市：</span>
              <span class="detail-value">{{ detailData.zchiCity || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">区/县：</span>
              <span class="detail-value">{{ detailData.zchiCounty || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">详细地址：</span>
              <span class="detail-value">{{ detailData.zchiAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="财务信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">原值(万元)：</span>
              <span class="detail-value">{{ detailData.zchiOriginalValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">净值(万元)：</span>
              <span class="detail-value">{{ detailData.zchiNetValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">累计折旧(万元)：</span>
              <span class="detail-value">{{ detailData.zchiTotalDepreciation || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">评估价值(万元)：</span>
              <span class="detail-value">{{ detailData.zchiEvaluateValue || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">评估日期：</span>
              <span class="detail-value">{{ detailData.zchiEvaluateDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">使用年限(年)：</span>
              <span class="detail-value">{{ detailData.zchiServiceLife || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">计提年限：</span>
              <span class="detail-value">{{ detailData.zchiDepreciableYear || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="面积信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">建筑面积(㎡)：</span>
              <span class="detail-value">{{ detailData.zchiArea || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">科研办公面积：</span>
              <span class="detail-value">{{ detailData.zchiOfficeArea || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">商业面积：</span>
              <span class="detail-value">{{ detailData.zchiCommercialArea || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">住宅面积：</span>
              <span class="detail-value">{{ detailData.zchiResidentialArea || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">工业面积：</span>
              <span class="detail-value">{{ detailData.zchiIndustrialArea || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">地下建筑面积：</span>
              <span class="detail-value">{{ detailData.zchiUndergroundArea || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">其他面积：</span>
              <span class="detail-value">{{ detailData.zchiOtherArea || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="管理信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">业务管理部门：</span>
              <span class="detail-value">{{ detailData.zchiDeptName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门负责人：</span>
              <span class="detail-value">{{ detailData.zchiDepartmentLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">部门电话：</span>
              <span class="detail-value">{{ detailData.zchiDepartmentTel || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分公司负责人：</span>
              <span class="detail-value">{{ detailData.zchiCompanyLeader || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">分公司电话：</span>
              <span class="detail-value">{{ detailData.zchiCompanyTel || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系人：</span>
              <span class="detail-value">{{ detailData.zchiOperator || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">联系电话：</span>
              <span class="detail-value">{{ detailData.zchiOperatorTel || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="系统信息">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ detailData.createdTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value">{{ detailData.createdBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">更新时间：</span>
              <span class="detail-value">{{ detailData.updatedTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">备注：</span>
              <span class="detail-value">{{ detailData.zchiRemark || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getBuildingDetail } from '@/api/buildings'
export default {
  data () {
    return {
      visibleShow: false,
      detailData: {}
    }
  },
  methods: {
    showDialog (row) {
      getBuildingDetail(row.zchiId).then(response => {
        if (response && response.data) {
          this.detailData = response.data
          this.visibleShow = true
        }
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },

    closeDialog () {
      // 手动关闭对话框
      this.visibleShow = false
    },

  }
}
</script>

<style scoped>
.detail-item {
  margin-bottom: 15px;
  line-height: 24px;
}
.detail-label {
  display: inline-block;
  min-width: 120px;
  color: #606266;
  font-weight: bold;
}
.detail-value {
  color: #303133;
}
</style> 