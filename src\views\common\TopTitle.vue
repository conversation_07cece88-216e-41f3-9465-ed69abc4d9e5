<template>
  <div>
    <el-row style="margin-bottom: 10px;position: relative">
      <div :style="{ width: '100%', height: height }">
        <div class="imgClass" />

        <div class="zwClass">
          {{ zwName }}
        </div>

        <div class="ywClass" :style="{ left: ywLeft }">
          {{ ywName }}
        </div>
        <div class="editButton">
          <slot name="editButton"></slot>
        </div>

      </div>


    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'TopTitle',
    props: {
      zwName: {
        type: String,
        default: '',
      },
      ywName: {
        type: String,
        default: '',
      },
      height: {
        type: String,
        default: '40px',
      },
    },
    data() {
      return {}
    },
    computed: {
      ywLeft() {
        return this.zwName.length * 15 + 15 + 15 + 'px'
      },
    },
    mounted() {},
    methods: {},
  }
</script>

<style scoped lang="scss">
  .imgClass {
    background-image: url('../../assets/common_images/toptitleImg.png');
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%; //
    height: 100%; //
    position: absolute;
    left: 0;
    top: 0;
  }

  .zwClass {
    font-weight: bolder;
    color: #cc1214;
    font-size: 17px;
    position: absolute;
    top: 50%;
    transform: translateY(-45%);
    left: 15px;
  }
  .ywClass {
    font-weight: bold;
    color: #c6ae6e;
    font-size: 16px;
    position: absolute;
    top: 50%;
    transform: translateY(-45%);
    left: 80px;
  }
  .editButton {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
  }
</style>
