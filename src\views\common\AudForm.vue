<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        :model="form">
        <el-col :span="24">
          <el-form-item prop="audAdvice">
            <el-input v-model.trim="form.audAdvice" maxlength="100" show-word-limit clearable type="textarea" :rows="4"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  
  export default {
    name: 'audForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form
      }
    },
    created() {
      
    },
    methods: {
      
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>