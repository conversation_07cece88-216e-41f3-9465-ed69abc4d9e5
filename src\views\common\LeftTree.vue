<template>
  <el-container style="padding: 0px">
    <el-row>
      <el-col :span="24">
        <el-input
          v-model="filterText"
          clearable
          placeholder="请输入关键字"
          size="small"
          style="width: 240px"
        />
      </el-col>
      <el-col :span="24">
        <el-scrollbar :style="getHeight">
          <el-tree
            ref="deptTree"
            :data="treeData"
            :default-expand-all="defaultExpandAll"
            :default-expanded-keys="expandedKeys"
            :expand-on-click-node="expandOnClickNode"
            :filter-node-method="filterNode"
            :highlight-current="highlightCurrent"
            node-key="id"
            :props="treeDataProps"
            :render-content="renderTreeIcon"
            @node-click="handleDeptTreeClick"
          />
        </el-scrollbar>
      </el-col>
    </el-row>
  </el-container>
</template>
<script>
  export default {
    props: {
      highlightCurrent: {
        type: Boolean,
        default: true,
      },
      defaultExpandAll:{
        type: <PERSON>olean,
        default: true,
      },
      expandOnClickNode: {
        type: Boolean,
        default: false,
      },
      treeData: {
        type: Array,
        default: () => {
          return []
        },
      },
      expandedKeys: {
        type: Array,
        default: () => {
          return []
        },
      },
      treeDataProps: {
        type: Object,
        default: () => {
          return {
            children: 'children',
            label: 'label',
          }
        },
      },
    },
    data() {
      return {
        filterText: '',
      }
    },
    computed: {
      getHeight() {
        if (this.$route.query.module) {
          return { height: 'calc(100vh - 130px)', 'margin-top': '10px' }
        } else {
          return { height: 'calc(100vh - 230px)', 'margin-top': '10px' }
        }
      },
    },
    watch: {
      filterText(val) {
        this.$refs.deptTree.filter(val)
      },
    },
    methods: {
      reset() {
        this.$refs.deptTree.setCurrentKey(null)
        this.filterText = ''
        this.expangToFalse(this.$refs.deptTree.$children)
      },
      expangToFalse(arr) {
        for (const item of arr) {
          item.node.expanded = false
          if (item.$children.length) {
            this.expangToFalse(item.$children)
          }
        }
      },
      clearSearch() {
        this.$refs.deptTree.setCurrentKey(null)
      },
      handleDeptTreeClick(node) {
        this.$emit('dept-tree-click', node)
      },
      filterNode(value, data) {
        if (!value) return true
        return data[this.treeDataProps.label].indexOf(value) !== -1
      },
      renderTreeIcon(h, { node, data, store }) {
        if ('1' == data.isXzqh) {
          if ('1' == data.isvalid) {
            return (
              <span class="custom-tree-node">
                <i class="el-icon-location-outline"></i>
                &nbsp;&nbsp;
                <span>{node.label}</span>
              </span>
            )
          } else {
            return (
              <span class="custom-tree-node">
                <i class="el-icon-location-outline"></i>
                &nbsp;&nbsp;
                <span>{node.label}</span>
              </span>
            )
          }
        } else {
          if ('1' == data.isvalid) {
            return (
              <span class="custom-tree-node">
                <i class="el-icon-news"></i>
                &nbsp;&nbsp;
                <span>{node.label}</span>
              </span>
            )
          } else {
            return (
              <span class="custom-tree-node">
                <i class="el-icon-news"></i>
                &nbsp;&nbsp;
                <span>{node.label}</span>
              </span>
            )
          }
        }
      },
    },
  }
</script>

<style scoped lang="scss">
::v-deep .el-tree-node__content {
  margin-top: 10px;
}
::v-deep .el-tree-node__expand-icon.is-leaf   {
  background-color: transparent!important;
}
/*  ::v-deep .custom-tree-node {
  z-index: 99 !important;
}*/
</style>
