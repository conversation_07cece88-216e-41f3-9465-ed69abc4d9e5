@echo off
echo Converting all files in toUtf8 folder to UTF-8...

set "target_folder=toUtf8"

if not exist "%target_folder%" (
    echo Error: Folder %target_folder% not found
    pause
    exit /b 1
)

echo Target folder: %target_folder%
echo Starting conversion...
echo.

powershell -ExecutionPolicy Bypass -Command "$folder = '%target_folder%'; $extensions = @('*.vue', '*.js', '*.ts', '*.html', '*.css', '*.scss', '*.json', '*.txt', '*.md'); $totalFiles = 0; $successFiles = 0; foreach ($ext in $extensions) { $files = Get-ChildItem -Path $folder -Filter $ext -Recurse -File -ErrorAction SilentlyContinue; foreach ($file in $files) { $totalFiles++; Write-Host \"Processing: $($file.Name)\"; try { $content = Get-Content -Path $file.FullName -Raw -Encoding Default; $utf8 = New-Object System.Text.UTF8Encoding $false; [System.IO.File]::WriteAllText($file.FullName, $content, $utf8); Write-Host \"  Success\"; $successFiles++; } catch { Write-Host \"  Failed: $($_.Exception.Message)\"; } } } Write-Host \"`nConversion Summary:\"; Write-Host \"Total files: $totalFiles\"; Write-Host \"Successfully converted: $successFiles\"; Write-Host \"Failed: $($totalFiles - $successFiles)\""

echo.
echo All files in toUtf8 folder have been processed.
echo.
pause
