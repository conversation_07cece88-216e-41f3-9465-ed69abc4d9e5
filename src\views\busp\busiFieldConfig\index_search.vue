<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <div class="search-container">
      <!--el-input placeholder="业务字段" v-model="searchForm.bfColumn"  @change="handleSearch" style="width:150px;" />&nbsp;
      <el-input placeholder="业务属性" v-model="searchForm.bfName"  @change="handleSearch" style="width:150px;" />&nbsp;-->
      <el-button icon="el-icon-save" type="primary" @click="handleAdd3">
        查询列(否)
      </el-button>
      <el-button icon="el-icon-save" type="primary" @click="handleAdd4">
        查询列(是)
      </el-button>
      &nbsp;<el-input-number v-model="bfcOrderStart" :precision="0" :step="1" :max="500" :min="0"></el-input-number>
      &nbsp;<el-input-number v-model="bfcOrder"      :precision="0" :step="1" :max="500" :min="0"></el-input-number>&nbsp;
      
      <el-dropdown @command="handleAdd6">
        <el-button type="primary">
          保存顺序<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command='-'>向前</el-dropdown-item>
          <el-dropdown-item command='+'>向后</el-dropdown-item>
          <el-dropdown-item command='0'>重置</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>&nbsp;
      <el-button icon="el-icon-save" type="primary" @click="handleAdd5">
        保存配置
      </el-button> 
      <el-button icon="el-icon-save" type="primary" @click="handleAdd10">
        重置配置
      </el-button> 
      <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
        刷新
      </el-button>
    </div>

    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'selectbtn'" #default="{ row }">
          <el-input v-model="row[item.prop]" style="width:70%;"></el-input>
          <el-button icon="el-icon-search" circle @click="selectParamDir(row)"></el-button>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
        <template v-else-if="item.editType === 'select2'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in bfcExpre"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="120"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">保存</el-button>
          <!--el-button type="text" @click="handleDele(row)" :disabled="row.bfcIsquote=='是'">删除</el-button-->
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>
    <table-edit ref="lesysParamdirsSelRef">
      <lesysParamdirsSel
        ref="lesysParamdirsSel"
        :lpdSsign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="lesysParamdirsSelClose">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="lesysParamdirsSelSave"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,busiFieldConfigDoUpdateOrd,busiFieldConfigDoReset,busiFieldConfigDoDelete } from '@/api/busp/busiFieldConfig'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import TableEdit from '@/views/common/TableEdit.vue'
  import lesysParamdirsSel from '@/views/oa/lesysParamdirs/index_sel.vue'

  export default {
    name: 'busiFieldConfigSearch',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      lesysParamdirsSel,
      TableEdit
    },
    data() {
      return {
        bfcOrder:0,
        bfcOrderStart: 0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['业务字段','业务实体','业务属性','是否查询','是否区间查询','查询方式','查询顺序','查询字典','是否引用'],
        columns: [
                { prop:'bfColumn'     , label:'业务字段', width:'auto' , sortable:false},
                { prop:'bfField'      , label:'业务实体', width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性', width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfcIsquery'   , label:'是否查询', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcIsrange'   , label:'是否区间查询', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcExpre'     , label:'查询方式', width:'auto' , sortable:false  ,editType:'select2'},
                { prop:'bfcQorder'    , label:'查询顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcDictionary', label:'查询字典', width:'auto' , sortable:false  ,editType:'selectbtn'},
                { prop:'bfcIsquote'   , label:'是否引用', width:'auto' , sortable:false}         
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          bfVersion:'0',
          sortField:'BFC_QORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'},{value:'快速查询列',label:'快速查询列'}],
        bfcExpre:[{value:'like',label:'模糊匹配'},{value:'=',label:'等于'},
                  {value:'in',label:'包含'},{value:'!=',label:'不等于'},
                  {value:'>=',label:'大于等于'},{value:'>',label:'大于'},
                  {value:'<=',label:'小于等于'},{value:'<',label:'小于'},
                  {value:'not in',label:'不包含'}],
        busiModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfVersion = row.bmVersion
        this.searchForm.bfcBmId = row.bmId
        this.searchForm.bfcBcId = row.bfcBcId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      selectParamDir(row){
        this.$refs['lesysParamdirsSelRef'].showEdit('选择字典')
      },
      lesysParamdirsSelClose(){
        this.$refs.lesysParamdirsSelRef.close()
      },
      lesysParamdirsSelSave(){
        var row = this.$refs.lesysParamdirsSel.getSel()
        this.row.bfcDictionary = "{\"name\":\""+row.lpdName+"\",\"type\":\"url\",\"data\":\""+row.lpdId+"\"}";
        this.$refs.lesysParamdirsSelRef.close()
      },
      handleAdd3(){
        this.handleAdd('configIslist0')
      },
      handleAdd4(){
        this.handleAdd('configIslist1')
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        var slist = []
        slist.push({bfcIsquery:row.bfcIsquery,
                        bfcQorder:row.bfcQorder,
                        bfcDictionary:row.bfcDictionary,
                        bfName:row.bfName,
                        bfcIsrange:row.bfcIsrange,
                        bfcId:row.bfcId,
                        bfcExpre:row.bfcExpre,
                        bfcBfId:row.bfcBfId})
        const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
        if(msg.code == 200) {
          this.$message({message:'保存操作成功!',type:'success'})
        }else{
          this.$message({message:'保存操作失败!',type:'warning'})
        }
      },
      handleDele(row){
        if(row.bfcIsquote=='否'){
          this.$message({message:'非引用模型属性配置，无法删除!',type:'warning'})
        }else{
          this.$baseConfirm('确定删除引用的属性配置吗', null, async () => {
            const  msg  = await busiFieldConfigDoDelete( {'bfcId':row.bfcId} )
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
      },
      handleAdd10(){
        this.$baseConfirm('重置将清除所有配置信息，确定吗', null, async () => {
          const msg = await busiFieldConfigDoReset({bfBmId:this.busiModelRow.bmId,bfcBcId:this.busiModelRow.bfcBcId})
          if(msg.code == 200) {
            this.$message({message:'操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:'操作失败!',type:'warning'})
          }
        })
      },
      handleAdd6(command){
        if(command=='+'){
          this.$baseConfirm('确定将查询顺序向后'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_QORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='-'){
          this.$baseConfirm('确定将查询顺序向前'+this.bfcOrder+'吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_QORDER',bfcPlus:command,bfcPlusVal:this.bfcOrder,bfcOrderStart:this.bfcOrderStart,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        }
        else if(command=='0'){
          this.$baseConfirm('确定重置查询顺序吗', null, async () => {
            const msg = await busiFieldConfigDoUpdateOrd({bfBmId:this.busiModelRow.bmId,bfcColumnName:'BFC_QORDER',bfcPlus:command,bfcPlusVal:0,bfcBcId:this.busiModelRow.bfcBcId})
            if(msg.code == 200) {
              this.$message({message:'操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'操作失败!',type:'warning'})
            }
          })
        } 
      },
      async handleAdd(command) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIsquery:this.list[i].bfcIsquery,
                        bfcIsrange:this.list[i].bfcIsrange,
                        bfcQorder:this.list[i].bfcQorder,
                        bfcExpre:this.list[i].bfcExpre,
                        bfcDictionary:this.list[i].bfcDictionary,
                        bfName:this.list[i].bfName,
                        bfcId:this.list[i].bfcId,
                        bfcBfId:this.list[i].bfcBfId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist0'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIsquery:'否',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist1'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIsquery:'是',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>