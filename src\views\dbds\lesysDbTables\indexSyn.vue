<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <div class="search-container">
      <vab-query-form>
        <vab-query-form-left-panel>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleSyn"
            >
              同步表
            </el-button>
          </vab-query-form-left-panel>
      </vab-query-form>
    </div>

    <el-table
      ref="lesysDbTablesTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="lesysDbTables"
      row-key="TABLE_NAME"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
      type="selection"
      width="55">
      </el-table-column>

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import { vPageDsTables,syncTablesByName } from '@/api/dbds/lesysDbDs'
  import lesysDbTablesSearchSimple from './components/searchSimple.vue'
  import { Loading } from 'element-ui'
  export default {
    name: 'lesysDbTablesSyn',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      lesysDbTablesSearchSimple
    },
    data() {
      return {
        fullscreenLoading: false,
        queryFormDf: {},
        queryForm: {},
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['表名','表注释'],
        columns: [
                { prop:'TABLE_NAME'    , label:'表名'  , width:'auto' , sortable:false  },
                { prop:'TABLE_COMMENTS', label:'表注释', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        lesysDbDsRow:{},
        multipleSelection:[]
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {

    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      init(ldr){
        this.lesysDbDsRow = ldr
        this.searchForm.ldId = ldr.ldId
        this.fetchData()
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      //同步表列
      async handleSyn() {
        if(this.multipleSelection.length==0){
          this.$message({message:'请勾选同步的表!',type:'warning'})
        }else{
          let param = {ldId:this.lesysDbDsRow.ldId,entityList:this.multipleSelection}
          const msg = await syncTablesByName(param)
          if(msg.code == 200) {
            this.$message({message:'同步成功!',type:'success'})
          }else{
            this.$message({message:'同步失败!',type:'warning'})
          }
        }  
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await vPageDsTables(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>