{"name": "admin-pro", "version": "2.5.0", "private": true, "author": "chuz<PERSON><PERSON>", "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --report --dest admin", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "build:report": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --report", "build:deploy": "start ./deploy.sh", "build:docker": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build&&docker build --pull --rm -f \"dockerfile\" -t vueadminbeautifulpro:latest \".\"&&docker run --rm -d  -p 80:80/tcp vueadminbeautifulpro:latest", "global:install": "npm install -g nrm,cnpm,npm-check-updates", "globle:update": "ncu -g", "module:install": "npm i  --registry=http://mirrors.cloud.tencent.com/npm/", "module:update": "ncu -u --reject  chalk,@logicflow/core,@logicflow/extension,screenfull,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack,vue,vuex,vue-router,@vue/cli-plugin-babel,@vue/cli-plugin-eslint,@vue/cli-plugin-pwa,@vue/cli-plugin-router,@vue/cli-plugin-vuex,@vue/cli-service,plop,vue-eslint-parser,eslint-plugin-vue,vue-i18n,vab-player,xlsx  --registry http://mirrors.cloud.tencent.com/npm/&&npm run module:install", "module:reinstall": "rimraf node_modules&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao"}, "dependencies": {"@antv/g6": "^4.8.21", "@logicflow/core": "^1.0.2", "@logicflow/extension": "^1.0.2", "@vxe-ui/plugin-export-xlsx": "^3.0.12", "axios": "^1.4.0", "clipboard": "^2.0.11", "core-js": "^3.27.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "echarts": "^5.4.1", "element-ui": "2.15.12", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "jquery": "^3.7.0", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.1", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "postcss-import": "^11.0.0", "postcss-pxtorem": "^5.1.1", "qs": "^6.11.0", "register-service-worker": "^1.7.2", "resize-detector": "^0.3.0", "sass": "1.32.13", "screenfull": "5.2.0", "swiper": "^5.4.5", "uuid": "^9.0.0", "vab-icons": "file:vab-icons", "vue": "^2.6.11", "vue-i18n": "^8.26.8", "vue-json-viewer": "^2.2.22", "vue-pdf": "^4.3.0", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "vxe-table": "^3.12.16", "xlsx": "^0.17.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-pwa": "^4.5.15", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "^1.20.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "compression-webpack-plugin": "6.1.1", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "^8.2.0", "filemanager-webpack-plugin": "3.1.1", "image-webpack-loader": "^8.1.0", "lint-staged": "^13.1.0", "postcss": "^8.4.20", "postcss-html": "^1.5.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.6", "postcss-syntax": "^0.36.2", "prettier": "^2.8.1", "raw-loader": "^4.0.2", "sass": "1.32.13", "sass-loader": "10.2.0", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recess-order": "^3.1.0", "svg-sprite-loader": "^6.0.11", "video.js": "^8.5.2", "vue-eslint-parser": "^8.0.1", "webpack": "4.46.0", "webpackbar": "^5.0.2"}, "homepage": "https://chu1204505056.gitee.io/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx}": ["vue-cli-service lint", "git add"]}, "participants": ["LiufengFish"], "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-pro.git"}}