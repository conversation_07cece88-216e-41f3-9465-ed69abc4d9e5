import request from '@/utils/request'

//列表分页加载数据
export function busiModelGetList(params) {
  return request({
    url: '/busi-model/vPage',
    method: 'get',
    params,
  })
}

export function busiModelGetData(params) {
  return request({
    url: '/busi-model/vList',
    method: 'get',
    params,
  })
}

export function loadModelToRedis(data) {
  return request({
    url: '/busi-model/loadingModel',
    method: 'post',
    data,
  })
}

export function busiModelCreateTable(data) {
  return request({
    url: '/busi-model/createTable',
    method: 'post',
    data,
  })
}

export function busiModelCreateTableOth(data) {
  return request({
    url: '/busi-model/createTableOth',
    method: 'post',
    data,
  })
}

//保存新增,不记录日志
export function busiModelDoSave(data) {
  return request({
    url: '/busi-model/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiModelDoUpdate(data) {
  return request({
    url: '/busi-model/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function busiModelDoSaveOrUpd(data) {
  return request({
    url: '/busi-model/saveOrUpd',
    method: 'post',
    data,
  })
}
export function busiModelDoCopy(data) {
  return request({
    url: '/busi-model/copy',
    method: 'post',
    data,
  })
}
export function busiModelDoDef(data){
  return request({
    url: '/busi-model/defc',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function busiModelDoSaveOrUpdLog(data) {
  return request({
    url: '/busi-model/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiModelDoDelete(data) {
  return request({
    url: '/busi-model/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function busiModelDoDeleteLog(data) {
  return request({
    url: '/busi-model/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function busiModelDoDeleteELog(data) {
  return request({
    url: '/busi-model/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function busiModelGetTreeList(params){
  return request({
    url: '/busi-model/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function busiModelDoExport(data) {
  return request({
    url: '/busi-model/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function busiModelGetStat(params) {
  return request({
    url: '/busi-model/vStat',
    method: 'get',
    params,
  })
}
