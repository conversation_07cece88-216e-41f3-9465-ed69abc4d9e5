<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
        >
          <el-form-item>
            <el-button icon="el-icon-save" type="primary" @click="handleAdd3">
              列表列(否)
            </el-button>
            <el-button icon="el-icon-save" type="primary" @click="handleAdd4">
              列表列(是)
            </el-button>
            <el-button icon="el-icon-save" type="primary" @click="handleAdd5">
              保存配置
            </el-button> 
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
    </vab-query-form>

    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates } from '@/api/busp/busiFieldConfig'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiFieldConfigSearch from './components/Search.vue'
  import busiFieldConfigForm from './components/Form.vue'
  import busiFieldConfigQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'busiFieldConfigManage',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiFieldConfigSearch,
      busiFieldConfigForm,
      busiFieldConfigQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['业务属性名','是否列表列','列表列顺序','是否编辑列','编辑列顺序','列最大长度','列是否必填','编辑列字典','是否查询列','查询列顺序'],
        columns: [
                { prop:'bfName'       , label:'业务属性名', width:'auto' , sortable:false  },
                { prop:'bfcIslist'    , label:'是否列表列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcLorder'    , label:'列表列顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcIsedit'    , label:'是否编辑列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcOrder'     , label:'编辑列顺序', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcMaxl'      , label:'列最大长度', width:'auto' , sortable:false  ,editType:'number'},
                { prop:'bfcRequire'   , label:'列是否必填', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcDictionary', label:'编辑列字典', width:'auto' , sortable:false  },
                { prop:'bfcIsquery'   , label:'是否查询列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcQorder'    , label:'查询列顺序', width:'auto' , sortable:false  ,editType:'number'}        
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          sortField:'BFC_LORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'}],
        busiModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfcBmId = row.bmId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      handleAdd3(){
        this.handleAdd('configIslist0')
      },
      handleAdd4(){
        this.handleAdd('configIslist1')
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      async handleAdd(command) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:this.list[i].bfcIslist,
                        bfcLorder:this.list[i].bfcLorder,
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist0'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:'否',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
        else if(command == 'configIslist1'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:'是',
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>