<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model="key"
              placeholder="快速搜索"
              @keyup.enter.native="handleFind"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-folder-opened"
              type="primary"
              @click="expandAllTreeEvent"
            >
              展开
            </el-button>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-folder"
              type="primary"
              @click="claseExpandTreeEvent"
            >
              收缩
            </el-button>
          </el-form-item>

        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>

      </vab-query-form-right-panel>
    </vab-query-form>

    <vxe-table
      class="mylist-table"
      v-loading="listLoading"
      ref="deptTable"
      id="dept"
      border
      stripe
      show-overflow="tooltip"
      :height="height"
      :data="list"
      size="small"
      :scroll-y="{enabled: false, gt: 300}"
      @current-change="currentSelectRow"
      :export-config="exportConfig"
      :tree-config="treeConfig"
      :row-config="{ useKey: true,keyField:'odId',isCurrent:true,isHover:true}"
    >
      <vxe-column field="seq" type="seq" width="70" align="center" fixed="left"></vxe-column>
      <vxe-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :title="item.label"
        :field="item.prop"
        :tree-node="item.node"
        :align="item.align"
        v-bind="{ ...item }"
        header-align="center"
        minWidth="150"
      >
      </vxe-column>

      <vxe-column
        align="center"
        title="操作"
        fixed="right"
        show-overflow-tooltip
        width="150"
        field="oper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleExpend(row)" icon="el-icon-folder-opened">展开/收缩</el-button>
        </template>
      </vxe-column>

      <template #empty>
        <el-image
          style="height: 110px"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </vxe-table>

  </div>
</template>

<script>
  import { getList} from '@/api/data/lesysDeptVue'
  import { exportRearTmplVxe } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  
  export default {
    name: 'task2',
    props: {
      gheight: {
        type: Number,
      }
    },
    components: {
      
    },
    data() {
      let searchForm = {
          
      };
      const seqConfig = {
        seqMethod ({ rowIndex }) {
          return `${rowIndex + 1}`
        }
      };
      const exportConfig = {
        sheetMethod(params) {
          const { worksheet } = params;
          worksheet.eachRow(excelRow => {
              excelRow.height = 40;
              excelRow.eachCell(excelCell => {
                  // 设置单元格边框
                  excelCell.border = {
                      top: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      left: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      bottom: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      right: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      }
                  };
              });
          });
        }
      };

      return {
        fullscreenLoading: false,
        form: {},
        formConfig: {
          labelPosition: 'right',
          labelWidth: '80px',
          size: 'small',
        },
        title: '',
        isFullscreen: false,
        height: !this.gheight ? this.$baseTableHeight(1, 0) : this.gheight,
        list: [],
        tableData: [],
        listLoading: true,
        row: '0',
        searchForm,
        rules: {},
        checkList: ['简称','全称','编码','类型','状态','顺序'],
        columns: [
          { prop:"odName"     , label:"简称"    , sortable:false  ,align:'left'   ,width:'200px',node:'tree-node'},
          { prop:'odFullname' , label:'全称'    , sortable:false  ,align:'center' ,width:'250px'},
          { prop:'odCode'     , label:'编码'    , sortable:false  ,align:'center' },
          { prop:'odType'     , label:'类型'    , sortable:false  ,align:'center' },
          { prop:'odStatus'   , label:'状态'    , sortable:false  ,align:'center' },
          { prop:'odOrder'    , label:'顺序'    , sortable:false  ,align:'center' }       
        ],
        seqConfig,
        exportConfig,
        treeConfig:{
          transform: true,
          rowField: 'odId',
          parentField: 'odPid',
          seqMode: 'increasing',
          expandRowKeys: [],
          showLine: true,
          iconOpen: 'vxe-icon-square-minus',
          iconClose: 'vxe-icon-square-plus'
        },
        key:'',
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      expandAllTreeEvent(){
        const $table = this.$refs.deptTable
        if ($table) {
          $table.setAllTreeExpand(true)
        }
      },
      claseExpandTreeEvent(){
        const $table = this.$refs.deptTable
        if ($table) {
          $table.clearTreeExpand()
        }
      },
      handleExpend(row){
        const $table = this.$refs.deptTable
        if($table.isTreeExpandByRow(row)){
          $table.setTreeExpand(row, false)
        }else{
          $table.setTreeExpand(row, true)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val.row
      },
      // 快速查询
      handleFind() {
        let searchProps = []
        this.columns.forEach(key => {
          searchProps.push(key['prop'])
        })
        const filterVal = String(this.key).trim()
        if (filterVal) {
          const filterRE = new RegExp(filterVal, 'gi')
          const rest = this.tableData.filter(item => searchProps.some(key => String(item[key]).indexOf(filterVal) > -1))
          this.list = rest.map(row => {
            // 搜索为克隆数据，不会污染源数据
            const item = Object.assign({}, row)
            searchProps.forEach(key => {
              item[key] = String(item[key]).replace("null","")
            })
            return item
          })
        } else {
          this.list = this.tableData
        }
      }, 
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await getList(this.searchForm)
        this.tableData = data.data
        this.list = data.data

        if(this.list.length>0){
          this.treeConfig.expandRowKeys = [this.list[0].odId]
        }

        this.listLoading = false
      }
    },
  }
</script>