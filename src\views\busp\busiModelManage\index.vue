<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiModelSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="busiModelTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="busiModelTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiModel"
      row-key="bmId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="80"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleSet(row)">业务属性</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="busiModelEdit">
      <busiModelForm
        ref="busiModelForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="busiModelQuerySearch">
      <busiModelQuery
        ref="busiModelQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="busiFieldSel" :fullscreen="true">
      <busiFieldManage
        ref="busiFieldManage"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigManageListSel" :fullscreen="true">
      <busiFieldConfigManageList
        ref="busiFieldConfigManageList"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigManageListclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigManageFormSel" :fullscreen="true">
      <busiFieldConfigManageForm
        ref="busiFieldConfigManageForm"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigManageFormclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="busiFieldConfigManageSearchSel" :fullscreen="true">
      <busiFieldConfigManageSearch
        ref="busiFieldConfigManageSearch"
        :bmSign="bmSign"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="busiFieldConfigManageSearchclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

  </div>
</template>

<script>
  import { busiModelGetList} from '@/api/busp/busiModel'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiModelSearch from './components/Search.vue'
  import busiModelForm from './components/Form.vue'
  import busiModelQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import busiFieldManage from '@/views/busp/busiFieldManage/index.vue'
  import busiFieldConfigManageList from '@/views/busp/busiFieldConfigManage/index_list.vue'
  import busiFieldConfigManageForm from '@/views/busp/busiFieldConfigManage/index_form.vue'
  import busiFieldConfigManageSearch from '@/views/busp/busiFieldConfigManage/index_search.vue'
  export default {
    name: 'busiModelManage',
    props: {
      gheight: {
        type:Number
      },
      bmSign: {
        type:String
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiModelSearch,
      busiModelForm,
      busiModelQuery,
      busiFieldManage,
      busiFieldConfigManageList,
      busiFieldConfigManageForm,
      busiFieldConfigManageSearch
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {

        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','描述','标识','实体名','表名','状态'],
        columns: [
                { prop:'bmName', label:'名称', width:'auto' , sortable:false  },
                { prop:'bmDesc', label:'描述', width:'auto' , sortable:false  },
                { prop:'bmSign', label:'标识', width:'auto' , sortable:false  },
                { prop:'bmEntity', label:'实体名', width:'auto' , sortable:false  },
                { prop:'bmTable' , label:'表名'  , width:'auto' , sortable:false  },
                { prop:'bmStatus', label:'状态', width:'auto' , sortable:false  }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bmStatus:'有效',
          bmSign:this.bmSign,
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        
      },
      // 弹窗编辑取消按钮
      close() {
        
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd(command) {
        if(command == 'field'){
          this.handleSet(this.row)
        }
        else if(command == 'listset'){
          this.$refs['busiFieldConfigManageListSel'].showEdit('业务显示列配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigManageList.init(this.row)
          })
        }
        else if(command == 'formset'){
          this.$refs['busiFieldConfigManageFormSel'].showEdit('业务编辑列配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigManageForm.init(this.row)
          })
        }
        else if(command == 'searchs'){
          this.$refs['busiFieldConfigManageSearchSel'].showEdit('业务查询列配置')
          this.$nextTick(()=> {
            this.$refs.busiFieldConfigManageSearch.init(this.row)
          })
        }
      },
      handleSet(row) {
        this.$refs['busiFieldSel'].showEdit('业务属性')
        this.$nextTick(()=> {
          this.$refs.busiFieldManage.init(row)
        })
      },
      busiFieldclose(){
        this.$refs.busiFieldSel.close()
      },
      busiFieldConfigManageListclose(){
        this.$refs.busiFieldConfigManageListSel.close()
      },
      busiFieldConfigManageFormclose(){
        this.$refs.busiFieldConfigManageFormSel.close()
      },
      busiFieldConfigManageSearchclose(){
        this.$refs.busiFieldConfigManageSearchSel.close()
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleSet(row)
      },
      // 编辑行数据
      handleEdit(row) {
        
      },
      // 删除行数据
      handleDelete(row) {
        
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        
      },
      //高级查询关闭
      queryClose(){
        
      },
      //高级查询清空
      queryClear(){
        
      },
      //高级查询
      querySure(){
        
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await busiModelGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
      },
      //后端导出模板
      async handleExportTmpl(){
      },
      // excel导入
      handleImportRear(){
      }
    },
  }
</script>