<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <mainData mainName="Q0VUQ1RBU0tT" mainTitle="任务"></mainData>
  </div>
</template>

<script>
  import mainData from '@/views/data/data/index.vue'
  export default {
    name: 'task',
    props: {
      
    },
    components: {
      mainData
    },
    
    data() {
      return {
        isFullscreen: false
      }
    },
    created() {
      
    },
    methods: {

    },
  }
</script>
