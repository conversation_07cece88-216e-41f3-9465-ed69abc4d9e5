import request from '@/utils/request'

export function zlbhDataDoSave(data) {
  return request({
    url: '/data-zlbh/save',
    method: 'post',
    data,
  })
}

export function mainDataDoSave(data) {
  return request({
    url: '/data/saveOrUpd',
    method: 'post',
    data,
  })
}

export function mainDataDoDelete(data) {
  return request({
    url: '/data/delete',
    method: 'post',
    data,
  })
}

export function mainDataGetPageList(params) {
  return request({
    url: '/data/vPageQuery',
    method: 'get',
    params,
  })
}
//根据模型列类型匹配查询条件-分页
export function mainDataPostPageList(data) {
  return request({
    url: '/data/vPostPageQuery',
    method: 'post',
    data,
  })
}
//根据模型列类型匹配查询条件-不分页
export function mainDataPostList(data) {
  return request({
    url: '/data/vListQuery',
    method: 'post',
    data,
  })
}
//根据模型配置匹配查询条件-分页
export function mainDataPostPageList1(data) {
  return request({
    url: '/data/vPostPageQueryExpre',
    method: 'post',
    data,
  })
}
//根据模型配置匹配查询条件-不分页
export function mainDataPostList1(data) {
  return request({
    url: '/data/vListQueryExpre',
    method: 'post',
    data,
  })
}

export function mainDataPostPageExport(data) {
  return request({
    url: '/data/vPostPageExport',
    method: 'post',
    data,
  })
}

export function mainDataExport(data) {
  return request({
    url: '/data-export/vExport',
    method: 'post',
    data,
  })
}

export function mainDataExportExpre(data) {
  return request({
    url: '/data-export/vExportExpre',
    method: 'post',
    data,
  })
}

export function mainDataDown(data) {
  return request({
    url: '/data-export/downloadFile',
    method: 'post',
    data,
  })
}

export function mainDataPostImport(data) {
  return request({
    url: '/data-import/vPostExecImport1',
    method: 'post',
    data,
  })
}

export function mainDataPostPus(data) {
  return request({
    url: '/data-import/vPostDataPus',
    method: 'post',
    data,
  })
}

export function mainDataPostRepeat(data) {
  return request({
    url: '/data-import/vPostExecRepeat',
    method: 'post',
    data,
  })
}
