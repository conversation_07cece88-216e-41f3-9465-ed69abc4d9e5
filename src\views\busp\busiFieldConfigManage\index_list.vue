<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  > 
  <div class="search-container">
    <el-input placeholder="业务属性" v-model="searchForm.bfName"  @change="handleSearch" style="width:150px;" />&nbsp;
    
    <el-button icon="el-icon-save" type="success" @click="handleAdd5">
      保存配置
    </el-button> 
    <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
      刷新
    </el-button>
  </div>
    <el-table
      ref="busiFieldConfigTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiFieldConfig"
      row-key="bfcId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template v-if="!item.editType" #default="{ row }">
          {{ row[item.prop] }}
        </template>
        <template v-else-if="item.editType === 'input'" #default="{ row }">
          <el-input v-model="row[item.prop]"></el-input>
        </template>
        <template v-else-if="item.editType === 'number'" #default="{ row }">
          <el-input-number v-model="row[item.prop]" :precision="0" :min="0" :max="2000"></el-input-number>
        </template>
        <template v-else-if="item.editType === 'select'"  #default="{ row }">
          <el-select v-model="row[item.prop]" placeholder="请选择">
            <el-option
                v-for="item in tOf"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="120"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">保存</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

  </div>
</template>

<script>
  import { busiFieldConfigGetList,
           busiFieldConfigDoUpdates,
           busiFieldConfigDoUpdateOrd,
           busiFieldConfigDoReset,
           busiFieldConfigDoDelete } from '@/api/busp/busiFieldConfig'
  
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import TableEdit from '@/views/common/TableEdit.vue'
  export default {
    name: 'busiFieldConfigManageList',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit
    },
    data() {
      return {
        bfcOrder:0,
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['属性名','业务属性名','使用状态','是否列表列','列表列顺序'],
        columns: [
                { prop:'bfField'      , label:'属性名', width:'auto' , sortable:false},
                { prop:'bfName'       , label:'业务属性名', width:'auto' , sortable:false  ,editType:'input'},
                { prop:'bfUseState'   , label:'使用状态'    , width:'auto' , sortable:false  },
                { prop:'bfcIslist'    , label:'是否列表列', width:'auto' , sortable:false  ,editType:'select'},
                { prop:'bfcLorder'    , label:'列表列顺序', width:'auto' , sortable:false  ,editType:'number'}      
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfcBmId:'',
          sortField:'BFC_LORDER',
          sortOrder:'ASC'
        },
        tOf:[{value:'是',label:'是'},{value:'否',label:'否'}],
        busiModelRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfcBmId = row.bmId
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldConfigGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      },
      handleAdd5(){
        this.handleAdd('saveTitle')
      },
      
      handleSearch(){
        this.fetchData()
      },
      async handleEdit(row){
        var slist = []
        slist.push({bfcIslist:row.bfcIslist,
                      bfcLorder:row.bfcLorder,
                      bfName:row.bfName,
                      bfcId:row.bfcId})
        const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
        if(msg.code == 200) {
          this.$message({message:'保存操作成功!',type:'success'})
        }else{
          this.$message({message:'保存操作失败!',type:'warning'})
        }
      },
      async handleAdd(command) {
        if(command=='saveTitle'){
          var slist = []
          for(var i=0;i<this.list.length;i++){
            slist.push({bfcIslist:this.list[i].bfcIslist,
                        bfcLorder:this.list[i].bfcLorder,
                        bfName:this.list[i].bfName,
                        bfcId:this.list[i].bfcId})
          }
          const  msg  = await busiFieldConfigDoUpdates( {busiFieldConfigList:slist} )
          if(msg.code == 200) {
            this.$message({message:'保存操作成功!',type:'success'})
            this.fetchData()
          }else{
            this.$message({message:'保存操作失败!',type:'warning'})
          }
        }
      }
    },
  }
</script>