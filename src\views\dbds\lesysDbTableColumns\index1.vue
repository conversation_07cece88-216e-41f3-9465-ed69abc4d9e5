<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-row :gutter="20">
      <el-col :span="10" >
        <lesysDbTablesSimple  ref="lesysDbTablesSimple" @lesysDbTablesSimpleSel="lesysDbTablesSimpleSel">
          
        </lesysDbTablesSimple>
      </el-col>
      <el-col :span="14">
        <lesysDbTableColumns ref="lesysDbTableColumns">
          
        </lesysDbTableColumns>
      </el-col>
    </el-row>  
  </div>
</template>

<script>
  import lesysDbTablesSimple from '@/views/dbds/lesysDbTables/indexSimple.vue'
  import lesysDbTableColumns from './index.vue'

  export default {
    name: 'lesysDbTableColumns1',
    components: {
      lesysDbTablesSimple,
      lesysDbTableColumns
    },
    data() {
      return {
        isFullscreen:false
      }
    },
    computed: {

    },
    created() {
      
    },
    methods: {
      init(lesysDatasources){
        this.$refs.lesysDbTablesSimple.loadTable(lesysDatasources)
        this.$refs.lesysDbTableColumns.loadSchema(lesysDatasources,null)
      },
      lesysDbTablesSimpleSel(lesysDatasources,lesysDatasourceTable){
        this.$refs.lesysDbTableColumns.loadSchema(lesysDatasources,lesysDatasourceTable)
      }
      
    }
  }
</script>