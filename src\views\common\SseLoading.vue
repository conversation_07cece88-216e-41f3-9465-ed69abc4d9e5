<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-progress :text-inside="true" :stroke-width="24" :percentage="percentage" status="success"></el-progress>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import { lesysCloseSse } from '@/utils/lesysSse' 
  import { baseURL } from '@/config'
  export default {
    name: 'sseLoading',
    props: {
      uid:{
        type:String,
        default:'sse1234567890'
      }
    },
    data() {
      return {
        percentage:0
      }
    },
    created() {
      this.sseTest()
    },
    methods: {
      sseTest(){
        let that = this
        let eventSource = new EventSource(baseURL+'/Sse/createSse/'+this.uid,{ withCredentials: true,openWhenHidden:true });
        eventSource.onopen = function (event) {
          //console.log('SSE链接成功');
        }
        eventSource.onmessage = function (event) {
            if(event.data){
                //console.log('后端返回的数据:', event.data);
                if(event.data!='连接成功'){
                  let data = JSON.parse(event.data);
                  that.setPec(data.pec)
                  if(data.status=='finished'){
                    that.closeSse()
                    eventSource.close()
                    that.$emit("handleFinish",data.data)
                  }
                }
            }
        }
        eventSource.onerror = (error) => {
            //console.log('SSE链接失败');
        };  
      },
      setPec(pec){
        this.percentage = pec
      },
      closeSse(){
        lesysCloseSse({id:this.uid})
      }
    },
    watch: {
      form(newVal) {
        
      }
    }
  }
</script>