<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="12">
          <el-form-item label="模型ID" prop="bfBmId">
            <el-input v-model.trim="form.bfBmId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表名" prop="bfBmTable">
            <el-input v-model.trim="form.bfBmTable" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段名" prop="bfColumn">
            <el-input v-model.trim="form.bfColumn" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段类型" prop="bfColumnType">
            <el-input v-model.trim="form.bfColumnType" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否有效" prop="bfEffState">
            <el-input v-model.trim="form.bfEffState" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性名" prop="bfField">
            <el-input v-model.trim="form.bfField" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性类型" prop="bfFieldType">
            <el-input v-model.trim="form.bfFieldType" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否接口列" prop="bfInterface">
            <el-input v-model.trim="form.bfInterface" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否主键" prop="bfIspk">
            <el-input v-model.trim="form.bfIspk" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性中文" prop="bfName">
            <el-input v-model.trim="form.bfName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性顺序" prop="bfOrder">
            <el-input-number v-model.trim="form.bfOrderStart" clearable></el-input-number> - <el-input-number v-model.trim="form.bfOrderEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统计/维度/其他" prop="bfSort">
            <el-input v-model.trim="form.bfSort" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示列名" prop="bfTitle">
            <el-input v-model.trim="form.bfTitle" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="属性类型" prop="bfType">
            <el-input v-model.trim="form.bfType" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="bfUseState">
            <el-input v-model.trim="form.bfUseState" clearable ></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'busiFieldQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>