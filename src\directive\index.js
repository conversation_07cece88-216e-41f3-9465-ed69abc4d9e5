import { createFilterIcons, clickhandler } from './tableFilter/index'
export default {
  install(Vue) {
    /**
     * 防抖 单位时间只触发最后一次
     * 调用时按顺序传入一个数组[fn,event,delay]
     * @param fn 执行的函数（必传）
     * @param event 事件类型（默认click）
     * @param delay 间隔时间(默认300ms)
     * */
    Vue.directive('debounce', {
      inserted: (el, binding) => {
        let [fn, event = 'click', delay = 300] = binding.value
        let timer
        el.addEventListener(event, () => {
          if (timer) {
            clearTimeout(timer)
          }
          timer = setTimeout(() => {
            fn()
          }, delay)
        })
      },
    })
    /**
     * 节流 每单位时间可触发一次
     * 调用时按顺序传入一个数组[fn,event,delay]
     * @param fn 执行的函数（必传）
     * @param event 事件类型（默认click）
     * @param delay 间隔时间(默认300ms)
     * */
    Vue.directive('throttle', {
      inserted: (el, binding) => {
        let [fn, event = 'click', delay = 300] = binding.value
        let timer = null
        el.addEventListener(event, () => {
          if (!timer) {
            timer = setTimeout(() => {
              fn()
              timer = null
            }, delay)
          }
        })
      },
    })

    /**
     * 表头筛选
     * 调用时按顺序传入一个数组[fn,event,delay]
     * @param fn 执行的函数（必传）
     * @param event 事件类型（默认click）
     * @param delay 间隔时间(默认300ms)
     * */
    Vue.directive('tableFilter', {
      bind(el, binding, vnode) {
        Vue.nextTick(() => {
          // 考虑配合低代码生成，不传参设置默认值。
          const {
            fn = 'search',
            ref = 'table',
            tableList = 'tableData',
            filterParmas = 'filterParmas',
            getDirectiveParmas = 'getDirectiveParmas',
            filterColumns = [],
          } = binding.value

          const cache = {}
          const vnodeCache = {}
          const columnDom = {}
          let handle
          const vmCache = {
            cache,
            vnodeCache,
            columnDom,
            handle,
          }
          const filterLabels = filterColumns
            .filter((item) => item.filter)
            .map((item) => item.label)
          const headerWrapper = el.querySelector('.el-table__header-wrapper')
          const headerRow = headerWrapper.querySelectorAll('.el-table__cell')
          const arr = ['序号', '操作']
          for (let i = 0; i < headerRow.length; i++) {
            // 要首先把序号，操作栏，可能会出现的滚动条排除
            const classList = [...headerRow[i].classList]
            if (classList.includes('gutter')) continue
            if (
              arr.includes(
                headerRow[i]
                  .querySelector('.cell')
                  ?.childNodes[0].nodeValue.trim()
              )
            )
              continue
            if (
              !filterLabels.includes(
                headerRow[i]
                  .querySelector('.cell')
                  ?.childNodes[0].nodeValue.trim()
              )
            )
              continue
            const divDom = createFilterIcons(i)
            vnodeCache[`vnode${i}`] = vnode.componentInstance.$children[i]
            headerRow[i].querySelector('.cell').style.display = 'flex'
            headerRow[i].querySelector('.cell').appendChild(divDom)
            columnDom[`vnode${i}`] = headerRow[i]
          }
          const clickHandler = function (event) {
            clickhandler(
              event,
              cache,
              vnodeCache,
              columnDom,
              vnode,
              fn,
              ref,
              tableList,
              filterParmas,
              getDirectiveParmas
            )
          }.bind(this)
          vnode.componentInstance.$on('hook:activated', () => {
            // 在组件的mounted钩子函数被调用时执行指令中的逻辑
            const { cache, vnodeCache, columnDom } = vmCache
            document.removeEventListener('click', clickHandler)
            document.addEventListener('click', (event) =>
              clickhandler(
                event,
                cache,
                vnodeCache,
                columnDom,
                vnode,
                fn,
                ref,
                tableList,
                filterParmas,
                getDirectiveParmas
              )
            )

            // 执行其他逻辑...
          })
          document.addEventListener('click', clickHandler)

          el._clickhandler = clickHandler
          vmCache.handle = clickHandler
        })
      },

      unbind(el) {
        document.removeEventListener('click', el._clickhandler)
      },
    })

    Vue.directive('ylRuntimeTemplate', {
      bind(el, binding) {
        console.log('binding', binding)
        const { dom } = binding.value
        var InsertComponent = Vue.extend({
          template: '<div>' + dom + '</div>',
        })
        el.appendChild(new InsertComponent().$mount().$el)
      },
      update(el, binding) {
        const { dom } = binding.value
        var InsertComponent = Vue.extend({
          template: '<div>' + dom + '</div>',
        })
        el.innerHTML = ''
        el.appendChild(new InsertComponent().$mount().$el)
      },
    })
  },
}
