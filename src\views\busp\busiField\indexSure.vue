<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-table
      ref="busiFieldTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      id="BusiField"
      row-key="bfId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

  </div>
</template>

<script>
  import { busiFieldGetList } from '@/api/busp/busiField'

  export default {
    name: 'busiFieldSure',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {

    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['属性名','属性实体','属性顺序','属性版本'],
        columns: [
                { prop:'bfName'     , label:'属性名'      , width:'auto' , sortable:false  },
                { prop:'bfField'    , label:'属性实体'    , width:'auto' , sortable:false  },
                { prop:'bfOrder'    , label:'属性顺序'    , width:'auto' , sortable:false  },
                { prop:'bfVersion'  , label:'属性版本'    , width:'auto' , sortable:false  }       
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfBmId:'',
          bfName:'',
          bfVersion:'',
          bfLevel:'',
          sortField:'',
          sortOrder:''
        },
        busiModelRow:{}
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row){
        this.busiModelRow = row
        this.searchForm.bfBmId = row.bmId
        this.searchForm.bfLevel= '业务属性'
        this.fetchData()
      }, 
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldGetList(this.searchForm)
        this.list = data.data
        this.listLoading = false
      }
    },
  }
</script>