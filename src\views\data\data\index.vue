<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model="key"
              placeholder="快速搜索"
              @keyup.enter.native="handleFind"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd('add')"
            >
              添加
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-dropdown @command="handleExport">
          <el-button type="primary">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-download"   command="page">导出当前数据</el-dropdown-item>
            <el-dropdown-item icon="el-icon-download"   command="allp">导出全部数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </vab-query-form-right-panel>
    </vab-query-form>

    <vxe-table
      class="mylist-table"
      v-loading="listLoading"
      ref="mainDataTable"
      id="mainData"
      border
      stripe
      show-overflow="tooltip"
      :height="height"
      :row-config="{ useKey: true,keyField:'taskId',isCurrent:true,isHover:true }"
      :column-config="{ resizable: true, useKey: true }"
      :data="list"
      size="small"
      :scroll-y="{enabled: true, gt: 300}"
      @current-change="currentSelectRow"
      @checkbox-change="handleSelectionChange"
      :checkbox-config="{highlight: true}"
      @cell-dblclick="cellDblClick"
      :seq-config="seqConfig"
      :export-config="exportConfig"
    >
      <vxe-column field="seq" type="seq" width="70" align="center" fixed="left"></vxe-column>
      <vxe-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :title="item.label"
        :field="item.prop"
        align="center"
        v-bind="{ ...item }"
        header-align="center"
        
      >
      </vxe-column>

      <vxe-column
        align="center"
        title="操作"
        fixed="right"
        show-overflow-tooltip
        width="auto"
        field="oper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)"   icon="el-icon-edit">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)" icon="el-icon-delete">删除</el-button>
        </template>
      </vxe-column>

      <template #empty>
        <el-image
          style="height: 110px"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </vxe-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="mainDataEdit" :popWidth="editWidth">
      <MasterForm
        ref="mainDataForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formList="this.checkListForms.formList2"
        :formCols="this.checkListForms.formCols"
        :formOptionData="this.checkListForms.formOptionData"
        :formConfig="formConfig"
      />
      <template slot="footerCont">
        <el-button @click="close">取 消</el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading"
        >
          确 定
        </el-button>
      </template>
    </table-edit>

    <table-search ref="mainDataSearch">
      <MasterQuery
        ref="mainDataQuery"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"
        :queryList="this.checkListForms.queryList2"
        :queryFormCols="this.checkListForms.queryFormCols"
        :queryOptionData="this.checkListForms.queryOptionData"
      />
      <template slot="footerCont">
        
        <el-button @click="queryClear">清 空</el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading"
        >
          确 定
        </el-button>
      </template>
    </table-search>

    <table-edit ref="sseLoadingEdit">
      <sseLoading
        ref="sseLoading"
        @handleFinish="handleFinish"
        :uid="uid"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          @click="sseLoadingclose">
          关 闭
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import { mainDataDoSave,
           mainDataDoDelete,
           mainDataGetPageList,
           mainDataPostPageList,
           mainDataPostPageList1,
           mainDataPostPageExport,
           mainDataPostImport,
           mainDataExport,
           mainDataExportExpre,
           mainDataDown } from '@/api/data/data'         
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import MasterForm from './Data_Form.vue'
  import MasterQuery from './Data_Query.vue'
  import { exportRearTmplVxe } from '@/api/exportExcel'
  import { mapGetters } from 'vuex'
  import config from '@/config'
  import { uuid } from '@/utils/index'
  import sseLoading from '@/views/common/SseLoading.vue'
  import {
    busiFieldConfigs,
    getCheckListColumnsForms,
    getQueryExpre
  } from '@/api/busp/busiUtil'

  export default {
    name: 'mainData',
    props: {
      gheight: {
        type: Number,
      },
      mainName: {
        type: String,
        default: '-1'
      },
      mainTitle: {
        type: String,
        default: '数据'
      }
    },
    components: {
      TableEdit,
      TableSearch,
      MasterForm,
      MasterQuery,
      sseLoading
    },
    data() {
      let searchForm = {
          pageNo: 1,
          pageSize: 20,
          sortField: 'taskId',
          sortOrder: '0',
          tn:this.mainName
      };
      const seqConfig = {
        seqMethod ({ rowIndex }) {
          return `${(searchForm.pageNo - 1) * searchForm.pageSize + rowIndex + 1}`
        }
      };
      const exportConfig = {
        sheetMethod(params) {
          const { worksheet } = params;
          worksheet.eachRow(excelRow => {
              excelRow.height = 40;
              excelRow.eachCell(excelCell => {
                  // 设置单元格边框
                  excelCell.border = {
                      top: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      left: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      bottom: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      },
                      right: {
                          style: 'thin',
                          color: {
                              argb: '000000'
                          }
                      }
                  };
              });
          });
        }
      };

      return {
        fullscreenLoading: false,
        editType: '',
        queryFormDf: {},
        queryForm: {},
        form: {},
        formConfig: {
          labelPosition: 'right',
          labelWidth: '80px',
          size: 'small',
        },
        title: '',
        isFullscreen: false,
        height: !this.gheight ? this.$baseTableHeight(1, 1) : this.gheight,
        list: [],
        tableData: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm,
        rules: {},
        checkList: [],
        columns: [],
        multipleSelection: [],
        editWidth: '1200px', 
        checkListForms:{},
        seqConfig,
        exportConfig,
        key:'',
        uid:''
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getTableFieldConfigs()
    },
    methods: {
      
      async getTableFieldConfigs() {
        const data = await busiFieldConfigs(this.mainName)
        this.checkListForms = getCheckListColumnsForms(data, {})
        this.checkList  = this.checkListForms.checkList
        this.columns    = this.checkListForms.columns
        this.rules      = this.checkListForms.rules
        var queryFormDf = {}
        var queryForm = {}
        for (var i = 0; i < this.checkListForms.queryList.length; i++) {
          if(this.checkListForms.queryList[i].bfcIsrange=='是'){
            queryFormDf[this.checkListForms.queryList[i].bfField+'Start'] = ''
            queryForm[this.checkListForms.queryList[i].bfField+'Start'] = ''
            queryFormDf[this.checkListForms.queryList[i].bfField+'End'] = ''
            queryForm[this.checkListForms.queryList[i].bfField+'End'] = ''
          }else{
            queryFormDf[this.checkListForms.queryList[i].bfField] = ''
            queryForm[this.checkListForms.queryList[i].bfField] = ''
          }
           
        }
        this.queryFormDf = queryFormDf
        this.queryForm = queryForm
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.mainDataForm.$refs.form.validate(async (valid) => {
          if (valid) { 
            for(let i in this.checkListForms.formMulList){
              if(this.form[i]){ 
                this.form[i] = this.form[i].join(",")
              }
            }
            this.form.tn = this.mainName 
            this.$baseConfirm('确定保存数据吗', null, async () => {
              const msg = await mainDataDoSave(this.form)
              if (msg.code == 200) {
                this.$message({ message: '保存操作成功!', type: 'success' })
                this.fetchData()
                this.close()
              } else {
                this.$message({ message: '保存操作失败!', type: 'warning' })
              }
            })
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.mainDataEdit.close()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val.row
      },
      handleSelectionChange(val) {
        
      },
      handleCommand(command){
        if(command=='edit'){
          this.handleEdit()
        } 
        else if(command=='delete'){
          this.handleDelete()
        } 
      },
      // 添加按钮事件
      async handleAdd(command) {
        if(command == 'add') {
          this.editWidth = document.documentElement.clientWidth * 0.9 + 'px'
          this.form = {}
          this.editType = 'add'
          this.$refs['mainDataEdit'].showEdit('添加'+this.mainTitle)
        }
      },
      // 双击行编辑事件
      cellDblClick(val) {
        this.row = val.row
        this.handleEdit(val.row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row
        this.editWidth = document.documentElement.clientWidth * 0.9 + 'px'
        this.editType = 'update'
        this.$refs['mainDataEdit'].showEdit('编辑'+this.mainTitle)
        this.form = Object.assign({}, this.row)
        for(let i in this.checkListForms.formMulList){
          if(this.form[i]){
            this.form[i] = this.form[i].split(",")
          }
        }
      },
      // 删除行数据
      handleDelete(row) {
        this.row = row
        this.$baseConfirm('确定删除'+this.mainTitle+'吗?', null, async () => {
          const msg = await mainDataDoDelete({tn:this.mainName,taskId:this.row.taskId})
          if (msg.code == 200) {
            this.$message({ message: '操作成功!', type: 'success' })
            this.fetchData()
          } else {
            this.$message({ message: '操作失败!', type: 'warning' })
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleFind() {
        let searchProps = []
        this.columns.forEach(key => {
          searchProps.push(key['field'])
        })
        const filterVal = String(this.key).trim()
        if (filterVal) {
          const filterRE = new RegExp(filterVal, 'gi')
          const rest = this.tableData.filter(item => searchProps.some(key => String(item[key]).indexOf(filterVal) > -1))
          this.list = rest.map(row => {
            // 搜索为克隆数据，不会污染源数据
            const item = Object.assign({}, row)
            searchProps.forEach(key => {
              item[key] = String(item[key]).replace("null","")
            })
            return item
          })
        } else {
          this.list = this.tableData
        }
      },
      
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await mainDataPostPageList1(this.searchForm)
        this.tableData = list
        this.list = list
        this.total = total
        this.listLoading = false
      },
      async handleFinish(batchId){
        this.$refs.sseLoadingEdit.close()
        if(batchId!=0 && batchId!=-1){
          let fm = {fileId:batchId,fileName:this.mainTitle+".xlsx"}
          const data = await mainDataDown({export:fm})
          if(data.code==200){
            window.open(config.baseURL+"/"+data.msg)
          }else{
            this.$message({ message: data.msg||'导出数据失败!', type: 'warning' })
          }
        }
      },
      sseLoadingclose(){
        this.$refs.sseLoadingEdit.close()
      },
      async handleExport(command){
        if(command=='page'){
          this.handleExportRear()
        }
        else{
          this.uid = uuid()
          this.$refs['sseLoadingEdit'].showEdit('数据导出')
          const $table = this.$refs.mainDataTable
          if ($table) {
            let columns = $table.getColumns()
            let excelExps = []
            for(var i=0;i<columns.length;i++){
              if(columns[i].field=='seq' || columns[i].field=='oper'){
                continue
              }
              let cs = parseInt(columns[i].colSpan)-1
              excelExps.push({field:columns[i].field,title:columns[i].title,rowspan:columns[i].rowSpan,celliswrap:"0",celltype:"text",cellwidth:"18",cellwraplength:"0",
                              colspan:columns[i].colSpan,dateFormat:"yyyy-MM-dd HH:mm:ss"})
              for(var j=0;j<cs;j++){
                excelExps.push({field:'',title:'',rowspan:1,celliswrap:"0",celltype:"text",cellwidth:"18",cellwraplength:"0",
                              colspan:1,dateFormat:"yyyy-MM-dd HH:mm:ss"})
              }                
            }
            let exportForm = Object.assign({}, this.searchForm)
            exportForm.export = {"sheetName":this.mainTitle,"excelExps":[excelExps],isserial:0,fileName:this.mainTitle+".xlsx",pageSize:500}
            exportForm.uid = this.uid
            const data = await mainDataExportExpre(exportForm)
            if(data.code==200){
              //window.open(config.baseURL+"/"+data.msg)
              this.$message({ message: '启动数据导出操作成功!', type: 'success' })
            }else{
              this.$message({ message: data.msg||'导出失败!', type: 'warning' })
            }
          }
        }
      },
      async handleExportRear() {
        const $table = this.$refs.mainDataTable
        if ($table) {
          var expcolumns = this.columns
          expcolumns.unshift({field:'seq'})
          $table.exportData({
            //excludeFields:"checkbox,oper",
            type: 'xlsx',
            columns:expcolumns,
            filename:this.mainTitle+`${Date.now()}`,
            sheetName:this.mainTitle
          })
        }
      },
      handleQuery(){
        //this.queryForm = Object.assign(this.queryForm, this.searchForm)
        this.$refs['mainDataSearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose() {
        this.$refs.mainDataSearch.close()
      },
      //高级查询清空
      queryClear() {
        this.queryForm = Object.assign(this.queryForm, this.queryFormDf)
        this.searchForm.query = {}
      },
      //高级查询
      querySure() {
        this.searchForm.pageNo = 1
        this.$refs.mainDataSearch.close()
        this.searchForm.query = getQueryExpre(this.checkListForms.queryList2,this.queryForm)
        this.fetchData()
      }
    }
  }
</script>
<style lang="scss" scoped>
  .mylist-table {
    ::v-deep(.keyword-highlight)  {
      background-color: #FFFF00;
    }
  }
</style>