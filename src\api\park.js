import request from '@/utils/request'

// 查询园区列表
export function getParksList(query) {
  return request({
    url: '/zcgl-park-registration/vPage',
    method: 'get',
    params: query
  })
}

// 查询园区详情
export function getParkDetail(id) {
  return request({
    url: `/zcgl-park-registration/get-by-id/${id}`,
    method: 'get'
  })
}

// 导出园区数据
export function exportParks(query) {
  return request({
    url: '/zcgl-park-registration/vExport',
    method: 'post',
    data: query
  })
}

// 统计园区数量和价值
export function getParksStats() {
  return request({
    url: '/zcgl-park-registration/getStatistic',
    method: 'get'
  })
}
