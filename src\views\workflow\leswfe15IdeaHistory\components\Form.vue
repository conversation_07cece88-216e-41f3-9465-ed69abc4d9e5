<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">

        <el-col :span="24">
          <el-form-item label="审批词" prop="ideaText">
            <el-input type="textarea" :rows="2" maxlength="1000" show-word-limit clearable v-model.trim="form.ideaText"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所属用户" prop="ideaUser"  style="display:none;">
            <el-input v-model.trim="form.ideaUser" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="PK, ID" prop="ideaId"  style="display:none;">
            <el-input v-model.trim="form.ideaId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'leswfe15IdeaHistoryForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>