<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="名称" prop="lpdName">
            <el-input v-model.trim="form.lpdName" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否为树形" prop="lpdIstree">
            <el-select v-model.trim="form.lpdIstree"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.lpdIstree" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="顺序" prop="lpdOrder">
            <el-input-number v-model.trim="form.lpdOrder" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="lpdDesc">
            <el-input type="textarea" :rows="2" maxlength="100" show-word-limit clearable v-model.trim="form.lpdDesc"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="pk" prop="lpdId"  style="display:none;">
            <el-input v-model.trim="form.lpdId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标识" prop="lpdSsign"  style="display:none;">
            <el-input v-model.trim="form.lpdSsign" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'lesysParamdirsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          lpdIstree:[{"label":"是","value":"是"},{"label":"否","value":"否"}]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>