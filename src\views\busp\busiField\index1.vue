<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <busiFieldSearch1
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :busiModelRow="busiModelRow"
      ref="busiFieldTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"/>

    <el-table
      ref="busiFieldTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="BusiField"
      row-key="bfId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleSele(row)">选择</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

  </div>
</template>

<script>
  import { busiFieldDoDeleteELog,
           busiFieldGetList,
           busiFieldDoSaveOrUpdLog,
           busiFieldDoExport } from '@/api/busp/busiField'
  import { busiDisplayFieldDoSaves } from '@/api/busp/busiDisplayField'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import busiFieldSearch1 from './components/Search1.vue'
  import busiFieldForm from './components/Form.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'busiField1',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      busiFieldSearch1,
      busiFieldForm
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,0):this.gheight,
        checkList: ['属性名','属性类型','属性实体名','属性实体类型','是否有效','使用状态','属性级别','属性顺序'],
        columns: [
                { prop:'bfName'      , label:'属性名'      , width:'auto' , sortable:false  },
                { prop:'bfColumnType', label:'属性类型'    , width:'auto' , sortable:false  },
                { prop:'bfField'     , label:'属性实体名'  , width:'auto' , sortable:false  },
                { prop:'bfEffState' , label:'是否有效'    , width:'auto' , sortable:false  },
                { prop:'bfUseState' , label:'使用状态'    , width:'auto' , sortable:false  },
                { prop:'bfLevel'    , label:'属性级别'    , width:'auto' , sortable:false  },
                { prop:'bfOrder'    , label:'属性顺序'    , width:'auto' , sortable:false  }
                
                
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          bfBmId:'',
          bfName:'',
          sortField:'',
          sortOrder:''
        },
        busiModelRow:{},
        busiDisplayRow:{}
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.searchForm.bfBmId = '-1'
      this.fetchData()
    },
    methods: {
      count(index) {
        return index + 1
      },
      init(row,row1){
        this.busiModelRow = row
        this.searchForm.bfBmId = row.bmId
        this.busiDisplayRow = row1
        this.fetchData()
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      async handleSele(row) {
        var slist = []
        slist.push({'bdfBdId':this.busiDisplayRow.bdId,bdfBmId:this.busiModelRow.bmId,bdfBfId:row.bfId,bdfLevel:'0',bdfIsshow:'是'})
        const  msg  = await busiDisplayFieldDoSaves( {busiDisplayFieldList:slist} )
        if(msg.code == 200) {
          this.$message({message:'操作成功!',type:'success'})
          //this.fetchData()
        }else{
          this.$message({message:'操作失败!',type:'warning'})
        }
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleSele(row)
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const data = await busiFieldGetList(this.searchForm)
        this.list = data.data
        this.total = this.list.length
        this.listLoading = false
      }
    },
  }
</script>