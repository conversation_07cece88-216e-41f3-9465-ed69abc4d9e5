import request from '@/utils/request'

//获取数据源表字段
export function lesysDbDsGetList(params) {
  return request({
    url: '/lesys-db-ds/vList1',
    method: 'get',
    params,
  })
}

export function lesysDbDsGetData(params) {
  return request({
    url: '/lesys-db-ds/vList',
    method: 'get',
    params,
  })
}

//列表分页加载数据
export function lesysDbDsGetPage(params) {
  return request({
    url: '/lesys-db-ds/vPageList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function lesysDbDsDoSave(data) {
  return request({
    url: '/lesys-db-ds/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function lesysDbDsDoUpdate(data) {
  return request({
    url: '/lesys-db-ds/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function lesysDbDsDoSaveOrUpd(data) {
  return request({
    url: '/lesys-db-ds/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function lesysDbDsDoDelete(data) {
  return request({
    url: '/lesys-db-ds/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function lesysDbDsDoExport(data) {
  return request({
    url: '/lesys-db-ds/vExport',
    method: 'post',
    data,
  })
}

export function testDataSource(ldId) {
  return request({
    url: '/lesys-db-ds/testDataSource/'+ldId,
    method: 'get'
  })
}

//根据数据源同步表信息、字段信息
export function syncTableByLdId(ldId) {
  return request({
    url: '/lesys-db-ds/syncTableByLdId/'+ldId,
    method: 'get'
  })
}

export function syncTables(ldId) {
  return request({
    url: '/lesys-db-ds/syncTables/'+ldId,
    method: 'get'
  })
}

export function syncTablesByName(data) {
  return request({
    url: '/lesys-db-ds/syncTablesByName',
    method: 'post',
    data
  })
}

//获取数据源表字段
export function lesysDbDsColumnExport(data) {
  return request({
    url: '/lesys-db-ds/vColumnExport',
    method: 'post',
    data,
  })
}

export function vPageDsTables(params) {
  return request({
    url: '/lesys-db-ds/vPageDsTables',
    method: 'get',
    params
  })
}

export function syncColumns(params) {
  return request({
    url: '/lesys-db-ds/syncColumns',
    method: 'get',
    params
  })
}
