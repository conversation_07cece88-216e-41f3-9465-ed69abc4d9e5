<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-button icon="el-icon-plus" type="primary" @click="handleCop">
              复制
            </el-button>  
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'

  export default {
    name: 'busiConfigsSearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal
      }
    },
    methods: {
      handleCop(){
        this.$emit('handleAdd','cop')
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd','add')
      }    
    },
  }
</script>