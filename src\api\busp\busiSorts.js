import request from '@/utils/request'

//列表分页加载数据
export function busiSortsGetList(params) {
  return request({
    url: '/busi-sorts/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function busiSortsDoSave(data) {
  return request({
    url: '/busi-sorts/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiSortsDoUpdate(data) {
  return request({
    url: '/busi-sorts/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function busiSortsDoSaveOrUpd(data) {
  return request({
    url: '/busi-sorts/saveOrUpd',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiSortsDoDelete(data) {
  return request({
    url: '/busi-sorts/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//后端导出
export function busiSortsDoExport(data) {
  return request({
    url: '/busi-sorts/vExport',
    method: 'post',
    data,
  })
}
