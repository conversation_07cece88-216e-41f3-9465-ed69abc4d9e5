<template>
  <div>
    <div class="rightClickMenu">
      <div class="menuItem" @click="item.func" v-for="(item,index) in menuList" :key="index">
        <div class="menuIcon">
          <img :src="item.pic" alt="#" style="object-fit: fill;width: 20px;">
        </div>
        <div class="menuTitle">
          {{item.title}}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: '',
    props: {},
    data() {
      return {
        menuList: []
      }
    },
    mounted() {},
    methods: {
      setMenuList(list){
        this.menuList = list
      },
    },
  }
</script>

<style scoped lang="scss">
  .rightClickMenu {
    border: 1px solid lightblue; /* 设置边框颜色为淡蓝色 */
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    background-color: #f0f0f0;
    color: black;
    width: 180px;
    height: auto;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    .menuItem {
      width: 100%;
      height: 40px;
      cursor: pointer;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &:hover {
        .menuIcon {
          background-color: #e6e6e6;
          border-right: none;
        }
        .menuTitle {
          background-color: #e6e6e6;
        }
      }
    }
    .menuIcon {
      width: 40px;
      height: 40px;
      border-right: 1px solid #ccc;
      display: flex;
      justify-content: center;
      align-items: center;

    }
    .menuTitle {
      width:140px;
      height: 100%;
      text-align: left;
      padding-left: 15px;
      line-height: 40px;

    }
  }
</style>
