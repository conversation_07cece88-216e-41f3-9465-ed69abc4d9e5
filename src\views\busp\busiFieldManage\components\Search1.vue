<template>
  <div class="search-container">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="0"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input placeholder="业务属性" v-model="tableQueryForm.bfName"  @keyup.enter.native="handleSearch" />
          </el-form-item>

          <el-form-item>
            
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        
      </vab-query-form-right-panel>
    </vab-query-form>
  </div>
</template>

<script>
  import VabDraggable from 'vuedraggable'
  import axios from 'axios'
  import config from '@/config'
  import store from '@/store'

  export default {
    name: 'busiFieldSearch1',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      },
      busiModelRow: {
        type: Object,
        require: true
      }
    },
    components: {
      VabDraggable
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList,
        fileUploadBtnText: "导入",
        uploadBtnIcon:"el-icon-upload2",
        fileAccept:".xls,.xlsx"
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm)
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd')
      }
      
    }
  }
</script>