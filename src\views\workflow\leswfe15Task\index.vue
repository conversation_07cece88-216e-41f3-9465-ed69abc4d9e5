<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <leswfe15TaskSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="leswfe15TaskTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleDevelop="handleDevelop"
      @handleContract="handleContract"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>
    <el-table
      ref="leswfe15TaskTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="Leswfe15Task"
      row-key="taskId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      highlight-current-row 
      @current-change="currentSelectRow"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="70"
        label-class-name="number">
      </el-table-column>

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
        :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>

        <template slot-scope="scope">
          <span v-if="item.slot === 'taskIsNew' ">
             <span v-if="scope.row.taskIsNew==0">
                否(已打开)
             </span>
             <span v-else>
                是(未打开)
            </span>
          </span>
          <span v-else-if="item.slot === 'taskDoSts' ">
            <span v-if="scope.row.taskDoTime==0">
               未办理
            </span>
            <span v-else>
               已办理
           </span>
         </span>
          <span v-else>
            {{ scope.row[item.prop] }}
          </span>
        </template>

      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>

    <table-edit ref="leswfe15TaskEdit">
      <leswfe15TaskForm
        ref="leswfe15TaskForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="leswfe15TaskQuerySearch">
      <leswfe15TaskQuery
        ref="leswfe15TaskQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >
  </div>
</template>

<script>
  import { leswfe15TaskDoReciveTask,
           leswfe15TaskDoSaveOrUpdLog,
           leswfe15TaskDoExport,
           leswfe15TaskGetTreeList } from '@/api/workflow/leswfe15Task'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import leswfe15TaskSearch from './components/Search.vue'
  import leswfe15TaskForm from './components/Form.vue'
  import leswfe15TaskQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel';
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'leswfe15Task',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      leswfe15TaskSearch,
      leswfe15TaskForm,
      leswfe15TaskQuery
    },
    data() {
      return {

        fullscreenLoading: false,
        editType:'',
        queryFormDf: {nodeName:''},
        queryForm: {nodeName:''},
        //表单赋值
        form: {instId:'',nodeId:'',nodeName:'',taskPid:'',taskGid:'',taskUid:'',taskUxm:'',taskTime:'',taskIsNew:'',taskDoUid:'',taskDoUxm:'',taskDoTime:'',taskDoIdea:'',xUserData:'',xNodeData:'',xTempSave:'',xTempFile:'',xTempSend:''},
        //表单验证规则
        rules: {
          instId: [
            { required: true, message: '请输入流程实例ID', trigger: 'blur' }
          ],
          nodeId: [
            { required: true, message: '请输入节点ID', trigger: 'blur' }
          ],
          nodeName: [
            { required: true, message: '请输入节点名称', trigger: 'blur' }
          ],
          taskPid: [
            { required: true, message: '请输入上一环节task_id，第一个为"0"，缺省', trigger: 'blur' }
          ],
          taskGid: [
            { required: true, message: '请输入任务组id，抢先式任务为同一组,task_gid = task_pid，非抢先式 taskgid=0', trigger: 'blur' }
          ],
          taskUid: [
            { required: true, message: '请输入指定的任务办理人,登录帐号', trigger: 'blur' }
          ],
          taskUxm: [
            { required: true, message: '请输入指定的任务办理人,姓名', trigger: 'blur' }
          ],
          taskTime: [
            { required: true, message: '请输入任务产生时间', trigger: 'blur' }
          ],
          taskIsNew: [
            { required: true, message: '请输入是否是新任务：1-新任务, 否则其他值', trigger: 'blur' }
          ],
          taskDoUid: [
            { required: true, message: '请输入任务实际办理人,登录帐号', trigger: 'blur' }
          ],
          taskDoUxm: [
            { required: true, message: '请输入任务实际办理人,姓名', trigger: 'blur' }
          ],
          taskDoTime: [
            { required: true, message: '请输入任务实际办理时间', trigger: 'blur' }
          ],
          taskDoIdea: [
            { required: true, message: '请输入任务办理的意见输入', trigger: 'blur' }
          ],
          xUserData: [
            { required: true, message: '请输入任务办理用户(TASK_UID)的 部门,岗位,角色,用户组 等属性信息', trigger: 'blur' }
          ],
          xNodeData: [
            { required: true, message: '请输入任务所属流程节点的 部门,岗位,角色,用户组 等属性信息, 来自流程定义中存储的数据', trigger: 'blur' }
          ],
          xTempSave: [
            { required: true, message: '请输入任务办理过程中的表单数据暂存: json object 格式', trigger: 'blur' }
          ],
          xTempFile: [
            { required: true, message: '请输入任务办理过程中文件操作的动作，临时存放', trigger: 'blur' }
          ],
          xTempSend: [
            { required: true, message: '请输入任务办理过程中客户端获取节点的留底，用于比对，避免再次解析流程', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1):this.gheight,
        checkList: ['任务名称','指定的任务办理人','任务产生时间','是否是新任务','任务实际办理人','任务办理状态'],
        columns: [
                { prop:"nodeName" ,label:"任务名称", width:'auto',disableCheck: true , sortable:false  },
                { prop:'taskUxm', label:'指定的任务办理人', width:'auto' , sortable:false  },
                { prop:'taskTime', label:'任务产生时间', width:'auto' , sortable:false  },
                { prop:'taskIsNew', label:'是否是新任务', width:'auto' , sortable:false ,slot:'taskIsNew' },
                { prop:'taskDoUxm', label:'任务实际办理人', width:'auto' , sortable:false  },
                { prop:'taskDoSts', label:'任务办理状态', width:'auto' , sortable:false ,slot:'taskDoSts' }
        ],
        list: [],
        imageList: [],
        listLoading: true,
        row: '',
        searchForm: {
          nodeName:'',
          instId:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      //this.fetchData();
    },
    methods: {
      initTask(row){
        this.searchForm.instId = row.instId
        this.fetchData()
      },
      rebackTask(row){
        if(this.row && this.row.taskId){
          if(this.row.taskDoTime==0){
            this.$message({message:'请选择已办理的流程任务节点!',type:'warning'})
          }else{
            this.$baseConfirm('确定取回该流程任务节点吗', null, async () => {
              const msg = await leswfe15TaskDoReciveTask( {taskId:this.row.taskId,"taskPid":this.row.taskPid} )
              if(msg.code == 200) {
                this.$message({message:'取回操作成功!',type:'success'})
                this.$emit("trackClose");  
              }else{
                this.$message({message:msg.data||'取回操作失败!',type:'warning'})
              }
            })
          }
        }else{
          this.$message({message:'请选择要取回的流程任务节点!',type:'warning'})
        }
      },
      //新增或者修改保存
      save() {
        this.$refs.leswfe15TaskForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //系统如记录操作日志，请修改日志信息
            this.form.logData = JSON.stringify(this.form)
            if(this.form.taskId){
              this.form.logDesc = "修改数据"
            }else{
              this.form.logDesc = "新增数据"
            }
            const  msg  = await leswfe15TaskDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        });
      },
      // 弹窗取消按钮
      close() {
        this.$refs.leswfe15TaskEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      currentSelectRow(val) {
        this.row = val
      },
      //添加
      handleAdd() {
        this.editType = 'add'
        this.form=Object.assign({},{"taskPid":"0"})
        this.$refs['leswfe15TaskEdit'].showEdit('添加')
      },
      //添加下级
      handleAddSub(row){
        this.editType = 'add'
        this.form=Object.assign({},{"taskPid":row.taskId})
        this.$refs['leswfe15TaskEdit'].showEdit('添加下级')
      },
      // 表格行双击事件
      cellDblClick(row) {
        this.handleEdit(row) 
      },
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['leswfe15TaskEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
      },
      handleDelete(row) {
        
      },
      handleSearch($event) {
        this.searchForm = $event
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['leswfe15TaskQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.leswfe15TaskQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.$refs.leswfe15TaskQuerySearch.close()
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const {
          data: { list },
        } = await leswfe15TaskGetTreeList(this.searchForm)
        this.list = list
        this.listLoading = false
        this.isShow = false
        this.$refs.leswfe15TaskTs.isShow = false
      },
      // 展开
      handleDevelop() {
        let dom = document.getElementById("Leswfe15Task")
        let els = dom.getElementsByClassName('el-table__expand-icon')
        if(this.list.length != 0 && els.length != 0){
          this.$refs.leswfe15TaskTs.isShow = false;
          for(let k = 0;k < els.length;k ++){
            els[k].classList.add("dafult")
          }
          if(dom.getElementsByClassName('el-table__expand-icon--expanded')){
            const open = dom.getElementsByClassName('el-table__expand-icon--expanded')
            for(let j = 0;j < open.length;j ++){
              open[j].classList.remove("dafult")
            }
            const dafult = dom.getElementsByClassName('dafult')
            for(let a = 0;a < dafult.length;a ++){
              dafult[a].click()
            }
          }
        }
      },
      // 收缩
      handleContract() {
        if(this.list.length != 0){
          this.$refs.leswfe15TaskTs.isShow = true
          const elsopen = document.getElementById("Leswfe15Task").getElementsByClassName('el-table__expand-icon--expanded')
          if(elsopen){
            for(let i = 0;i < elsopen.length;i ++){
              elsopen[i].click()
            }
          }
        }
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{"taskTime":{"celltype":"date"}},
                      "fileName":"LES工作流--流程任务表.xls",
                      "isnumber":true,
                      "excelTitle":"LES工作流--流程任务表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#Leswfe15Task",params)
        const { msg }  =  await leswfe15TaskDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"LES工作流--流程任务表模板.xls",
                      "excelIstmpl":true}
        let qf = exportRearEnd("#Leswfe15Task",params)
        const { msg }  =  await leswfe15TaskDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      }
    },
  }
</script>