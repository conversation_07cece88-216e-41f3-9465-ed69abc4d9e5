<template>
  <div>
    <el-row>
      <div class="FilterCondition">
        <div :style="{ width: nameLength, fontWeight: '777' }">
          {{ name }}
        </div>
        <el-select
          v-model="condition"
          placeholder="请选择"
          :popper-append-to-body="false"
          style="width: 100px"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="keyWords"
          clearable
          placeholder="请输入查询关键字"
          style="width: 150px"
          @change="handlechange"
        />
      </div>
    </el-row>
    <el-row>
      <div style="float: right; margin-right: 10px">
        <slot name="buttonSlot"></slot>
      </div>
    </el-row>
  </div>
</template>
<script>
  export default {
    name: 'FilterCondition',
    props: {
      name: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        condition: '包含',
        keyWords: '',
        options: [
          {
            label: '包含',
            value: '包含',
          },
        ],
      }
    },
    computed: {
      nameLength: function () {
        return this.name.length * 18 + 'px'
      },
    },
    methods: {
      clear() {
        this.keyWords = ''
      },
      handlechange(val) {
        this.$emit('keywordsChanged', val)
      },
    },
  }
</script>

<style scoped lang="scss">
  .FilterCondition {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 50px;
    width: 100%;
  }
</style>
