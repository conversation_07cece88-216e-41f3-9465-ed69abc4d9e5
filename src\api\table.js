import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/les-assets/vPage',
    method: 'get',
    params,
  })
}

export function doExport(data) {
  return request({
    url: '/les-assets/vExport',
    method: 'post',
    data,
  })
}

export function doSave(data) {
  return request({
    url: '/les-assets/save',
    method: 'post',
    data,
  })
}

export function doSaveOrUpd(data) {
  return request({
    url: '/les-assets/saveOrUpd',
    method: 'post',
    data,
  })
}

export function doSaveOrUpdLog(data) {
  return request({
    url: '/les-assets/saveOrUpdLog',
    method: 'post',
    data,
  })
}

export function doDelete(data) {
  return request({
    url: '/les-assets/delete-by-id/' + data.id,
    method: 'delete',
    data,
  })
}

export function getTreeList(params) {
  return request({
    url: '/lesys-tests/vTreeList',
    method: 'get',
    params,
  })
}
