<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <lesysDbDsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="lesysDbDsTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange"
      @handleExportRear="handleExportRear"
      @handleImportRear="handleImportRear"
      @handleExportTmpl="handleExportTmpl"
      @handleQuery="handleQuery"/>

    <el-table
      ref="lesysDbDsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="lesysDbDs"
      row-key="ldId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="180"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleTest(row)">测试</el-button>
          &nbsp;
          <el-dropdown @command="handleCommand" trigger="click">
            <span class="el-dropdown-link">
              同步
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="tables">同步表</el-dropdown-item>
              <el-dropdown-item command="columns">同步列</el-dropdown-item>
              <!--el-dropdown-item command="tablecolumns">同步表列</el-dropdown-item-->
            </el-dropdown-menu>
          </el-dropdown>
          &nbsp;
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <table-edit ref="lesysDbDsEdit">
      <lesysDbDsForm
        ref="lesysDbDsForm"
        slot="form"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="close">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="save"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-search ref="lesysDbDsQuerySearch">
      <lesysDbDsQuery
        ref="lesysDbDsQueryForm"
        slot="form"
        :form="queryForm"
        :formConfig="formConfig"/>
      <template slot="footerCont">
        <el-button
          @click="queryClose">
          取 消
        </el-button>
        <el-button
          @click="queryClear">
          清 空
        </el-button>
        <el-button
          type="primary"
          @click="querySure"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search >

    <table-edit ref="lesysDbTableColumns1Sel" :fullscreen="true">
      <lesysDbTableColumns1
        ref="lesysDbTableColumns1"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="lesysDbTableColumns1close"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >

    <table-edit ref="lesysDbTablesSynSel" :fullscreen="true">
      <lesysDbTablesSyn
        ref="lesysDbTablesSyn"
        slot="form"/>
      <template slot="footerCont">
        <el-button
          type="primary"
          @click="lesysDbTablesSynclose"
          v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit >
  </div>
</template>

<script>
  import {lesysDbDsDoDelete,
          lesysDbDsGetPage,
          lesysDbDsDoSaveOrUpd,
          lesysDbDsDoExport,
		      testDataSource,
		      syncTables,
		      syncColumns,
		      syncTableByLdId } from '@/api/dbds/lesysDbDs'
  import TableEdit from '@/views/common/TableEdit.vue'
  import TableSearch from '@/views/common/TableSearch.vue'
  import lesysDbDsSearch from './components/Search.vue'
  import lesysDbDsForm from './components/Form.vue'
  import lesysDbDsQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'
  import { Loading } from 'element-ui'
  import lesysDbTableColumns1 from '@/views/dbds/lesysDbTableColumns/index1.vue'
  import lesysDbTablesSyn from '@/views/dbds/lesysDbTables/indexSyn.vue'

  export default {
    name: 'lesysDbDs',
    props: {
      gheight: {
        type:Number
      }
    },
    components: {
      TableEdit,
      TableSearch,
      lesysDbDsSearch,
      lesysDbDsForm,
      lesysDbDsQuery,
      lesysDbTableColumns1,
      lesysDbTablesSyn
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {ldName:'',ldType:'',ldHost:'',ldUsername:'',ldPassword:'',ldDb:'',ldPort:'',ldShort:'',ldUrl:'',ldSchema:'',ldPath:'',ldSort:''},
        queryForm: {ldName:'',ldType:'',ldHost:'',ldUsername:'',ldPassword:'',ldDb:'',ldPort:'',ldShort:'',ldUrl:'',ldSchema:'',ldPath:'',ldSort:''},
        form: {ldName:'',ldType:'',ldHost:'',ldUsername:'',ldPassword:'',ldDb:'',ldPort:'',ldShort:'',ldUrl:'',ldSchema:'',ldPath:'',ldSort:''},
        rules: {
          ldName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ],
          ldType: [
            { required: true, message: '请输入数据库类型', trigger: 'blur' }
          ],
          ldHost: [
            { required: true, message: '请输入ip', trigger: 'blur' }
          ],
          ldUsername: [
            { required: true, message: '请输入用户', trigger: 'blur' }
          ],
          ldPassword: [
            { required: true, message: '请输入密码', trigger: 'blur' }
          ],
          ldDb: [
            { required: true, message: '请输入实例', trigger: 'blur' }
          ],
          ldPort: [
            { required: true, message: '请输入端口', trigger: 'blur' }
          ],
          ldShort: [
            { required: true, message: '请输入简称', trigger: 'blur' }
          ],
          ldUrl: [
            { required: false, message: '请输入url', trigger: 'blur' }
          ],
          ldSchema: [
            { required: false, message: '请输入模式名', trigger: 'blur' }
          ],
          ldSort: [
            { required: true, message: '请输入数据源类型', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '',
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['名称','数据库类型','简称','数据源类型','ip','端口','用户','密码','实例','模式名','url'],
        columns: [
                { prop:'ldName'    , label:'名称'         , width:'auto' , sortable:false  },
                { prop:'ldType'    , label:'数据库类型'    , width:'auto' , sortable:false  },
                { prop:'ldShort'   , label:'简称'         , width:'auto' , sortable:false  },
                { prop:'ldSort'    , label:'数据源类型'    , width:'auto' , sortable:false  },
                { prop:'ldHost'    , label:'ip'           , width:'auto' , sortable:false  },
                { prop:'ldPort'    , label:'端口'         , width:'auto' , sortable:false  },
                { prop:'ldUsername', label:'用户'         , width:'auto' , sortable:false  },
                { prop:'ldPassword', label:'密码'         , width:'auto' , sortable:false  },
                { prop:'ldDb'      , label:'实例'         , width:'auto' , sortable:false  },
                { prop:'ldSchema'  , label:'模式名'       , width:'auto' , sortable:false  },
                { prop:'ldUrl'     , label:'url'          , width:'auto' , sortable:false  }
                
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.lesysDbDsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            //验证模式名是否必填
            if(this.form.ldUrl.indexOf('{{schema}}')!=-1){
              if(this.form.ldSchema==''){
                this.$message({message:'请输入数据库模式名!',type:'warning'})
                return
              }
            }else{
              this.form.ldSchema = ''
            }
            const  msg  = await lesysDbDsDoSaveOrUpd( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.close()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.lesysDbDsEdit.close()
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.$refs['lesysDbDsEdit'].showEdit('添加')
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.$refs['lesysDbDsEdit'].showEdit('编辑')
        this.form = Object.assign({},row)
        this.$nextTick(()=> {
            this.$refs.lesysDbDsForm.changeType(this.form.ldType)
          })
      },
      // 删除行数据
      handleDelete(row) {
        this.$baseConfirm('确定删除吗', null, async () => {
          const msg = await lesysDbDsDoDelete({'id':row.ldId})
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            await this.fetchData()
          }else{
            this.$message({message:msg.msg||'删除操作失败!',type:'warning'})
          }
        })
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['lesysDbDsQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.lesysDbDsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.lesysDbDsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await lesysDbDsGetPage(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 后端导出
      async handleExportRear(){
        let params = {"dataFields":{},
                      "fileName":"数据源表.xls",
                      "isnumber":true,
                      "excelTitle":"数据源表",
                      "queryForm":this.searchForm||{}}
        let qf = exportRearEnd("#lesysDbDs",params)
        const { msg }  =  await lesysDbDsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      //后端导出模板
      async handleExportTmpl(){
        let params = {"fileName":"数据源表模板.xls","excelIstmpl":true}
        let qf = exportRearEnd("#lesysDbDs",params)
        const { msg }  =  await lesysDbDsDoExport(qf)
        window.open(baseURL+"/"+msg)
      },
      // excel导入
      handleImportRear(){
        this.fetchData()
      },
      //测试连通性
      handleTest(row) {
        this.$baseConfirm('确定测试数据源连通性吗？', null, async () => {
            let loadingInstance = Loading.service({ fullscreen: false,text: '测试数据源连通性。。。。。' });
            const msg = await testDataSource(row.ldId)
            loadingInstance.close()
            if(msg.code == 200) {
              this.$message({message:msg.msg||'测试连通成功!',type:'success'})
            }else{
              this.$message({message:msg.msg||'测试连通失败!',type:'warning'})
            }
        })
      },
      handleCommand(command) {
        if(command=='tablecolumns'){
          this.handleSyncTableColumns(this.row)
        }
        else if(command=='tables'){
          this.handleSyncTables(this.row)
        }
        else if(command=='columns'){
          this.handleSyncColumns(this.row)
        }
      },
      // 数据同步表
      async handleSyncTables(row) {
        const msg0 = await testDataSource(row.ldId)
        if(msg0.code == 200) {
          //this.$baseConfirm('确定同步数据源数据表吗？', null, async () => {
          //    let loadingInstance = Loading.service({ fullscreen: true,text: '数据源数据表同步中。。。。。' });
          //    const msg = await syncTables(row.ldId)
          //    this.$nextTick(() => {
          //      loadingInstance.close();
          //    });
          //    if(msg.code == 200) {
          //      this.$message({message:'同步成功!',type:'success'})
          //    }else{
          //      this.$message({message:'同步失败!',type:'warning'})
          //    }
          //})
          this.row = row
          this.$refs['lesysDbTablesSynSel'].showEdit(this.row.ldName+'表同步')
          this.$nextTick(()=> {
            this.$refs.lesysDbTablesSyn.init(this.row)
          })
        }else{
          this.$message({message:'数据库连接失败，无法同步!',type:'warning'})
        }
      },
      lesysDbTablesSynclose(){
        this.$refs.lesysDbTablesSynSel.close()
      },
      // 数据同步列
      async handleSyncColumns(row) {
        const msg0 = await testDataSource(row.ldId)
        if(msg0.code == 200) {
          this.row = row
          this.$refs['lesysDbTableColumns1Sel'].showEdit(this.row.ldName+'列同步')
          this.$nextTick(()=> {
            this.$refs.lesysDbTableColumns1.init(this.row)
          })
        }else{
          this.$message({message:'数据库连接失败，无法同步!',type:'warning'})
        }
      },
      lesysDbTableColumns1close(){
        this.$refs.lesysDbTableColumns1Sel.close()
        //this.fetchData()
      },
      // 数据同步
      async handleSyncTableColumns(row) {
        const msg0 = await testDataSource(row.ldId)
        if(msg0.code == 200) {
          //默认同步所有的表，是否增加控制选择 
          this.$baseConfirm('确定同步数据源数据表及表结构吗？', null, async () => {
              let loadingInstance = Loading.service({ fullscreen: true,text: '数据源表及表结构同步中。。。。。' });
              const msg = await syncTableByLdId(row.ldId)
              this.$nextTick(() => {
                loadingInstance.close();
              });
              if(msg.code == 200 && msg.data == true) {
                this.$message({message:'同步成功!',type:'success'})
                await this.fetchData()
              }else{
                this.$message({message:'同步失败!',type:'warning'})
              }
          })
        }else{
          this.$message({message:'数据库连接失败，无法同步!',type:'warning'})
        }
      }
    },
  }
</script>
<style>
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
</style>