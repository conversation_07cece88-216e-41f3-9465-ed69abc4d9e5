import request from '@/utils/request'

export function lesysCreateSse(params) {
  return request({
    url: '/Sse/createSse/'+params.id,
    method: 'get',
    params,
  })
}

export function lesysGetSseMessage(params) {
  return request({
    url: '/Sse/sendMsg/'+params.id,
    method: 'get',
    params,
  })
}

export function lesysCloseSse(params) {
  return request({
    url: '/Sse/closeSse/'+params.id,
    method: 'get',
    params,
  })
}


