<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="列类型" prop="ltcColType">
            <el-input v-model.trim="form.ltcColType" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="实体类型" prop="ltcLdeId">
            <el-select v-model.trim="form.ltcLdeId"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.ltcLdeId" :key="item.ldeId" :label="item.ldeName" :value="item.ldeId"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="ltcId"  style="display:none;">
            <el-input v-model.trim="form.ltcId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  import { lesysDbEntityTypeGetList } from '@/api/dbds/lesysDbEntType'
  export default {
    name: 'lesysDbColMappingForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          ltcLdeId:[]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const data = await lesysDbEntityTypeGetList({})
        this.optionsData.ltcLdeId = data.data
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>