<template>
  <div class="upload-container">
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="dialogFormVisible"
      :width="width"
      @close="close"
      style="overflow-y: auto;"
      :close-on-click-modal="false"
      class="showAll_dialog"
      :destroy-on-close="true"
      top="35vh"
    >
    <el-upload
        class="upload-demo"
        ref="upload"
        action=""
        :file-list="fileList"
        :auto-upload="fileConfig.isAuto"
        :multiple="fileConfig.limit > 1 ? true : false"
        :accept="fileConfig.accept"
        :limit="fileConfig.limit"
        :before-upload="beforeUpload"
        :on-exceed="handleExceed"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :http-request="uploadFile"
        :on-success="handleSuccess"
        :on-error="handleError"
        >
        
        <el-button slot="trigger" size="small" type="primary" :title="tipsContant">选择文件</el-button>
        <el-button v-if="!fileConfig.isAuto" style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        <!--div slot="tip" class="el-upload__tip">{{ tipsContant }}</div-->
      </el-upload>
    </el-dialog>

  </div>
</template>

<script>
  import axios from 'axios'
  import { v4 as uuidv4 } from 'uuid'
  import { mapGetters } from 'vuex'
  import request from '@/utils/request'

  import {baseURL,lesFileSaveSec} from '@/config'

  export default{
    name: 'Upload',
    props: {
      config: {
        type: Object,
        default: () => {
          return {
            isAuto: true,
            limit: 1,
            accept: '*',
            maxSize: '20', //单位M
            isDecode: ''
          }
        }
      }
    },
    data() {
      return {
        title: '文件上传',
        width: '500px',
        dialogFormVisible: false,
        // 上传的文件列表数据[{name: 'food.jpg', url: 'https://xxx.cdn.com/xxx.jpg'}]
        fileList: [],
        fileConfig: {},
        uploadFiles: []
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username'
      }),
      tipsContant() {
        this.fileConfig = Object.assign({
          isAuto: true,
          limit: 1,
          accept: '*',
          maxSize: '10',
          isDecode: ''
        },this.config);
        
        let tips = '';
        if(this.fileConfig.accept !== "*") {
          tips += `允许上传文件的类型为${this.fileConfig.accept},`
        }else {
          tips += `允许上传所有类型文件,`
        }
        tips += `上传的文件个数为${this.fileConfig.limit}`
        if(this.fileConfig.maxSize != 20) {
          tips += `且单个文件大小不能超过${this.fileConfig.maxSize}M`
        }else {
          tips += `且单个文件大小不能超过20M`
        }
        return tips;
      }
    },
    methods: {
      // 关闭文件上传弹窗
      close() {
        this.dialogFormVisible = false;
      },
      // 打开文件上传弹窗
      show() {
        this.dialogFormVisible = true;
      },
      // 文件上传之前
      beforeUpload(file) {
        const { name,size } = file;
        let fileType = name.substring(name.lastIndexOf('.') + 1).toUpperCase()+",";
        console.log(fileType)
        if(size / 1024 / 1024 > this.fileConfig.maxSize) {
          this.$message({
            message: `${name}文件大小超过${this.fileConfig.maxSize}M，请重新上传`,
            type: "warning",
          });
          return false;
        }
        if(this.fileConfig.accept !== '*' && this.fileConfig.accept !== '') {
          // let acceptArr = this.fileConfig.accept.split(',');
          // acceptArr.map(item => {
          //   if(item !== fileType) {
          //     this.$message({
          //       message: `${name}文件类型不符，请重新上传`,
          //       type: "warning",
          //     });
          //     return false;
          //   }
          // })
          let acceptStr = this.fileConfig.accept+","
          if(acceptStr.indexOf(fileType)==-1){
            this.$message({
              message: `${name}文件类型不符，请重新上传`,
              type: "warning",
            });
            return false;
          }
        }
      },
      handleExceed(files, fileList){
        this.$message.warning('本次选择了'+files.length+'个文件，共选择了'+(files.length + fileList.length)+'个文件，当前限制选择'+this.fileConfig.limit+'个文件。');
      },
      submitUpload() {
        this.$refs.upload.submit();
      },
      handleError(err, file, fileList){
        console.log("error"+file.name);
      },
      handleRemove(file, fileList) {

      },
      // 点击已上传的文件列表时触发的函数
      handlePreview(file) {
        console.log(file)
      },
      uploadFile(param){
        let file = param.file;
        let fd = new FormData();
        fd.append('file', file);
        // 配置请求头
        // const config = {
        //   headers: {
        //     "Content-Type": "application/octet-stream"
        //   }
        // };
        let file_id = uuidv4();
        file_id = file_id.replace(/-/g,'');
        let file_name = file.name;
        let file_source = this.username;
        let file_replace = 0;
        let file_sec = 0;
        let file_isdecode = this.fileConfig.isDecode||lesFileSaveSec;
        
        var url = "/FILE/FileUploadService?file_id="+file_id+"&file_name="+file_name+"&file_source="+file_source+
                  "&file_replace="+file_replace+"&file_sec="+file_sec+"&file_isdecode="+file_isdecode;

        axios({
          url,
          method: 'post',
          data:file,
          headers:{
             'Content-Type': 'application/octet-stream'
          },
          onUploadProgress: progressEvent => {
            const complete = parseInt(
              ((progressEvent.loaded / progressEvent.total) * 100) | 0,
              10
            );
            param.onProgress({ percent: complete })
          }
        }).then(res => {
          param.onSuccess(res);
        }).catch(err => {
          param.onError(err)
        })

      },
      // 文件上传成功时的钩子
      handleSuccess(response, file, fileList){
        if(fileList.every(that => that.status == 'success')) {
          fileList.map(item => {
            item.response && this.uploadFiles.push({
              file_name: item.response.data.file_name,
              file_id: item.response.data.file_id,
              file_sec: item.response.data.file_sec,
              file_size: item.response.data.file_size
            })
          });
          //this.fileConvert(this.uploadFiles);
          this.$nextTick(() => {
            this.$emit("handleUploadScuess", this.uploadFiles);
            // this.$message({
            //   message: '文件上传成功！',
            //   type: 'success'
            // });
            this.fileList = [];
            this.uploadFiles = [];
            this.close();
          })
        }
      },
      //文件转换
      fileConvert(fileList){
        var convertList = fileList.filter(item=>{
          let ext = item.file_name.toLowerCase();
          ext = '*.' + ext.split('.').pop();
          return this._LesFile_IsOleFile(ext);
        });
        var url = "/FILE/LesSystem/LesAspose/FileConvert.jsp";
        let param = new URLSearchParams()
        param.append('md','fileConvert')
        param.append('data', JSON.stringify(convertList))
        axios({
          url,
          method: 'post',
          data:param
        }).then(res => {
          console.log(res)
        })
      },
      //判断格式
      _LesFile_IsOleFile(ext){
        let _LESFILE_OLE_EXTS = "*.doc; *.docx; *.xls; *.xlsx; *.ppt; *.pptx; *.pdf;";
        return (_LESFILE_OLE_EXTS.indexOf(ext)>=0);
      }
    }
  }
</script>
