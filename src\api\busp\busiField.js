import request from '@/utils/request'

//列表分页加载数据
export function busiFieldGetList(params) {
  return request({
    url: '/busi-field/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function busiFieldDoSave(data) {
  return request({
    url: '/busi-field/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function busiFieldDoUpdate(data) {
  return request({
    url: '/busi-field/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function busiFieldDoSaveOrUpd(data) {
  return request({
    url: '/busi-field/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function busiFieldDoSaveOrUpdLog(data) {
  return request({
    url: '/busi-field/saveOrUpdLog',
    method: 'post',
    data,
  })
}
export function busiFieldDoDeletes(data) {
  return request({
    url: '/busi-field/deletes',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function busiFieldDoDelete(data) {
  return request({
    url: '/busi-field/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function busiFieldDoDeleteLog(data) {
  return request({
    url: '/busi-field/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function busiFieldDoDeleteELog(data) {
  return request({
    url: '/busi-field/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function busiFieldGetTreeList(params){
  return request({
    url: '/busi-field/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function busiFieldDoExport(data) {
  return request({
    url: '/busi-field/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function busiFieldGetStat(params) {
  return request({
    url: '/busi-field/vStat',
    method: 'get',
    params,
  })
}
