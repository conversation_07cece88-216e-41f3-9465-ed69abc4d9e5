<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="8">
          <el-form-item label="流程实例ID" prop="instId">
            <el-input v-model.trim="form.instId" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="节点ID" prop="nodeId">
            <el-input v-model.trim="form.nodeId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务名称" prop="nodeName">
            <el-input v-model.trim="form.nodeName" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务ID,流水号" prop="taskId">
            <el-input-number v-model.trim="form.taskId" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上一环节task_id，第一个为0，缺省" prop="taskPid">
            <el-input-number v-model.trim="form.taskPid" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务组id，抢先式任务为同一组,task_gid = task_pid，非抢先式 taskgid=0" prop="taskGid">
            <el-input-number v-model.trim="form.taskGid" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="指定的任务办理人,登录帐号" prop="taskUid">
            <el-input v-model.trim="form.taskUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="指定的任务办理人,姓名" prop="taskUxm">
            <el-input v-model.trim="form.taskUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务时间" prop="taskTime">
            <el-date-picker v-model.trim="form.taskTime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否新任务" prop="taskIsNew">
            <el-input-number v-model.trim="form.taskIsNew" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务实际办理人,登录帐号" prop="taskDoUid">
            <el-input v-model.trim="form.taskDoUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务实际办理人,姓名" prop="taskDoUxm">
            <el-input v-model.trim="form.taskDoUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务实际办理时间" prop="taskDoTime">
            <el-input-number v-model.trim="form.taskDoTime" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务办理的意见输入" prop="taskDoIdea">
            <el-input v-model.trim="form.taskDoIdea" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上一步骤名称" prop="lastNodeName">
            <el-input v-model.trim="form.lastNodeName" maxlength="93" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程创建部门" prop="instCreateDept">
            <el-input v-model.trim="form.instCreateDept" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程模板ID" prop="tmplId">
            <el-input v-model.trim="form.tmplId" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程实例名称" prop="instName">
            <el-input v-model.trim="form.instName" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程实例状态" prop="instState">
            <el-input v-model.trim="form.instState" maxlength="5" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程实例密级" prop="instSec">
            <el-input-number v-model.trim="form.instSec" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程实例创建用户账号" prop="instCreateUid">
            <el-input v-model.trim="form.instCreateUid" maxlength="10" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程实例创建用户" prop="instCreateUxm">
            <el-input v-model.trim="form.instCreateUxm" maxlength="40" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程创建时间" prop="instCreateTime">
            <el-date-picker v-model.trim="form.instCreateTime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程一级类型" prop="tmplType1">
            <el-input v-model.trim="form.tmplType1" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程二级类型" prop="tmplType2">
            <el-input v-model.trim="form.tmplType2" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程三级类型" prop="tmplType3">
            <el-input v-model.trim="form.tmplType3" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="流程类型" prop="tmplType">
            <el-input v-model.trim="form.tmplType" maxlength="61" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务ID,流水号" prop="taskId"  style="display:none;">
            <el-input v-model.trim="form.taskId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'viewLeswfe15TaskInstForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions();
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[];
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName});
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>